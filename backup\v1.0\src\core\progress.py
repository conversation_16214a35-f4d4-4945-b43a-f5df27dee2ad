#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度管理模块
支持批量处理的进度保存和恢复机制
"""

import json
import uuid
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

from config import CONFIG


class ProgressManager:
    """
    进度管理器
    
    支持批量处理的进度保存和恢复功能：
    - 保存处理进度到JSON文件
    - 中断后从上次停止的地方继续
    - 避免重复处理已完成的学员
    """
    
    def __init__(self, progress_file: Optional[str] = None):
        """
        初始化进度管理器
        
        Args:
            progress_file: 进度文件路径，默认为 progress.json
        """
        if progress_file is None:
            progress_file = "progress.json"
        
        self.progress_file = Path(progress_file)
        self.progress_data = self._load_progress()
        self.current_session_id = None
        
        # 确保进度文件目录存在
        self.progress_file.parent.mkdir(parents=True, exist_ok=True)
    
    def _load_progress(self) -> Dict[str, Any]:
        """
        加载进度数据
        
        Returns:
            进度数据字典
        """
        try:
            if self.progress_file.exists():
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"进度文件加载成功: {self.progress_file}")
                    return data
        except Exception as e:
            print(f"进度文件加载失败: {e}")
        
        return {}
    
    def _save_progress(self, progress_data: Dict[str, Any]) -> bool:
        """
        保存进度数据
        
        Args:
            progress_data: 进度数据字典
            
        Returns:
            是否保存成功
        """
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"进度保存失败: {e}")
            return False
    
    def start_session(self, total_students: int) -> str:
        """
        开始新的处理会话
        
        Args:
            total_students: 总学员数量
            
        Returns:
            会话ID
        """
        session_id = str(uuid.uuid4())
        self.current_session_id = session_id
        
        session_data = {
            'session_id': session_id,
            'start_time': datetime.now().isoformat(),
            'end_time': None,
            'status': 'in_progress',
            'total_students': total_students,
            'completed': [],
            'failed': [],
            'skipped': [],
            'statistics': {
                'total': total_students,
                'completed': 0,
                'failed': 0,
                'skipped': 0,
                'success_rate': 0.0
            }
        }
        
        self.progress_data = session_data
        self._save_progress(self.progress_data)
        
        print(f"新会话开始: {session_id}")
        print(f"总学员数量: {total_students}")
        
        return session_id
    
    def save_student_progress(self, student_id: str, student_name: str, 
                            status: str, error_message: Optional[str] = None) -> bool:
        """
        保存单个学员的处理进度
        
        Args:
            student_id: 学员身份证号
            student_name: 学员姓名
            status: 处理状态 ('completed', 'failed', 'skipped')
            error_message: 错误信息（可选）
            
        Returns:
            是否保存成功
        """
        if not self.progress_data:
            print("警告: 没有活动的会话，无法保存进度")
            return False
        
        student_record = {
            'id': student_id,
            'name': student_name,
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'error_message': error_message
        }
        
        # 添加到对应的状态列表
        if status == 'completed':
            self.progress_data['completed'].append(student_record)
            self.progress_data['statistics']['completed'] += 1
        elif status == 'failed':
            self.progress_data['failed'].append(student_record)
            self.progress_data['statistics']['failed'] += 1
        elif status == 'skipped':
            self.progress_data['skipped'].append(student_record)
            self.progress_data['statistics']['skipped'] += 1
        
        # 更新成功率
        total_processed = (self.progress_data['statistics']['completed'] + 
                          self.progress_data['statistics']['failed'] + 
                          self.progress_data['statistics']['skipped'])
        
        if total_processed > 0:
            success_rate = (self.progress_data['statistics']['completed'] / total_processed) * 100
            self.progress_data['statistics']['success_rate'] = round(success_rate, 2)
        
        return self._save_progress(self.progress_data)
    
    def end_session(self) -> bool:
        """
        结束当前会话
        
        Returns:
            是否成功结束
        """
        if not self.progress_data:
            print("警告: 没有活动的会话")
            return False
        
        self.progress_data['end_time'] = datetime.now().isoformat()
        self.progress_data['status'] = 'completed'
        
        success = self._save_progress(self.progress_data)
        
        if success:
            print(f"会话结束: {self.current_session_id}")
            print(f"处理统计: {self.progress_data['statistics']}")
        
        return success
    
    def get_processed_student_ids(self) -> Tuple[set, set, set]:
        """
        获取已处理的学员ID集合
        
        Returns:
            (已完成ID集合, 已失败ID集合, 已跳过ID集合)
        """
        if not self.progress_data:
            return set(), set(), set()
        
        completed_ids = {record['id'] for record in self.progress_data.get('completed', [])}
        failed_ids = {record['id'] for record in self.progress_data.get('failed', [])}
        skipped_ids = {record['id'] for record in self.progress_data.get('skipped', [])}
        
        return completed_ids, failed_ids, skipped_ids
    
    def filter_remaining_students(self, all_students: List[Dict]) -> List[Dict]:
        """
        过滤出尚未处理的学员
        
        Args:
            all_students: 所有学员列表
            
        Returns:
            尚未处理的学员列表
        """
        if not self.progress_data:
            return all_students
        
        completed_ids, failed_ids, skipped_ids = self.get_processed_student_ids()
        processed_ids = completed_ids | failed_ids | skipped_ids
        
        remaining_students = [
            student for student in all_students 
            if student.get('身份证号') not in processed_ids
        ]
        
        if len(remaining_students) < len(all_students):
            processed_count = len(all_students) - len(remaining_students)
            print(f"发现已处理的学员: {processed_count} 名")
            print(f"剩余待处理学员: {len(remaining_students)} 名")
        
        return remaining_students
    
    def has_unfinished_session(self) -> bool:
        """
        检查是否有未完成的会话
        
        Returns:
            是否有未完成的会话
        """
        return (self.progress_data and 
                self.progress_data.get('status') == 'in_progress')
    
    def get_session_info(self) -> Optional[Dict[str, Any]]:
        """
        获取当前会话信息
        
        Returns:
            会话信息字典
        """
        return self.progress_data.copy() if self.progress_data else None
    
    def clear_progress(self) -> bool:
        """
        清除进度数据
        
        Returns:
            是否清除成功
        """
        try:
            if self.progress_file.exists():
                self.progress_file.unlink()
            self.progress_data = {}
            self.current_session_id = None
            print("进度数据已清除")
            return True
        except Exception as e:
            print(f"清除进度数据失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Returns:
            统计信息字典
        """
        if not self.progress_data:
            return {
                'total': 0,
                'completed': 0,
                'failed': 0,
                'skipped': 0,
                'success_rate': 0.0
            }
        
        return self.progress_data.get('statistics', {}).copy()
