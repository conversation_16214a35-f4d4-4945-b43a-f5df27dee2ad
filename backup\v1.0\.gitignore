# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 项目特定文件
output/logs/*.log
output/logs/*.json
output/logs/captcha_debug/*.png
output/screenshots/*.png
output/screenshots/*.jpg
output/screenshots/*.html

# 配置文件（包含敏感信息）
config.local.yaml
config.local.json
.env.local

# 临时文件
*.tmp
*.temp
progress.json

# 测试覆盖率
.coverage
htmlcov/
.pytest_cache/

# 系统文件
.DS_Store
Thumbs.db
desktop.ini
