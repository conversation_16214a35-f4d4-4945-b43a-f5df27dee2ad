#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自考成绩查询自动化系统安装配置
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "docs" / "README.md"
long_description = ""
if readme_file.exists():
    with open(readme_file, "r", encoding="utf-8") as f:
        long_description = f.read()

setup(
    name="exam-score-query",
    version="1.0.0",
    description="自考成绩查询自动化系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="自考成绩查询项目组",
    author_email="",
    url="",
    
    # 包配置
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.8",
    
    # 依赖配置
    install_requires=[
        "playwright>=1.40.0",
        "loguru>=0.7.0", 
        "ddddocr>=1.4.0",
        "pyyaml>=6.0.0",
        "python-dotenv>=1.0.0",
    ],
    
    # 可选依赖
    extras_require={
        "excel": ["pandas>=2.0.0", "openpyxl>=3.0.0"],
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0", 
            "black>=23.0.0",
            "isort>=5.0.0",
        ],
    },
    
    # 入口点
    entry_points={
        "console_scripts": [
            "exam-query=main:main",
        ],
    },
    
    # 分类信息
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Education",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
    ],
    
    # 包含数据文件
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.yaml", "*.yml"],
    },
)
