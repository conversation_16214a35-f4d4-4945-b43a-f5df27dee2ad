<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml"><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>福建省高等教育自学考试</title>
<script language="javascript" src="/framework/pjs/jquery.min.20120321.js"></script>
<style type="text/css">
<!--
body {
	font-family: "宋体";
	font-size: 12px;
	color: #494949;
	text-align: center;
	margin: 0px;
	padding: 0px;
	background-color: #ffffff;
	line-height: 18px;
}

div, form, img, ul, ol, li, dl, dt, dd {
	padding: 0;
	border: 0;
	margin-top: 0;
	margin-right: 0;
	margin-bottom: 0;
	margin-left: 0;
}

li {
	list-style-type: none;
}

ol, ul {
	list-style: none;
}

a:link {
	text-decoration: none;
	color: #000000;
}

a:visited {
	text-decoration: none;
	color: #000000;
}

a:hover {
	text-decoration: underline;
	color: #990000;
}

a:active {
	text-decoration: none;
	color: #000000;
}

.public-table {
	border: 0;
	margin: 0;
	border-collapse: collapse;
	border-spacing: 0;
}
/*cellspacing*/
.public-table td {
	padding: 0;
}
/*cellpadding*/
/*border="0" cellspacing="0" cellpadding="0"*/
.top_img {
	background: url(/framework/pimg/fjzk/fjzk960.jpg) -0px -0px repeat;
	height: 112px;
}

.bmbox_title {
	width: 960px;
	height: 3px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
	background-color: #dfefff;
	background: url(/framework/pimg/fjzk/member_bg.jpg) -0px -0px repeat;
}

.bmbox_bottom {
	width: 960px;
	height: 3px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
	background-color: #dfefff;
}

.bmbox_mod {
	background-color: #dfefff;
	color: #000000;
	width: 960px;
	font-size: 14px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
}

.mod_next {
	color: #000000;
	width: 954px;
	font-size: 14px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
}

.menu1 {
	margin-top: 0px;
	margin-bottom: 0px;
	height: 44px;
	overflow: hidden;
	background: url(/framework/pimg/fjzk/fjzk960.jpg) -0px -112px repeat;
}

.menubutton {
	height: 30px;
	overflow: hidden;
}

.menu_index {
	margin-top: 0px;
	margin-bottom: 0px;
	text-align: center;
	width: 90px;
	height: 30px;
	overflow: hidden;
	background: url(/framework/pimg/fjzk/menu1.gif) -6px -8px repeat;
	border-right: #07467c 1px solid;
}

.menuselect {
	margin-top: 0px;
	margin-bottom: 0px;
	text-align: center;
	padding-left: 10px;
	padding-right: 10px;
	height: 30px;
	overflow: hidden;
	background: url(/framework/pimg/fjzk/menu1.gif) -6px -98px repeat;
	border-right: #07467c 1px solid;
}

.ML1 { /*background:url('/framework/pimg/b_1.gif');*/
	width: 6px;
	height: 30px;
	overflow: hidden;
}

.MR1 { /*background:url('/framework/pimg/b_3.gif');*/
	width: 6px;
	height: 30px;
	overflow: hidden;
}

.menu1 a:link {
	text-decoration: none;
	color: #ffffff;
}

.menu1 a:visited {
	text-decoration: none;
	color: #ffffff;
}

.menu1 a:hover {
	text-decoration: underline;
	color: #990000;
}

.menu1 a:active {
	text-decoration: none;
	color: #ffffff;
}

.leftmenu {
	float: left;
	background-color: #ffffff;
	color: #000000;
	width: 168px;
	font-size: 14px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
	border: 1px #b4d2ea solid;
	border-bottom: 1px #c7dfd6 solid;
}

.rightmain {
	float: right;
	background-color: #ffffff;
	color: #000000;
	width: 780px;
	font-size: 14px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
	border: 1px #c7dfd6 solid;
	border-bottom: 1px #c7dfd6 solid;
}

.rightmain_dh {
	background: url(/framework/pimg/fjzk/member_dh.jpg) -1px 0px repeat;
	height: 65px;
	font-size: 12px;
	color: #5b89aa;
	text-align: left;
}

.editmain {
	background-color: #ffffff;
	color: #000000;
	width: 946px;
	font-size: 14px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
	border: 1px #c7dfd6 solid;
	border-bottom: 1px #c7dfd6 solid;
}

.box2 {
	width: 193px;
	height: 398px;
	background: url(/framework/pimg/stmenu.png) -2px -0px no-repeat;
}

.box2 .box_title2 {
	padding-top: 20px;
	font-size: 14px;
	font-weight: bold;
}

.box2 .box_title2 {
	padding-top: 20px;
	font-size: 14px;
	font-weight: bold;
}

.box2 ul {
	margin-top: 12px;
}

.box2 li {
	margin: 3px;
	width: 170px;
	height: 33px;
	line-height: 33px;
	border: 0px #000 solid;
	background: url(/framework/pimg/stmenu.png) -12px -433px no-repeat;
}

.box2 .now2 {
	background: url(/framework/pimg/stmenu.png) -12px -398px no-repeat;
}

.box2 .now2 {
	font-size: 12px;
}

.box2 .now1 {
	font-size: 12px;
}
-->
</style>
<script>
function AjaxP(v,n,r,s){
	ad_countdown_button(v,n,3);
	var mypwd  =$("#mypwd").val();
	var mypwd2  =$("#mypwd2").val();

	if(mypwd.length==0){
		alert("请输入您的密码！");
		return (false);
	} 
	if(mypwd.length>=20 || mypwd.length<6){
		alert("密码由大小写字母和数字组成，长度不少于8位！");
		return (false);
	} 
	if(mypwd2.length==0){
		alert("请输入确认密码！");
		return (false);
	} 
	if(mypwd2!=mypwd2){
		alert("确认密码与密码不同！");
		return (false);
	} 
	$.ajax({type:"post",url:"../../ajaxs/2u/?p=uAjaxYonghuUser&a=action",data:{r:r,s:s,mypwd:mypwd,mypwd2:mypwd2},cache:false,async:false,timeout:5,dataType:"json",success:function(result){ 
			//alert(result);
			if(result!=null){
				if(result.errcode=="20010"){
					//$("#divxxyd").html(result.html);
					alert("提示："+result.msg);
					window.location.reload();
					//location.href = result.url;
				}else{
					alert("警告："+result.msg); 
					//window.location.reload();
				}		
			}else{
				alert("警告：errcode:19 操作失败！");
			}
		},
		error:function(jqXHR){alert("警告：errCode:"+jqXHR.status+" 请求失败，请稍后再试！");}
	});
}
//-- ad_countdown_button --
function ad_countdown_button(v,a,t){
	var o=document.getElementById(v);
	if(t==0){o.removeAttribute("disabled");o.value=a;}else{o.setAttribute("disabled",true);o.value=a+"("+t+")";setTimeout(function(){ad_countdown_button(v,a,t-1);},1000);}
}
</script><style type="text/css">
<!--
/*放开*/
input.ipt-normal{ border-color:#A0B4C5; background-color:#FFF}
/*选中*/
input.ipt-focus{  border-color:#727272; background-color:#FFFBD5}
/*出错*/
input.ipt-error{  border-color:#D5060D;background-color:#FEF5F5}	

input.inp{
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
	font-size: 14px;
width:190px; height:22px; border:#838383 1px solid; padding:2px 3px; font-size:14px; line-height:20px}
.regs{ line-height:30px;}
.regs a:link    {color:#0000ff; font-weight: bold; text-decoration:none;  }  
.regs a:visited {color:#0000ff; font-weight: bold; text-decoration:none;  } 
.regs a:hover   {color:#ee0000; font-weight: bold; text-decoration:underline;  }
.regs a:active  {color:#ee0000; font-weight: bold; text-decoration:underline;  }
-->
</style><style type="text/css">
<!--
.ml_title {
	cursor: pointer;
	padding-left: 6px;
	color: #296599;
	text-align: left;
	font-weight: bold;
	font-size: 12px;
	color: #296599;
	line-height: 30px;
}

.ml_title a {
	color: #296599;
	text-align: left;
	font-size: 12px;
	color: #296599;
}

.span_list_title {
	text-align: left;
	padding-left: 20px;
}

.span_list_title a {
	padding-left: 6px;
	color: #296599;
	text-align: left;
	font-size: 12px;
	color: #296599;
}

.span_1a {
	border-bottom: #e0eef9 1px solid;
	text-align: left;
}

.span_1b {
	border-bottom: #e0eef9 1px solid;
	text-align: left;
}

.span_1a .uml_ico {
	width: 12px;
	background: url(/framework/pimg/fjzk/ico1.gif) 3px 6px repeat;
}

.span_1b .uml_ico {
	width: 12px;
	background: url(/framework/pimg/fjzk/ico1.gif) -9px 6px repeat;
}

.span_1a .span_list {
	DISPLAY: none;
}

.span_1b .span_list {
	DISPLAY: block;
}
-->
</style>

<script>
function jsmenus_onclick(o){
	var v=document.getElementById('span_'+o).className;
	if(v=="span_1a"){
		document.getElementById('span_'+o).className= 'span_1b';
	}else{
		document.getElementById('span_'+o).className= 'span_1a';
	}
}
</script>
</head>
<!--BODY oncontextmenu="return false" onselectstart="return false" ondragstart="return false" onbeforecopy="return false" oncopy="return false" oncut="return false;"-->
<body>
	<!-- top img begin -->
	<table width="960" border="0" cellspacing="0" cellpadding="0" align="center">
		<tbody><tr>
			<td><img src="/framework/online/skzy/top.jpg" width="960" height="136"></td>
		</tr>
	</tbody></table>
	<!-- top img end -->


	<div class="bmbox_title"></div>
	<div class="bmbox_mod">
		<div class="mod_next">
			<div class="leftmenu"><div style="font-size:12px;line-height:24px;"><div style="background-color:#5d99cf;color:#ffffff;line-height:30px;"><b>欢迎您:姜菲菲</b></div><div id="span_1" class="span_1a"><table border="0" cellspacing="0" cellpadding="0">	<tbody><tr>		<td class="uml_ico"></td>		<td class="ml_title">&nbsp;</td>		<td class="ml_title">&nbsp;</td>		<td class="ml_title">&nbsp;</td>		<td class="ml_title">&nbsp;</td>	</tr></tbody></table><table border="0" cellspacing="0" cellpadding="0">	<tbody><tr><td class="span_list_title">&nbsp;</td></tr>	<tr><td class="span_list_title">&nbsp;</td></tr>	<tr><td class="span_list_title">&nbsp;</td></tr>	<tr><td class="span_list_title">&nbsp;</td></tr>	<tr><td class="span_list_title">&nbsp;</td></tr>	<tr><td class="span_list_title">&nbsp;</td></tr>	<tr><td class="span_list_title">&nbsp;</td></tr>	<tr><td class="span_list_title">&nbsp;</td></tr>	<tr><td class="span_list_title">&nbsp;</td></tr>	<tr><td class="span_list_title">&nbsp;</td></tr>	<tr><td class="span_list_title">&nbsp;</td></tr></tbody></table></div></div></div>
			<div class="rightmain">
				<div class="rightmain_dh">
					<div style="line-height: 33px; padding-left: 10px;">您当前的位置：个人报考平台 &gt; 注册信息</div>
					<div style="line-height: 36px; padding-left: 30px; font-weight: bold;">用户密码修改</div>
				</div>
				<br>
						<br>
<div style="margin:auto; width:700px; text-align:center;font-size:12px;"><b>提示：您的密码强度过低或密码已过期，请修改您的登录密码！</b></div>
<br>
<div style="margin:auto; width:700px; text-align:left;font-size:12px;"><b>修改登录密码:</b></div>

	<table border="0" cellpadding="0" cellspacing="0" style="border-collapse: collapse;font-size:12px;border:1px #000000 solid;" bordercolor="#BFBFBF" width="700" align="center">
	<tbody><tr>
		<td align="right" height="40">输入新密码:</td>
		<td align="left"><input class="inp ipt-focus" onfocus="this.className='inp ipt-focus'" onblur="this.className='inp ipt-normal'" style="width:160px;" id="mypwd" name="mypwd" type="password" autocomplete="off"></td>
		<td align="left"><span>*</span> 必填，密码由大小写字母和数字组成，长度不少于8位！</td>
	</tr>
	<tr>
		<td align="right" height="40">再次输入密码:</td>
		<td align="left"><input class="inp ipt-normal" onfocus="this.className='inp ipt-focus'" onblur="this.className='inp ipt-normal'" style="width:160px;" id="mypwd2" name="mypwd2" type="password" autocomplete="off"></td>
		<td align="left"><span>*</span> 必填，请再次输入密码</td>
	</tr>
	</tbody></table>
<br>
<input id="btnsubmit" type="button" value="修改登录密码" onclick="return AjaxP('btnsubmit','修改登录密码','1-WEB83-455-2b0-1173465-1753948856-U','1-yonghu_u_grpwd_edit-536-62f---1173465')">
<br><br>



			<br> <br>
			</div>
		</div>
	</div>
	<div class="bmbox_bottom"></div>
	<!--底部-->
	<div style="margin-top:0px;text-align:center;margin:0 auto;width:960px;background-color:#ADD3DB;">
		<div style="line-height:1.5;text-align:center;padding-top:10px;padding-bottom:10px;font-size:12px;text-align:center;font-family:Arial;">© 2024 福建省高等教育自学考试考务考籍管理信息系统</div>
	</div>

</body></html>