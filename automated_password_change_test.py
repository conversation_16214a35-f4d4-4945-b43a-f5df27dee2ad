#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化密码修改功能测试程序
实现从登录到密码修改的端到端自动化测试流程
验证密码修改功能的修复效果，确保选择器配置正确且功能正常工作
"""

import sys
import time
import yaml
import argparse
import random
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from playwright.sync_api import Page, sync_playwright

# 添加src目录到Python路径，以便导入现有模块
sys.path.append(str(Path(__file__).parent / "src"))

# 导入现有模块
try:
    from modules.data import load_students_data
    from modules.login import LoginManager
    from modules.browser import BrowserManager
    from modules.password_change import PasswordChangeHandler
    from modules.captcha import CaptchaRecognizer
    from modules.logger import log_info, log_success, log_error, log_warning, log_debug
    from config import CONFIG, SELECTORS
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入现有模块: {e}")
    MODULES_AVAILABLE = False

# 备用配置加载函数
def load_config_fallback():
    """备用配置加载函数"""
    config_path = Path(__file__).parent / "config.yaml"
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"配置文件加载失败: {e}")
        return {}

# 备用日志函数
def log_info_fallback(message, module="测试"):
    """备用信息日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [INFO] [{module}] {message}")

def log_success_fallback(message, module="测试"):
    """备用成功日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [SUCCESS] [{module}] ✅ {message}")

def log_error_fallback(message, module="测试"):
    """备用错误日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [ERROR] [{module}] ❌ {message}")

def log_warning_fallback(message, module="测试"):
    """备用警告日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [WARNING] [{module}] ⚠️ {message}")

# 根据模块可用性选择日志函数
if MODULES_AVAILABLE:
    log_test_info = log_info
    log_test_success = log_success
    log_test_error = log_error
    log_test_warning = log_warning
    CONFIG_DATA = CONFIG
    SELECTORS_DATA = SELECTORS
else:
    log_test_info = log_info_fallback
    log_test_success = log_success_fallback
    log_test_error = log_error_fallback
    log_test_warning = log_warning_fallback
    CONFIG_DATA = load_config_fallback()
    SELECTORS_DATA = CONFIG_DATA.get('selectors', {})


class AutomatedPasswordChangeTest:
    """自动化密码修改功能测试类"""
    
    def __init__(self):
        """初始化测试程序"""
        log_test_info("初始化自动化密码修改测试程序", "测试框架")
        
        # 加载配置
        self.config = CONFIG_DATA
        self.selectors = SELECTORS_DATA
        
        # 初始化组件
        self.browser_manager = None
        self.login_manager = None
        self.password_change_handler = None
        self.test_users = []
        
        # 测试统计信息
        self.statistics = {
            'start_time': None,
            'end_time': None,
            'total_tests': 0,
            'successful_tests': 0,
            'failed_tests': 0,
            'test_results': [],
            'performance_metrics': {
                'average_login_time': 0.0,
                'average_password_change_time': 0.0,
                'total_execution_time': 0.0
            },
            'error_summary': {},
            'selector_validation_results': {}
        }
        
        log_test_success("测试程序初始化完成", "测试框架")
    
    def setup_logging(self):
        """配置测试专用日志"""
        log_test_info("配置测试日志系统", "测试框架")
        
        # 创建日志目录
        log_dir = Path("output/logs/test")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 记录测试开始时间
        self.statistics['start_time'] = datetime.now()
        
        log_test_success("测试日志系统配置完成", "测试框架")
    
    def validate_environment(self) -> bool:
        """验证测试环境"""
        log_test_info("验证测试环境", "测试框架")
        
        try:
            # 检查配置文件
            if not self.config:
                log_test_error("配置文件加载失败", "测试框架")
                return False
            
            # 检查必要的配置项
            # CONFIG是CompatibleConfigDict，直接包含配置项
            required_configs = ['login_url', 'headless', 'slow_mo']
            for config_key in required_configs:
                if config_key not in self.config:
                    log_test_warning(f"缺少配置项: {config_key}", "测试框架")
                else:
                    log_test_info(f"配置项检查通过: {config_key} = {self.config[config_key]}", "测试框架")
            
            # 检查选择器配置
            required_selectors = ['new_password_input', 'confirm_password_input', 'change_password_button']
            for selector_key in required_selectors:
                if selector_key not in self.selectors:
                    log_test_warning(f"缺少选择器配置: {selector_key}", "测试框架")
                else:
                    selector_count = len(self.selectors[selector_key])
                    log_test_info(f"选择器配置检查通过: {selector_key} ({selector_count}个选择器)", "测试框架")
            
            # 检查数据文件
            data_file = Path(self.config.get('data_file', 'data/students.csv'))
            if not data_file.exists():
                log_test_error(f"数据文件不存在: {data_file}", "测试框架")
                return False
            else:
                log_test_info(f"数据文件检查通过: {data_file}", "测试框架")
            
            log_test_success("测试环境验证通过", "测试框架")
            return True
            
        except Exception as e:
            log_test_error(f"环境验证异常: {str(e)}", "测试框架")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取测试统计信息"""
        return self.statistics.copy()
    
    def reset_statistics(self):
        """重置测试统计信息"""
        log_test_info("重置测试统计信息", "测试框架")
        
        self.statistics = {
            'start_time': None,
            'end_time': None,
            'total_tests': 0,
            'successful_tests': 0,
            'failed_tests': 0,
            'test_results': [],
            'performance_metrics': {
                'average_login_time': 0.0,
                'average_password_change_time': 0.0,
                'total_execution_time': 0.0
            },
            'error_summary': {},
            'selector_validation_results': {}
        }
        
        log_test_success("测试统计信息已重置", "测试框架")

    def load_test_users(self) -> bool:
        """加载测试用户数据"""
        log_test_info("开始加载测试用户数据", "数据管理")

        try:
            # 获取数据文件路径
            data_file = self.config.get('data_file', 'data/students.csv')
            log_test_info(f"数据文件路径: {data_file}", "数据管理")

            # 使用现有的load_students_data函数
            if MODULES_AVAILABLE:
                users = load_students_data(data_file)
            else:
                # 备用数据加载方法
                users = self._load_users_fallback(data_file)

            if not users:
                log_test_error("未能加载任何用户数据", "数据管理")
                return False

            # 验证用户数据
            validated_users = self._validate_users(users)

            if not validated_users:
                log_test_error("没有有效的用户数据", "数据管理")
                return False

            self.test_users = validated_users
            log_test_success(f"成功加载 {len(self.test_users)} 个测试用户", "数据管理")

            # 记录用户信息摘要
            self._log_users_summary()

            return True

        except Exception as e:
            log_test_error(f"加载测试用户数据异常: {str(e)}", "数据管理")
            return False

    def _load_users_fallback(self, data_file: str) -> List[Dict]:
        """备用数据加载方法"""
        log_test_info("使用备用方法加载数据", "数据管理")

        try:
            import csv
            users = []
            file_path = Path(data_file)

            if not file_path.exists():
                log_test_error(f"数据文件不存在: {file_path}", "数据管理")
                return []

            with open(file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                users = list(reader)

            log_test_info(f"备用方法读取到 {len(users)} 条记录", "数据管理")
            return users

        except Exception as e:
            log_test_error(f"备用数据加载失败: {str(e)}", "数据管理")
            return []

    def _validate_users(self, users: List[Dict]) -> List[Dict]:
        """验证用户数据"""
        log_test_info("开始验证用户数据", "数据管理")

        validated_users = []
        required_fields = ["姓名", "身份证号", "密码"]

        for i, user in enumerate(users, 1):
            try:
                # 检查必要字段
                missing_fields = []
                for field in required_fields:
                    if field not in user or not str(user[field]).strip():
                        missing_fields.append(field)

                if missing_fields:
                    log_test_warning(f"第{i}个用户缺少字段: {missing_fields}", "数据管理")
                    continue

                # 验证身份证号格式
                id_card = str(user["身份证号"]).strip()
                if not self._validate_id_card(id_card):
                    log_test_warning(f"第{i}个用户身份证号格式错误: {id_card}", "数据管理")
                    continue

                # 清理数据
                cleaned_user = {
                    "姓名": str(user["姓名"]).strip(),
                    "身份证号": id_card,
                    "密码": str(user["密码"]).strip(),
                    "index": i - 1  # 添加索引便于选择
                }

                validated_users.append(cleaned_user)
                log_test_info(f"用户验证通过: {cleaned_user['姓名']} ({cleaned_user['身份证号']})", "数据管理")

            except Exception as e:
                log_test_warning(f"第{i}个用户数据处理失败: {str(e)}", "数据管理")
                continue

        log_test_success(f"用户数据验证完成，有效用户: {len(validated_users)}", "数据管理")
        return validated_users

    def _validate_id_card(self, id_card: str) -> bool:
        """验证身份证号格式"""
        if not id_card:
            return False

        # 基本长度检查
        if len(id_card) not in [15, 18]:
            return False

        # 18位身份证号检查
        if len(id_card) == 18:
            # 前17位必须是数字
            if not id_card[:17].isdigit():
                return False
            # 最后一位可以是数字或X
            if not (id_card[17].isdigit() or id_card[17].upper() == 'X'):
                return False
        # 15位身份证号检查
        elif len(id_card) == 15:
            if not id_card.isdigit():
                return False

        return True

    def _log_users_summary(self):
        """记录用户信息摘要"""
        if not self.test_users:
            return

        log_test_info("=== 测试用户信息摘要 ===", "数据管理")
        for user in self.test_users:
            masked_id = user['身份证号'][:6] + "****" + user['身份证号'][-4:]
            log_test_info(f"用户 {user['index']}: {user['姓名']} ({masked_id})", "数据管理")
        log_test_info("=====================", "数据管理")


def main():
    """主程序入口"""
    print("🧪 自动化密码修改功能测试程序")
    print("=" * 60)
    
    try:
        # 创建测试实例
        test = AutomatedPasswordChangeTest()
        
        # 配置日志
        test.setup_logging()
        
        # 验证环境
        if not test.validate_environment():
            log_test_error("环境验证失败，程序退出", "主程序")
            return False
        
        log_test_success("测试程序框架初始化完成", "主程序")
        log_test_info("后续功能模块将在下一阶段开发", "主程序")
        
        return True
        
    except KeyboardInterrupt:
        log_test_warning("用户中断程序执行", "主程序")
        return False
    except Exception as e:
        log_test_error(f"程序执行异常: {str(e)}", "主程序")
        return False


if __name__ == "__main__":
    success = main()
    exit_code = 0 if success else 1
    print(f"\n程序执行完成，退出码: {exit_code}")
    sys.exit(exit_code)
