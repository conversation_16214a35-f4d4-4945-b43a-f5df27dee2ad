# 进度管理系统使用说明

## 📋 概述

进度管理系统为自考成绩查询自动化项目提供了强大的进度保存和恢复功能，确保在处理中断时能够从上次停止的地方继续，避免重复处理已完成的学员。

## 🚀 主要功能

### 1. 自动进度保存
- 每处理完一个学员，自动保存进度到 `progress.json` 文件
- 记录已完成、失败、跳过的学员信息
- 实时更新处理统计信息

### 2. 智能恢复机制
- 程序启动时自动检测未完成的处理会话
- 提示用户是否从上次中断的地方继续
- 自动过滤已处理的学员，只处理剩余学员

### 3. 详细统计信息
- 实时显示处理进度和成功率
- 记录每个学员的处理状态和时间戳
- 提供完整的会话统计报告

## 💡 使用方法

### 正常使用
进度管理系统已完全集成到主程序中，无需额外配置：

```bash
python src/main.py
```

### 中断恢复
当程序因任何原因中断后，重新运行程序：

```bash
python src/main.py
```

系统会自动检测到未完成的进度并询问：
```
🔄 发现未完成的处理进度
是否从上次中断的地方继续处理？(y/n): 
```

- 输入 `y`：从上次中断的地方继续处理
- 输入 `n`：清除旧进度，重新开始处理

### 手动清理进度
如果需要手动清理进度数据：

```python
from modules.logger import get_logger_manager

logger_manager = get_logger_manager()
logger_manager.clear_progress()
```

## 📁 文件结构

```
src/
├── core/
│   ├── __init__.py          # 核心模块包
│   └── progress.py          # ProgressManager类实现
├── modules/
│   └── logger.py            # 扩展的LoggerManager（集成进度管理）
└── main.py                  # 主程序（集成进度管理功能）

progress.json                # 进度数据文件（运行时创建）
```

## 🔧 技术实现

### ProgressManager类
位于 `src/core/progress.py`，提供核心进度管理功能：

- `start_session(total_students)` - 开始新的处理会话
- `save_student_progress(id, name, status, error)` - 保存学员处理进度
- `filter_remaining_students(all_students)` - 过滤已处理学员
- `end_session()` - 结束处理会话
- `clear_progress()` - 清除进度数据

### LoggerManager集成
扩展了现有的LoggerManager类，添加进度管理功能：

- `start_progress_session()` - 开始进度管理会话
- `save_student_progress()` - 保存学员进度
- `filter_remaining_students()` - 过滤学员列表
- `has_unfinished_progress()` - 检查未完成进度
- `get_progress_statistics()` - 获取统计信息

### 主程序集成
在 `src/main.py` 的 `AutoQueryManager` 中集成：

- 启动时检查未完成进度
- 处理每个学员后自动保存进度
- 结束时完成进度会话

## 📊 进度文件格式

`progress.json` 文件包含以下信息：

```json
{
  "session_id": "uuid-string",
  "start_time": "2024-01-01T10:00:00",
  "end_time": null,
  "status": "in_progress",
  "total_students": 10,
  "completed": [
    {
      "id": "350122199510201430",
      "name": "张三",
      "status": "completed",
      "timestamp": "2024-01-01T10:05:00"
    }
  ],
  "failed": [
    {
      "id": "350425198804062414", 
      "name": "李四",
      "status": "failed",
      "timestamp": "2024-01-01T10:10:00",
      "error_message": "登录失败"
    }
  ],
  "skipped": [],
  "statistics": {
    "total": 10,
    "completed": 1,
    "failed": 1,
    "skipped": 0,
    "success_rate": 50.0
  }
}
```

## ⚠️ 注意事项

1. **进度文件位置**：默认在项目根目录下的 `progress.json`
2. **数据安全**：进度文件包含学员身份信息，请妥善保管
3. **并发处理**：不支持多个程序实例同时运行
4. **文件权限**：确保程序有读写进度文件的权限

## 🔍 故障排除

### 进度文件损坏
如果进度文件损坏，程序会自动忽略并重新开始：
```
进度文件加载失败: JSON decode error
```

### 权限问题
如果无法写入进度文件：
```
进度保存失败: Permission denied
```
请检查文件权限或运行目录的写入权限。

### 内存不足
处理大量学员时，如果内存不足：
- 进度管理系统会确保已处理的数据不会丢失
- 重启程序后可以继续处理

## 📈 性能优化

- 进度数据使用JSON格式，读写效率高
- 只在学员处理完成后保存进度，减少I/O操作
- 内存中维护进度状态，避免频繁文件读取

## 🎯 最佳实践

1. **定期备份**：重要批次处理前备份进度文件
2. **监控日志**：关注进度管理相关的日志信息
3. **测试恢复**：在正式使用前测试中断恢复功能
4. **清理数据**：完成批次处理后及时清理进度数据
