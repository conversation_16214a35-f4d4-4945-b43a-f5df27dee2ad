#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自考成绩查询自动化系统配置文件
现在使用新的ConfigManager系统，支持配置文件和环境变量
"""

from pathlib import Path

# 导入新的配置管理系统
from config import CONFIG, SELECTORS, get_config_manager, update_config, reload_config

# ============================================================================
# 配置使用说明
# ============================================================================

# 1. 默认配置
#    所有默认配置在 config/defaults.py 中定义
#    包括网站URL、浏览器设置、重试配置、目录配置等

# 2. 配置文件覆盖
#    您可以创建以下配置文件来覆盖默认配置：
#    - config.yaml (推荐)
#    - config.json
#    - config.yml
#
#    配置文件示例请参考项目根目录的 config.yaml

# 3. 环境变量覆盖（最高优先级）
#    您可以通过环境变量覆盖任何配置项：
#
#    EXAM_HEADLESS=true          # 无头模式
#    EXAM_MAX_RETRIES=5          # 最大重试次数
#    EXAM_LOG_LEVEL=DEBUG        # 日志级别
#    EXAM_SCREENSHOT_DIR=/path   # 截图目录
#
#    更多环境变量请参考 config/defaults.py 中的 ENV_MAPPINGS

# 4. 程序中使用配置
#    # 原有方式（向后兼容）
#    from config import CONFIG
#    url = CONFIG["login_url"]
#
#    # 新方式
#    from config import get_config_manager
#    manager = get_config_manager()
#    url = manager.get("login_url")
#
#    # 动态更新配置
#    from config import update_config
#    update_config({"headless": True})

# ============================================================================
# 实用函数
# ============================================================================

def get_config(key, default=None):
    """
    获取配置值的便捷函数

    Args:
        key: 配置键名
        default: 默认值

    Returns:
        配置值
    """
    return CONFIG.get(key, default)

def get_selector(key, default=None):
    """
    获取选择器配置的便捷函数

    Args:
        key: 选择器键名
        default: 默认值

    Returns:
        选择器列表
    """
    if default is None:
        default = []
    return SELECTORS.get(key, default)

def is_headless():
    """检查是否为无头模式"""
    return CONFIG.get("headless", False)

def get_timeout(timeout_type="page"):
    """
    获取超时配置

    Args:
        timeout_type: 超时类型 ("page", "navigation", "element")

    Returns:
        超时时间（毫秒）
    """
    timeout_key = f"{timeout_type}_timeout"
    return CONFIG.get(timeout_key, 30000)

def get_retry_config():
    """
    获取重试配置

    Returns:
        重试配置字典
    """
    return {
        "max_retries": CONFIG.get("max_retries", 3),
        "retry_delay": CONFIG.get("retry_delay", 2.0),
        "captcha_retries": CONFIG.get("captcha_retries", 3)
    }

# ============================================================================
# 目录初始化
# ============================================================================

def ensure_directories():
    """确保必要目录存在"""
    try:
        screenshot_dir = Path(CONFIG.get("screenshot_dir", "../output/screenshots"))
        log_dir = Path(CONFIG.get("log_dir", "../output/logs"))
        captcha_save_path = Path(CONFIG.get("captcha_save_path", "../output/logs/captcha_debug"))

        screenshot_dir.mkdir(parents=True, exist_ok=True)
        log_dir.mkdir(parents=True, exist_ok=True)
        captcha_save_path.mkdir(parents=True, exist_ok=True)

        print(f"目录初始化完成:")
        print(f"  截图目录: {screenshot_dir}")
        print(f"  日志目录: {log_dir}")
        print(f"  验证码调试目录: {captcha_save_path}")
    except Exception as e:
        print(f"目录初始化失败: {e}")

# 初始化时创建目录
ensure_directories()
        "input[name='username']",
        "input[name='user']",
        "input[name='account']",
        "#username",
        "#user",
        "#account"
    ],
    
    "password_input": [
        "input[name='mypwd']",
        "#mypwd",
        "input[name='password']",
        "input[name='pwd']",
        "input[name='pass']",
        "#password",
        "#pwd",
        "#pass"
    ],
    
    "captcha_input": [
        "input[name='verifycode']",
        "#verifycode",
        "input[name='captcha']",
        "input[name='code']",
        "input[name='verifyCode']",
        "input[name='validateCode']",
        "input[name='checkCode']",
        "#captcha",
        "#code",
        "#verifyCode",
        "#validateCode",
        "#checkCode"
    ],
    
    "captcha_image": [
        "#seccodeimg",
        "img[src*='verify']",
        "img[src*='captcha']",
        "img[src*='code']",
        "img[alt*='验证码']",
        "img[title*='验证码']",
        ".captcha img",
        ".code img",
        ".verify img"
    ],
    
    "login_button": [
        "#loginBtn",
        "input[type='submit']",
        "button[type='submit']",
        "input[value*='登录']",
        "input[value*='登陆']",
        "button:has-text('登录')",
        "button:has-text('登陆')",
        ".login-btn",
        "#login",
        "#submit"
    ],
    
    # 成绩查询页面选择器
    "score_query_menu": [
        "text=当次成绩查询",
        "//a[contains(text(), '当次成绩查询')]",
        "//td[contains(text(), '当次成绩查询')]",
        "//span[contains(text(), '当次成绩查询')]",
        "[title*='当次成绩查询']",
        "[alt*='当次成绩查询']"
    ],
    
    "query_options": [
        "text=点击进入",
        "//a[contains(text(), '点击进入')]",
        "//button[contains(text(), '点击进入')]",
        "//input[contains(@value, '点击进入')]",
        "[title*='点击进入']",
        "[alt*='点击进入']"
    ],
    
    # 成绩表格选择器
    "score_table": [
        "//table[.//td[contains(text(), '成绩')] or .//th[contains(text(), '成绩')]]",
        "//table[.//td[contains(text(), '科目')] or .//th[contains(text(), '科目')]]",
        "//table[.//td[contains(text(), '分数')] or .//th[contains(text(), '分数')]]",
        "//table[.//td[contains(text(), '合格')] or .//th[contains(text(), '合格')]]",
        "table",
        ".score-table",
        ".grade-table",
        "#scoreTable",
        "#gradeTable"
    ]
}

# 错误信息配置
ERROR_MESSAGES = {
    "login_failed": "登录失败，请检查用户名和密码",
    "captcha_failed": "验证码识别失败，请重试",
    "network_error": "网络连接错误，请检查网络",
    "page_timeout": "页面加载超时，请重试",
    "element_not_found": "页面元素未找到，可能页面结构已变化",
    "screenshot_failed": "截图保存失败，请检查目录权限",
    "data_load_failed": "数据文件加载失败，请检查文件格式"
}

# 成功信息配置
SUCCESS_MESSAGES = {
    "login_success": "登录成功",
    "navigation_success": "页面导航成功",
    "screenshot_success": "截图保存成功",
    "batch_complete": "批量处理完成"
}

def get_config():
    """获取配置"""
    return CONFIG

def get_selectors():
    """获取选择器配置"""
    return SELECTORS

def update_config(key, value):
    """更新配置"""
    CONFIG[key] = value

def ensure_directories():
    """确保必要目录存在"""
    CONFIG["screenshot_dir"].mkdir(parents=True, exist_ok=True)
    CONFIG["log_dir"].mkdir(parents=True, exist_ok=True)
    CONFIG["captcha_save_path"].mkdir(parents=True, exist_ok=True)

# 初始化时创建目录
ensure_directories()
