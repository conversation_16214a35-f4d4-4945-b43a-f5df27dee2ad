#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密码修改处理模块
实现密码修改页面的检测、处理和验证功能
"""

import time
import os
from datetime import datetime
from pathlib import Path
from typing import Tuple, Optional
from playwright.sync_api import Page

import sys
sys.path.append('..')
from config import CONFIG, SELECTORS
from modules.logger import log_info, log_success, log_error, log_warning, log_debug


class PasswordChangeHandler:
    """密码修改处理器"""

    def __init__(self):
        """初始化密码修改处理器"""
        self.max_retries = CONFIG.get('max_retries', 3)
        self.retry_delay = CONFIG.get('retry_delay', 2.0)
        self.page_timeout = CONFIG.get('page_timeout', 30000)
        self.element_timeout = CONFIG.get('element_timeout', 10000)

        # 容错机制配置
        self.max_detection_time = CONFIG.get('max_detection_time', 30)  # 最大检测时间(秒)
        self.max_detection_attempts = CONFIG.get('max_detection_attempts', 3)  # 最大检测尝试次数
        self.max_change_time = CONFIG.get('max_change_time', 60)  # 最大密码修改时间(秒)
        self.auto_skip_on_failure = CONFIG.get('auto_skip_on_failure', True)  # 失败时自动跳过

        # 调试目录设置
        self.debug_dir = Path("output/logs/debug")
        self.debug_dir.mkdir(parents=True, exist_ok=True)

        # 统计信息
        self.statistics = {
            'detection_attempts': 0,
            'detection_success': 0,
            'change_attempts': 0,
            'change_success': 0,
            'total_retries': 0,
            'average_detection_time': 0.0,
            'average_change_time': 0.0,
            'detection_timeouts': 0,  # 检测超时次数
            'change_timeouts': 0,     # 修改超时次数
            'auto_skips': 0,          # 自动跳过次数
            'failure_recoveries': 0   # 失败恢复次数
        }

        log_debug("密码修改处理器初始化完成，统计信息已重置", "密码修改")

    def _is_timeout(self, start_time: float, max_time: float, operation_name: str) -> bool:
        """
        检查操作是否超时

        Args:
            start_time: 开始时间
            max_time: 最大允许时间(秒)
            operation_name: 操作名称

        Returns:
            是否超时
        """
        elapsed_time = time.time() - start_time
        is_timeout = elapsed_time > max_time

        if is_timeout:
            log_warning(f"{operation_name}操作超时: {elapsed_time:.2f}秒 > {max_time}秒", "密码修改")
        else:
            log_debug(f"{operation_name}操作耗时: {elapsed_time:.2f}秒 / {max_time}秒", "密码修改")

        return is_timeout

    def _should_auto_skip(self, failure_count: int, operation_name: str) -> bool:
        """
        判断是否应该自动跳过

        Args:
            failure_count: 失败次数
            operation_name: 操作名称

        Returns:
            是否应该跳过
        """
        if not self.auto_skip_on_failure:
            return False

        should_skip = failure_count >= self.max_detection_attempts

        if should_skip:
            log_warning(f"{operation_name}失败次数达到阈值({failure_count}/{self.max_detection_attempts})，启动自动跳过", "密码修改")
            self.statistics['auto_skips'] += 1

        return should_skip

    def _save_debug_info(self, page: Page, stage: str, success: bool = True, extra_details: dict = None) -> None:
        """
        保存调试信息（HTML、截图和详细信息）

        Args:
            page: Playwright页面对象
            stage: 调试阶段标识
            success: 是否成功
            extra_details: 额外的详细信息
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
            status = "success" if success else "failed"

            # 保存HTML内容
            try:
                html_content = page.content()
                html_filename = f"password_change_{stage}_{status}_{timestamp}.html"
                html_path = self.debug_dir / html_filename

                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                log_debug(f"调试HTML已保存: {html_path}", "密码修改")
            except Exception as e:
                log_warning(f"保存调试HTML失败: {str(e)}", "密码修改")

            # 保存截图
            try:
                screenshot_filename = f"password_change_{stage}_{status}_{timestamp}.png"
                screenshot_path = self.debug_dir / screenshot_filename

                page.screenshot(path=str(screenshot_path), full_page=True)
                log_debug(f"调试截图已保存: {screenshot_path}", "密码修改")
            except Exception as e:
                log_warning(f"保存调试截图失败: {str(e)}", "密码修改")

            # 保存详细信息到JSON文件
            if extra_details:
                try:
                    import json
                    details_filename = f"password_change_{stage}_{status}_{timestamp}_details.json"
                    details_path = self.debug_dir / details_filename

                    # 准备详细信息
                    debug_details = {
                        "timestamp": timestamp,
                        "stage": stage,
                        "success": success,
                        "page_info": {
                            "url": page.url,
                            "title": page.title(),
                            "html_length": len(html_content) if 'html_content' in locals() else 0
                        },
                        "extra_details": extra_details,
                        "statistics": self.statistics.copy()
                    }

                    with open(details_path, 'w', encoding='utf-8') as f:
                        json.dump(debug_details, f, ensure_ascii=False, indent=2)

                    log_debug(f"调试详情已保存: {details_path}", "密码修改")
                except Exception as e:
                    log_warning(f"保存调试详情失败: {str(e)}", "密码修改")

            # 记录页面基本信息
            try:
                current_url = page.url
                page_title = page.title()
                log_info(f"调试信息保存完成 - 阶段: {stage}, 状态: {status}", "密码修改")
                log_debug(f"页面URL: {current_url}", "密码修改")
                log_debug(f"页面标题: {page_title}", "密码修改")
                log_debug(f"HTML长度: {len(html_content) if 'html_content' in locals() else 'N/A'} 字符", "密码修改")

                if extra_details:
                    log_debug(f"额外详情: {extra_details}", "密码修改")

            except Exception as e:
                log_warning(f"记录页面信息失败: {str(e)}", "密码修改")

        except Exception as e:
            log_error("密码修改", e)

    def _find_password_inputs(self, page: Page) -> list:
        """
        智能查找页面中的密码输入框 - 使用多策略查找机制

        Args:
            page: Playwright页面对象

        Returns:
            找到的密码输入框列表
        """
        password_inputs = []

        try:
            log_debug("开始智能查找密码输入框", "密码修改")

            # 策略1：使用配置的选择器（优先级最高）
            password_inputs.extend(self._find_by_configured_selectors(page))

            # 策略2：通用密码输入框选择器
            if len(password_inputs) < 2:
                password_inputs.extend(self._find_by_generic_selectors(page))

            # 策略3：检查iframe中的密码输入框
            if len(password_inputs) < 2:
                password_inputs.extend(self._find_in_iframes(page))

            # 策略4：基于文本内容查找
            if len(password_inputs) < 2:
                password_inputs.extend(self._find_by_text_content(page))

            # 策略5：基于位置关系查找
            if len(password_inputs) < 2:
                password_inputs.extend(self._find_by_position_relationship(page))

            # 策略6：动态等待后重新查找
            if len(password_inputs) < 2:
                password_inputs.extend(self._find_with_dynamic_wait(page))

            # 去重并排序
            unique_inputs = self._deduplicate_and_sort_inputs(password_inputs)

            log_debug(f"智能查找完成，总共找到 {len(unique_inputs)} 个唯一的密码输入框", "密码修改")
            return unique_inputs

        except Exception as e:
            log_warning(f"智能查找密码输入框时发生异常: {str(e)}", "密码修改")
            return []

    def _find_by_configured_selectors(self, page: Page) -> list:
        """
        策略1：使用配置的选择器查找密码输入框

        Args:
            page: Playwright页面对象

        Returns:
            找到的密码输入框列表
        """
        password_inputs = []

        try:
            new_password_selectors = SELECTORS.get('new_password_selectors', [])
            log_debug(f"使用配置选择器查找，共 {len(new_password_selectors)} 个选择器", "密码修改")

            for selector in new_password_selectors:
                try:
                    elements = page.query_selector_all(selector)
                    if elements:
                        password_inputs.extend(elements)
                        log_debug(f"配置选择器 '{selector}' 找到 {len(elements)} 个密码输入框", "密码修改")
                except Exception as e:
                    log_debug(f"配置选择器 '{selector}' 查找失败: {str(e)}", "密码修改")
                    continue

            if password_inputs:
                log_success(f"配置选择器策略成功，找到 {len(password_inputs)} 个密码输入框", "密码修改")
            else:
                log_debug("配置选择器策略未找到密码输入框", "密码修改")

        except Exception as e:
            log_warning(f"配置选择器策略异常: {str(e)}", "密码修改")

        return password_inputs

    def _find_by_generic_selectors(self, page: Page) -> list:
        """
        策略2：使用通用选择器查找密码输入框

        Args:
            page: Playwright页面对象

        Returns:
            找到的密码输入框列表
        """
        password_inputs = []

        try:
            log_debug("使用通用选择器查找密码输入框", "密码修改")

            # 通用密码输入框选择器
            generic_selectors = [
                'input[type="password"]',
                'input[type="PASSWORD"]',
                'input[class*="password"]',
                'input[id*="password"]',
                'input[name*="password"]',
                'input[placeholder*="密码"]',
                'input[placeholder*="password"]'
            ]

            for selector in generic_selectors:
                try:
                    elements = page.query_selector_all(selector)
                    if elements:
                        password_inputs.extend(elements)
                        log_debug(f"通用选择器 '{selector}' 找到 {len(elements)} 个密码输入框", "密码修改")
                except Exception as e:
                    log_debug(f"通用选择器 '{selector}' 查找失败: {str(e)}", "密码修改")
                    continue

            if password_inputs:
                log_success(f"通用选择器策略成功，找到 {len(password_inputs)} 个密码输入框", "密码修改")
            else:
                log_debug("通用选择器策略未找到密码输入框", "密码修改")

        except Exception as e:
            log_warning(f"通用选择器策略异常: {str(e)}", "密码修改")

        return password_inputs

    def _find_in_iframes(self, page: Page) -> list:
        """
        策略3：在iframe中查找密码输入框

        Args:
            page: Playwright页面对象

        Returns:
            找到的密码输入框列表
        """
        password_inputs = []

        try:
            log_debug("在iframe中查找密码输入框", "密码修改")

            iframes = page.query_selector_all('iframe')
            log_debug(f"找到 {len(iframes)} 个iframe", "密码修改")

            for i, iframe in enumerate(iframes):
                try:
                    frame = iframe.content_frame()
                    if frame:
                        # 在iframe中使用多种选择器
                        iframe_selectors = [
                            'input[type="password"]',
                            'input[class*="password"]',
                            'input[id*="password"]',
                            'input[name*="password"]'
                        ]

                        for selector in iframe_selectors:
                            try:
                                iframe_inputs = frame.query_selector_all(selector)
                                if iframe_inputs:
                                    password_inputs.extend(iframe_inputs)
                                    log_debug(f"iframe[{i}] 中选择器 '{selector}' 找到 {len(iframe_inputs)} 个密码输入框", "密码修改")
                            except Exception as e:
                                log_debug(f"iframe[{i}] 中选择器 '{selector}' 查找失败: {str(e)}", "密码修改")
                                continue

                except Exception as e:
                    log_debug(f"iframe[{i}] 访问失败: {str(e)}", "密码修改")
                    continue

            if password_inputs:
                log_success(f"iframe策略成功，找到 {len(password_inputs)} 个密码输入框", "密码修改")
            else:
                log_debug("iframe策略未找到密码输入框", "密码修改")

        except Exception as e:
            log_warning(f"iframe策略异常: {str(e)}", "密码修改")

        return password_inputs

    def _find_by_text_content(self, page: Page) -> list:
        """
        策略4：基于文本内容查找密码输入框

        Args:
            page: Playwright页面对象

        Returns:
            找到的密码输入框列表
        """
        password_inputs = []

        try:
            log_debug("基于文本内容查找密码输入框", "密码修改")

            # 查找包含密码相关文本的label关联的input
            password_keywords = ['新密码', '密码', '确认密码', '重复密码', '输入密码', 'password', 'Password', 'PASSWORD']

            for keyword in password_keywords:
                try:
                    # 查找包含关键词的label
                    labels = page.query_selector_all(f'label:has-text("{keyword}")')
                    for label in labels:
                        try:
                            # 获取label的for属性
                            for_attr = label.get_attribute('for')
                            if for_attr:
                                input_elem = page.query_selector(f'input#{for_attr}')
                                if input_elem and input_elem not in password_inputs:
                                    password_inputs.append(input_elem)
                                    log_debug(f"通过label文本 '{keyword}' 找到关联的密码输入框", "密码修改")

                            # 查找label内部或相邻的input
                            nearby_inputs = label.query_selector_all('input')
                            if not nearby_inputs:
                                # 查找label后面的input
                                nearby_inputs = page.evaluate('''
                                    (label) => {
                                        const inputs = [];
                                        let next = label.nextElementSibling;
                                        while (next && inputs.length < 3) {
                                            if (next.tagName === 'INPUT') {
                                                inputs.push(next);
                                                break;
                                            }
                                            const nestedInput = next.querySelector('input');
                                            if (nestedInput) {
                                                inputs.push(nestedInput);
                                                break;
                                            }
                                            next = next.nextElementSibling;
                                        }
                                        return inputs;
                                    }
                                ''', label)

                            for input_elem in nearby_inputs:
                                if input_elem and input_elem not in password_inputs:
                                    password_inputs.append(input_elem)
                                    log_debug(f"通过label文本 '{keyword}' 找到相邻的输入框", "密码修改")

                        except Exception as e:
                            log_debug(f"处理label '{keyword}' 时失败: {str(e)}", "密码修改")
                            continue

                except Exception as e:
                    log_debug(f"查找关键词 '{keyword}' 时失败: {str(e)}", "密码修改")
                    continue

            if password_inputs:
                log_success(f"文本内容策略成功，找到 {len(password_inputs)} 个密码输入框", "密码修改")
            else:
                log_debug("文本内容策略未找到密码输入框", "密码修改")

        except Exception as e:
            log_warning(f"文本内容策略异常: {str(e)}", "密码修改")

        return password_inputs

    def _find_by_position_relationship(self, page: Page) -> list:
        """
        策略5：基于位置关系查找密码输入框

        Args:
            page: Playwright页面对象

        Returns:
            找到的密码输入框列表
        """
        password_inputs = []

        try:
            log_debug("基于位置关系查找密码输入框", "密码修改")

            # 查找所有input元素，然后基于位置关系筛选
            all_inputs = page.query_selector_all('input')
            log_debug(f"找到 {len(all_inputs)} 个input元素", "密码修改")

            # 查找相邻的两个input（可能是新密码和确认密码）
            for i in range(len(all_inputs) - 1):
                try:
                    input1 = all_inputs[i]
                    input2 = all_inputs[i + 1]

                    # 检查是否都是可见的输入框
                    if input1.is_visible() and input2.is_visible():
                        # 获取位置信息
                        box1 = input1.bounding_box()
                        box2 = input2.bounding_box()

                        if box1 and box2:
                            # 检查是否垂直相邻（Y坐标相近）
                            y_diff = abs(box1['y'] - box2['y'])
                            if y_diff < 100:  # 100像素内认为是相邻的
                                password_inputs.extend([input1, input2])
                                log_debug(f"找到位置相邻的两个输入框，Y坐标差: {y_diff:.1f}px", "密码修改")
                                break

                except Exception as e:
                    log_debug(f"检查位置关系时失败: {str(e)}", "密码修改")
                    continue

            if password_inputs:
                log_success(f"位置关系策略成功，找到 {len(password_inputs)} 个密码输入框", "密码修改")
            else:
                log_debug("位置关系策略未找到密码输入框", "密码修改")

        except Exception as e:
            log_warning(f"位置关系策略异常: {str(e)}", "密码修改")

        return password_inputs

    def _find_with_dynamic_wait(self, page: Page) -> list:
        """
        策略6：动态等待后重新查找密码输入框

        Args:
            page: Playwright页面对象

        Returns:
            找到的密码输入框列表
        """
        password_inputs = []

        try:
            log_debug("使用动态等待策略查找密码输入框", "密码修改")

            # 等待页面可能的动态加载
            wait_times = [1, 2, 3]  # 等待1秒、2秒、3秒

            for wait_time in wait_times:
                try:
                    log_debug(f"等待 {wait_time} 秒后重新查找", "密码修改")
                    time.sleep(wait_time)

                    # 等待网络空闲
                    page.wait_for_load_state("networkidle", timeout=5000)

                    # 重新使用通用选择器查找
                    dynamic_inputs = page.query_selector_all('input[type="password"]')
                    if dynamic_inputs:
                        password_inputs.extend(dynamic_inputs)
                        log_debug(f"动态等待 {wait_time} 秒后找到 {len(dynamic_inputs)} 个密码输入框", "密码修改")
                        break

                except Exception as e:
                    log_debug(f"动态等待 {wait_time} 秒时失败: {str(e)}", "密码修改")
                    continue

            if password_inputs:
                log_success(f"动态等待策略成功，找到 {len(password_inputs)} 个密码输入框", "密码修改")
            else:
                log_debug("动态等待策略未找到密码输入框", "密码修改")

        except Exception as e:
            log_warning(f"动态等待策略异常: {str(e)}", "密码修改")

        return password_inputs

    def _deduplicate_and_sort_inputs(self, password_inputs: list) -> list:
        """
        去重并排序密码输入框

        Args:
            password_inputs: 密码输入框列表

        Returns:
            去重并排序后的密码输入框列表
        """
        try:
            # 去重
            unique_inputs = []
            for input_elem in password_inputs:
                if input_elem not in unique_inputs:
                    unique_inputs.append(input_elem)

            # 按照页面位置排序（从上到下）
            try:
                unique_inputs.sort(key=lambda elem: elem.bounding_box()['y'] if elem.bounding_box() else 0)
                log_debug(f"密码输入框已按位置排序", "密码修改")
            except Exception as e:
                log_debug(f"排序失败，保持原顺序: {str(e)}", "密码修改")

            log_debug(f"去重排序完成，最终 {len(unique_inputs)} 个唯一密码输入框", "密码修改")
            return unique_inputs

        except Exception as e:
            log_warning(f"去重排序时异常: {str(e)}", "密码修改")
            return password_inputs

    def _log_password_inputs_details(self, password_inputs: list, stage: str) -> None:
        """
        记录密码输入框的详细信息

        Args:
            password_inputs: 密码输入框列表
            stage: 检测阶段名称
        """
        try:
            if not password_inputs:
                log_debug(f"{stage} - 未找到任何密码输入框", "密码修改")
                return

            log_debug(f"{stage} - 找到 {len(password_inputs)} 个密码输入框", "密码修改")

            for i, input_elem in enumerate(password_inputs):
                try:
                    # 获取元素的详细信息
                    element_info = {
                        "索引": i + 1,
                        "标签": input_elem.tag_name,
                        "类型": input_elem.get_attribute('type') or '未知',
                        "ID": input_elem.get_attribute('id') or '无',
                        "名称": input_elem.get_attribute('name') or '无',
                        "类名": input_elem.get_attribute('class') or '无',
                        "占位符": input_elem.get_attribute('placeholder') or '无',
                        "可见": input_elem.is_visible(),
                        "可用": input_elem.is_enabled()
                    }

                    # 获取位置信息
                    try:
                        box = input_elem.bounding_box()
                        if box:
                            element_info["位置"] = f"({box['x']:.0f}, {box['y']:.0f})"
                            element_info["大小"] = f"{box['width']:.0f}x{box['height']:.0f}"
                        else:
                            element_info["位置"] = "无法获取"
                            element_info["大小"] = "无法获取"
                    except:
                        element_info["位置"] = "获取失败"
                        element_info["大小"] = "获取失败"

                    log_debug(f"{stage} - 输入框{i+1}: {element_info}", "密码修改")

                except Exception as e:
                    log_debug(f"{stage} - 输入框{i+1}信息获取失败: {str(e)}", "密码修改")

        except Exception as e:
            log_warning(f"记录密码输入框详细信息时异常: {str(e)}", "密码修改")

    def _verify_inputs_interactive_with_details(self, page: Page, password_inputs: list) -> Tuple[bool, dict]:
        """
        验证密码输入框是否可交互，并返回详细信息

        Args:
            page: Playwright页面对象
            password_inputs: 密码输入框列表

        Returns:
            (是否可交互, 详细信息字典)
        """
        details = {
            "总输入框数": len(password_inputs),
            "可见输入框数": 0,
            "可用输入框数": 0,
            "可交互输入框数": 0,
            "验证结果": []
        }

        if len(password_inputs) < 2:
            details["失败原因"] = f"密码输入框数量不足2个，实际: {len(password_inputs)}"
            log_debug("密码输入框数量不足2个，无法进行交互验证", "密码修改")
            return False, details

        try:
            interactive_count = 0

            for i, input_elem in enumerate(password_inputs[:2]):  # 只检查前两个
                input_details = {
                    "索引": i + 1,
                    "可见": False,
                    "可用": False,
                    "可聚焦": False,
                    "验证状态": "失败"
                }

                try:
                    # 检查元素是否可见和可用
                    is_visible = input_elem.is_visible()
                    is_enabled = input_elem.is_enabled()

                    input_details["可见"] = is_visible
                    input_details["可用"] = is_enabled

                    if is_visible:
                        details["可见输入框数"] += 1
                    if is_enabled:
                        details["可用输入框数"] += 1

                    if is_visible and is_enabled:
                        # 尝试聚焦元素
                        input_elem.focus()
                        input_details["可聚焦"] = True
                        input_details["验证状态"] = "成功"
                        interactive_count += 1
                        details["可交互输入框数"] += 1
                        log_debug(f"密码输入框 {i+1} 可交互", "密码修改")
                    else:
                        input_details["失败原因"] = f"不可见: {not is_visible}, 不可用: {not is_enabled}"
                        log_debug(f"密码输入框 {i+1} 不可交互（不可见或不可用）", "密码修改")

                except Exception as e:
                    input_details["异常"] = str(e)
                    log_debug(f"密码输入框 {i+1} 交互测试失败: {str(e)}", "密码修改")

                details["验证结果"].append(input_details)

            is_interactive = interactive_count >= 2
            details["最终结果"] = "通过" if is_interactive else "失败"
            details["交互成功率"] = f"{interactive_count}/2"

            log_debug(f"交互验证结果: {interactive_count}/2 个输入框可交互，验证{'通过' if is_interactive else '失败'}", "密码修改")
            return is_interactive, details

        except Exception as e:
            details["验证异常"] = str(e)
            log_warning(f"验证输入框交互性时发生异常: {str(e)}", "密码修改")
            return False, details

    def _verify_inputs_interactive(self, page: Page, password_inputs: list) -> bool:
        """
        验证密码输入框是否可交互

        Args:
            page: Playwright页面对象
            password_inputs: 密码输入框列表

        Returns:
            是否可交互
        """
        if len(password_inputs) < 2:
            log_debug("密码输入框数量不足2个，无法进行交互验证", "密码修改")
            return False

        try:
            interactive_count = 0
            for i, input_elem in enumerate(password_inputs[:2]):  # 只检查前两个
                try:
                    # 检查元素是否可见和可用
                    if input_elem.is_visible() and input_elem.is_enabled():
                        # 尝试聚焦元素
                        input_elem.focus()
                        interactive_count += 1
                        log_debug(f"密码输入框 {i+1} 可交互", "密码修改")
                    else:
                        log_debug(f"密码输入框 {i+1} 不可交互（不可见或不可用）", "密码修改")
                except Exception as e:
                    log_debug(f"密码输入框 {i+1} 交互测试失败: {str(e)}", "密码修改")
                    continue

            is_interactive = interactive_count >= 2
            log_debug(f"交互验证结果: {interactive_count}/2 个输入框可交互，验证{'通过' if is_interactive else '失败'}", "密码修改")
            return is_interactive

        except Exception as e:
            log_warning(f"验证输入框交互性时发生异常: {str(e)}", "密码修改")
            return False

    def detect_password_change_page(self, page: Page) -> Tuple[bool, str]:
        """
        检测当前页面是否为密码修改页面 - 使用分层验证机制

        分层验证逻辑：
        1. 第一层：粗略检测（关键词匹配）
        2. 第二层：精确验证（查找密码输入框）
        3. 第三层：功能验证（检查输入框可交互性）

        Args:
            page: Playwright页面对象

        Returns:
            (是否检测到密码修改页面, 检测结果描述)
        """
        start_time = time.time()
        self.statistics['detection_attempts'] += 1

        log_info("开始分层检测密码修改页面", "密码修改")
        log_debug(f"检测尝试次数: {self.statistics['detection_attempts']}", "密码修改")

        try:
            # 检查是否已经超时
            if self._is_timeout(start_time, self.max_detection_time, "密码修改检测"):
                self.statistics['detection_timeouts'] += 1
                return False, "检测超时，自动跳过"

            # 等待页面稳定
            page_load_start = time.time()
            page.wait_for_load_state("networkidle", timeout=self.page_timeout)
            time.sleep(1)
            page_load_time = time.time() - page_load_start
            log_debug(f"页面加载耗时: {page_load_time:.2f}秒", "密码修改")

            current_url = page.url
            page_title = page.title()
            page_content = page.content()

            log_info(f"检测页面URL: {current_url}", "密码修改")
            log_info(f"检测页面标题: {page_title}", "密码修改")
            log_debug(f"页面内容长度: {len(page_content)} 字符", "密码修改")

            # 再次检查超时
            if self._is_timeout(start_time, self.max_detection_time, "密码修改检测"):
                self.statistics['detection_timeouts'] += 1
                return False, "页面加载后检测超时，自动跳过"

            # ===== 第一层：粗略检测（关键词匹配） =====
            log_info("第一层检测：关键词匹配", "密码修改")
            rough_detection_result = self._rough_detection(page, current_url, page_title, page_content)

            if not rough_detection_result[0]:
                detection_time = time.time() - start_time
                self._update_average_detection_time(detection_time)
                log_info(f"第一层检测失败: {rough_detection_result[1]}", "密码修改")
                log_debug(f"检测耗时: {detection_time:.2f}秒", "密码修改")
                self._log_statistics()
                return False, f"第一层检测失败: {rough_detection_result[1]}"

            log_success(f"第一层检测通过: {rough_detection_result[1]}", "密码修改")

            # 检查第一层后是否超时
            if self._is_timeout(start_time, self.max_detection_time, "密码修改检测"):
                self.statistics['detection_timeouts'] += 1
                return False, "第一层检测后超时，自动跳过"

            # ===== 第二层：精确验证（查找密码输入框） =====
            log_info("第二层检测：查找密码输入框", "密码修改")
            layer2_start = time.time()
            password_inputs = self._find_password_inputs(page)
            layer2_time = time.time() - layer2_start

            # 记录详细的查找结果
            self._log_password_inputs_details(password_inputs, "第二层检测")

            # 检查第二层后是否超时
            if self._is_timeout(start_time, self.max_detection_time, "密码修改检测"):
                self.statistics['detection_timeouts'] += 1
                reason = f"第二层检测后超时，查找耗时: {layer2_time:.2f}秒"
                log_warning(reason, "密码修改")
                return False, "第二层检测后超时，自动跳过"

            if len(password_inputs) < 2:
                detection_time = time.time() - start_time
                self._update_average_detection_time(detection_time)
                reason = f"找到的密码输入框数量不足: {len(password_inputs)}/2，可能是误检测"

                # 详细记录跳过原因
                skip_details = {
                    "跳过原因": "密码输入框数量不足",
                    "找到数量": len(password_inputs),
                    "需要数量": 2,
                    "查找耗时": f"{layer2_time:.2f}秒",
                    "总检测耗时": f"{detection_time:.2f}秒",
                    "页面URL": page.url,
                    "页面标题": page.title()
                }

                log_warning(f"第二层检测失败: {reason}", "密码修改")
                log_info(f"跳过详情: {skip_details}", "密码修改")
                log_debug(f"检测耗时: {detection_time:.2f}秒", "密码修改")
                self._log_statistics()

                # 保存调试信息用于分析
                self._save_debug_info(page, "detection_failed_layer2", False, skip_details)
                return False, f"第二层检测失败: {reason}"

            log_success(f"第二层检测通过: 找到 {len(password_inputs)} 个密码输入框，查找耗时: {layer2_time:.2f}秒", "密码修改")

            # ===== 第三层：功能验证（检查输入框可交互性） =====
            log_info("第三层检测：验证输入框可交互性", "密码修改")
            layer3_start = time.time()
            is_interactive, interactive_details = self._verify_inputs_interactive_with_details(page, password_inputs)
            layer3_time = time.time() - layer3_start

            # 记录交互性验证的详细结果
            log_debug(f"第三层检测 - 交互性验证详情: {interactive_details}", "密码修改")

            # 检查第三层后是否超时
            if self._is_timeout(start_time, self.max_detection_time, "密码修改检测"):
                self.statistics['detection_timeouts'] += 1
                reason = f"第三层检测后超时，验证耗时: {layer3_time:.2f}秒"
                log_warning(reason, "密码修改")
                return False, "第三层检测后超时，自动跳过"

            if not is_interactive:
                detection_time = time.time() - start_time
                self._update_average_detection_time(detection_time)
                reason = "密码输入框不可交互，页面可能未完全加载"

                # 详细记录跳过原因
                skip_details = {
                    "跳过原因": "密码输入框不可交互",
                    "输入框总数": len(password_inputs),
                    "交互性详情": interactive_details,
                    "验证耗时": f"{layer3_time:.2f}秒",
                    "总检测耗时": f"{detection_time:.2f}秒",
                    "页面URL": page.url,
                    "页面标题": page.title()
                }

                log_warning(f"第三层检测失败: {reason}", "密码修改")
                log_info(f"跳过详情: {skip_details}", "密码修改")
                log_debug(f"检测耗时: {detection_time:.2f}秒", "密码修改")
                self._log_statistics()

                # 保存调试信息用于分析
                self._save_debug_info(page, "detection_failed_layer3", False, skip_details)
                return False, f"第三层检测失败: {reason}"

            # ===== 所有层检测都通过 =====
            detection_time = time.time() - start_time
            self.statistics['detection_success'] += 1
            self._update_average_detection_time(detection_time)

            log_success("三层检测全部通过，确认为密码修改页面", "密码修改")
            log_debug(f"检测耗时: {detection_time:.2f}秒", "密码修改")
            self._log_statistics()

            # 保存调试信息
            self._save_debug_info(page, "detection_success_all_layers", True)

            return True, f"三层检测通过: {rough_detection_result[1]}, 找到{len(password_inputs)}个可交互密码输入框"

        except Exception as e:
            detection_time = time.time() - start_time
            self._update_average_detection_time(detection_time)

            log_error("密码修改", e)
            log_debug(f"检测异常，耗时: {detection_time:.2f}秒", "密码修改")
            return False, f"检测密码修改页面异常: {str(e)}"

    def _rough_detection(self, page: Page, current_url: str, page_title: str, page_content: str) -> Tuple[bool, str]:
        """
        第一层：粗略检测（关键词匹配）

        Args:
            page: Playwright页面对象
            current_url: 当前页面URL
            page_title: 页面标题
            page_content: 页面内容

        Returns:
            (是否检测到, 检测结果描述)
        """
        try:
            # 使用配置的选择器检测密码修改指示器
            password_change_selectors = SELECTORS.get('password_change_indicators', [])

            for selector in password_change_selectors:
                try:
                    # 检查选择器是否匹配
                    if selector.startswith('text=') or selector.startswith('//text()'):
                        # 文本选择器，检查页面内容
                        text_to_find = selector.replace('text=', '').replace('//text()[contains(., \'', '').replace('\')]', '').replace('\'', '')
                        if text_to_find in page_content:
                            return True, f"匹配文本: {text_to_find}"
                    else:
                        # 元素选择器，检查元素是否存在
                        element = page.query_selector(selector)
                        if element:
                            element_text = element.text_content() or ""
                            return True, f"匹配选择器: {selector}, 元素文本: {element_text[:30]}..."

                except Exception as e:
                    # 单个选择器失败不影响整体检测
                    log_debug(f"选择器检测失败: {selector} - {str(e)}", "密码修改")
                    continue

            # 额外检查页面标题和URL
            password_keywords = ['修改密码', '密码修改', '修改登录密码', '密码即将过期', '请修改密码']

            for keyword in password_keywords:
                if keyword in page_title or keyword in current_url:
                    return True, f"匹配关键词: {keyword}"

            return False, "未找到密码修改相关的关键词或元素"

        except Exception as e:
            log_warning(f"粗略检测时发生异常: {str(e)}", "密码修改")
            return False, f"粗略检测异常: {str(e)}"

    def handle_password_change_with_retry(self, page: Page, student_name: str, new_password: str) -> Tuple[bool, str]:
        """
        带重试的密码修改处理流程

        Args:
            page: Playwright页面对象
            student_name: 学员姓名
            new_password: 新密码

        Returns:
            (是否处理成功, 处理结果描述)
        """
        start_time = time.time()
        self.statistics['change_attempts'] += 1
        max_retries = self.max_retries
        failure_count = 0

        log_info(f"开始处理密码修改流程，学员: {student_name}，最大重试次数: {max_retries}", "密码修改")
        log_debug(f"密码修改尝试次数: {self.statistics['change_attempts']}", "密码修改")
        log_debug("注意：密码内容已脱敏，不会记录到日志中", "密码修改")

        for attempt in range(max_retries):
            # 检查是否超时
            if self._is_timeout(start_time, self.max_change_time, "密码修改处理"):
                self.statistics['change_timeouts'] += 1
                reason = f"密码修改处理超时({self.max_change_time}秒)，自动跳过"
                log_warning(reason, "密码修改")
                if self.auto_skip_on_failure:
                    self.statistics['auto_skips'] += 1
                    return False, f"超时跳过: {reason}"
                else:
                    return False, reason

            # 检查是否应该自动跳过
            if self._should_auto_skip(failure_count, "密码修改"):
                reason = f"失败次数过多({failure_count})，自动跳过"
                log_warning(reason, "密码修改")
                return False, f"自动跳过: {reason}"

            log_info(f"密码修改尝试 {attempt + 1}/{max_retries}", "密码修改")

            try:
                success, error_msg = self._attempt_password_change(page, new_password)

                if success:
                    change_time = time.time() - start_time
                    self.statistics['change_success'] += 1
                    self.statistics['total_retries'] += attempt
                    self._update_average_change_time(change_time)

                    log_success(f"密码修改成功，尝试次数: {attempt + 1}", "密码修改")
                    log_debug(f"密码修改总耗时: {change_time:.2f}秒", "密码修改")
                    self._log_statistics()
                    return True, f"密码修改成功，尝试次数: {attempt + 1}"

                failure_count += 1
                log_warning(f"密码修改失败: {error_msg}", "密码修改")
                log_debug(f"当前尝试: {attempt + 1}/{max_retries}，失败次数: {failure_count}", "密码修改")

                # 检查是否应该自动跳过
                if self._should_auto_skip(failure_count, "密码修改"):
                    reason = f"失败次数过多({failure_count})，自动跳过"
                    log_warning(reason, "密码修改")
                    return False, f"自动跳过: {reason}"

                # 如果不是最后一次尝试，准备重试
                if attempt < max_retries - 1:
                    self.statistics['total_retries'] += 1
                    log_info("准备重试密码修改", "密码修改")
                    log_debug(f"重试延迟: {self.retry_delay}秒", "密码修改")

                    # 检查重试前是否超时
                    if self._is_timeout(start_time, self.max_change_time, "密码修改处理"):
                        self.statistics['change_timeouts'] += 1
                        reason = "重试前检测到超时，停止重试"
                        log_warning(reason, "密码修改")
                        if self.auto_skip_on_failure:
                            self.statistics['auto_skips'] += 1
                            return False, f"超时跳过: {reason}"
                        else:
                            return False, reason

                    # 等待一段时间后重试
                    time.sleep(self.retry_delay)

                    # 尝试刷新页面或重新检测
                    try:
                        page.wait_for_load_state("networkidle", timeout=5000)
                        time.sleep(1)
                        log_debug("页面刷新成功", "密码修改")
                        self.statistics['failure_recoveries'] += 1
                    except Exception as refresh_error:
                        log_warning(f"页面刷新失败: {str(refresh_error)}", "密码修改")

            except Exception as e:
                failure_count += 1
                log_error("密码修改", e)
                error_msg = f"密码修改尝试 {attempt + 1} 异常: {str(e)}"

                # 检查是否应该自动跳过
                if self._should_auto_skip(failure_count, "密码修改"):
                    reason = f"异常次数过多({failure_count})，自动跳过"
                    log_warning(reason, "密码修改")
                    return False, f"自动跳过: {reason}"

                # 如果是最后一次尝试，返回失败
                if attempt == max_retries - 1:
                    if self.auto_skip_on_failure:
                        self.statistics['auto_skips'] += 1
                        return False, f"最终失败，自动跳过: {error_msg}"
                    else:
                        return False, error_msg

                log_warning(f"{error_msg}，准备重试", "密码修改")
                time.sleep(self.retry_delay)

        # 所有重试都失败
        change_time = time.time() - start_time
        self.statistics['total_retries'] += max_retries
        self._update_average_change_time(change_time)

        final_error = f"密码修改失败，已重试 {max_retries} 次，失败次数: {failure_count}"

        # 详细的失败总结
        failure_summary = {
            "失败原因": "所有重试尝试均失败",
            "学员姓名": student_name,
            "重试次数": max_retries,
            "失败次数": failure_count,
            "总耗时": f"{change_time:.2f}秒",
            "平均每次耗时": f"{change_time/max_retries:.2f}秒",
            "恢复次数": self.statistics['failure_recoveries'],
            "自动跳过启用": self.auto_skip_on_failure,
            "页面URL": page.url,
            "页面标题": page.title()
        }

        if self.auto_skip_on_failure:
            self.statistics['auto_skips'] += 1
            log_warning(f"所有重试失败，启用自动跳过: {final_error}", "密码修改")
            log_info(f"失败总结: {failure_summary}", "密码修改")
            log_debug(f"密码修改失败总耗时: {change_time:.2f}秒", "密码修改")
            self._log_statistics()

            # 保存失败的详细调试信息
            try:
                self._save_debug_info(page, "final_failure_auto_skip", False, failure_summary)
            except Exception as debug_error:
                log_warning(f"保存失败调试信息时异常: {str(debug_error)}", "密码修改")

            return False, f"自动跳过: {final_error}"
        else:
            log_error("密码修改", Exception(final_error))
            log_info(f"失败总结: {failure_summary}", "密码修改")
            log_debug(f"密码修改失败总耗时: {change_time:.2f}秒", "密码修改")
            self._log_statistics()

            # 保存失败的详细调试信息
            try:
                self._save_debug_info(page, "final_failure", False, failure_summary)
            except Exception as debug_error:
                log_warning(f"保存失败调试信息时异常: {str(debug_error)}", "密码修改")

            return False, final_error

    def _attempt_password_change(self, page: Page, original_password: str) -> Tuple[bool, str]:
        """
        单次密码修改尝试

        Args:
            page: Playwright页面对象
            original_password: 原密码

        Returns:
            (是否处理成功, 处理结果描述)
        """
        try:
            # 步骤1: 尝试填写原密码（如果页面需要的话）
            old_password_filled = False
            try:
                success, error_msg = self._fill_old_password(page, original_password)
                if success:
                    old_password_filled = True
                    log_info("原密码填写成功", "密码修改")
                else:
                    log_info(f"跳过原密码填写: {error_msg}", "密码修改")
            except Exception as e:
                log_info(f"跳过原密码填写（页面可能不需要）: {str(e)}", "密码修改")

            # 步骤2: 填写新密码（与原密码相同）
            success, error_msg = self._fill_new_password(page, original_password)
            if not success:
                return False, f"填写新密码失败: {error_msg}"

            # 步骤3: 填写确认密码
            success, error_msg = self._fill_confirm_password(page, original_password)
            if not success:
                return False, f"填写确认密码失败: {error_msg}"

            # 步骤4: 点击修改密码按钮
            success, error_msg = self._click_change_button(page)
            if not success:
                return False, f"点击修改按钮失败: {error_msg}"

            # 步骤5: 等待处理结果
            success, error_msg = self._wait_for_change_result(page)
            if not success:
                return False, f"密码修改处理失败: {error_msg}"

            if old_password_filled:
                return True, "密码修改流程处理成功（包含原密码）"
            else:
                return True, "密码修改流程处理成功（仅新密码模式）"

        except Exception as e:
            log_error("密码修改", e)
            return False, f"处理密码修改流程异常: {str(e)}"

    def _fill_old_password(self, page: Page, password: str) -> Tuple[bool, str]:
        """填写原密码（如果页面需要的话）"""
        log_info("检查是否需要填写原密码", "密码修改")

        old_password_selectors = SELECTORS.get('old_password_input', [])

        # 使用更短的超时时间快速检测
        for selector in old_password_selectors:
            try:
                # 使用 locator 方法获取元素，使用很短的超时时间（1秒）
                locator = page.locator(selector)

                # 快速检测元素是否存在
                locator.wait_for(timeout=1000)

                # 确保元素可见和可交互
                if not locator.is_visible():
                    log_debug(f"原密码输入框不可见: {selector}", "密码修改")
                    continue

                if not locator.is_enabled():
                    log_debug(f"原密码输入框不可用: {selector}", "密码修改")
                    continue

                # 清空输入框并填写密码
                locator.fill("")  # 先清空
                time.sleep(0.2)  # 短暂等待清空完成
                locator.fill(password)  # 再填写密码
                time.sleep(0.2)  # 短暂等待填写完成

                # 验证输入是否成功
                try:
                    input_value = locator.input_value()
                    if input_value == password:
                        log_success(f"原密码填写成功: {selector}", "密码修改")
                        return True, "原密码填写成功"
                    else:
                        log_warning(f"原密码填写验证失败: {selector}", "密码修改")
                        continue
                except:
                    # 某些密码框可能无法读取值，直接认为成功
                    log_success(f"原密码填写成功: {selector}", "密码修改")
                    return True, "原密码填写成功"

            except Exception as e:
                # 使用debug级别，避免过多警告日志
                log_debug(f"原密码选择器检测: {selector} - 未找到", "密码修改")
                continue

        log_info("页面无需原密码输入，跳过此步骤", "密码修改")
        return False, "页面无需原密码输入"

    def _fill_new_password(self, page: Page, password: str) -> Tuple[bool, str]:
        """填写新密码 - 使用智能查找策略"""
        log_info("开始填写新密码", "密码修改")

        # 策略1：使用配置的选择器
        new_password_selectors = SELECTORS.get('new_password_input', [])
        for selector in new_password_selectors:
            try:
                # 使用 locator 方法获取元素
                locator = page.locator(selector)

                # 等待元素出现并可见
                locator.wait_for(timeout=min(self.element_timeout, 5000))

                # 确保元素可见和可交互
                if not locator.is_visible():
                    log_debug(f"新密码输入框不可见: {selector}", "密码修改")
                    continue

                if not locator.is_enabled():
                    log_debug(f"新密码输入框不可用: {selector}", "密码修改")
                    continue

                # 清空输入框并填写密码
                locator.fill("")  # 先清空
                time.sleep(0.2)  # 短暂等待清空完成
                locator.fill(password)  # 再填写密码
                time.sleep(0.2)  # 短暂等待填写完成

                # 验证输入是否成功
                try:
                    input_value = locator.input_value()
                    if input_value == password:
                        log_success(f"新密码填写成功: {selector}", "密码修改")
                        return True, "新密码填写成功"
                    else:
                        log_debug(f"新密码填写验证失败: {selector}", "密码修改")
                        continue
                except:
                    # 某些密码框可能无法读取值，直接认为成功
                    log_success(f"新密码填写成功: {selector}", "密码修改")
                    return True, "新密码填写成功"

            except Exception as e:
                log_debug(f"新密码选择器失败: {selector} - {str(e)}", "密码修改")
                continue

        # 策略2：使用智能查找到的密码输入框
        log_info("配置选择器失败，使用智能查找策略", "密码修改")
        password_inputs = self._find_password_inputs(page)

        if len(password_inputs) >= 1:
            try:
                # 使用第一个密码输入框作为新密码输入框
                first_input = password_inputs[0]

                # 聚焦并填写
                first_input.focus()
                first_input.fill("")  # 先清空
                time.sleep(0.2)
                first_input.fill(password)  # 再填写密码
                time.sleep(0.2)

                # 验证输入是否成功
                try:
                    input_value = first_input.input_value()
                    if input_value == password:
                        log_success("通过智能查找填写新密码成功", "密码修改")
                        return True, "通过智能查找填写新密码成功"
                    else:
                        log_debug("智能查找的密码框填写验证失败", "密码修改")
                except:
                    # 某些密码框可能无法读取值，直接认为成功
                    log_success("通过智能查找填写新密码成功", "密码修改")
                    return True, "通过智能查找填写新密码成功"

            except Exception as e:
                log_warning(f"智能查找的密码框填写失败: {str(e)}", "密码修改")
        else:
            log_warning("智能查找未找到密码输入框", "密码修改")

        return False, "未找到新密码输入框"

    def _fill_confirm_password(self, page: Page, password: str) -> Tuple[bool, str]:
        """填写确认密码"""
        log_info("填写确认密码", "密码修改")

        confirm_password_selectors = SELECTORS.get('confirm_password_input', [])

        for selector in confirm_password_selectors:
            try:
                # 使用 locator 方法获取元素
                locator = page.locator(selector)

                # 等待元素出现并可见
                locator.wait_for(timeout=min(self.element_timeout, 5000))

                # 确保元素可见和可交互
                if not locator.is_visible():
                    log_warning(f"确认密码输入框不可见: {selector}", "密码修改")
                    continue

                if not locator.is_enabled():
                    log_warning(f"确认密码输入框不可用: {selector}", "密码修改")
                    continue

                # 清空输入框并填写密码
                locator.fill("")  # 先清空
                time.sleep(0.2)  # 短暂等待清空完成
                locator.fill(password)  # 再填写密码
                time.sleep(0.2)  # 短暂等待填写完成

                # 验证输入是否成功
                try:
                    input_value = locator.input_value()
                    if input_value == password:
                        log_success(f"确认密码填写成功: {selector}", "密码修改")
                        return True, "确认密码填写成功"
                    else:
                        log_warning(f"确认密码填写验证失败: {selector}", "密码修改")
                        continue
                except:
                    # 某些密码框可能无法读取值，直接认为成功
                    log_success(f"确认密码填写成功: {selector}", "密码修改")
                    return True, "确认密码填写成功"

            except Exception as e:
                log_warning(f"确认密码选择器失败: {selector} - {str(e)}", "密码修改")
                continue

        return False, "未找到确认密码输入框"

    def _click_change_button(self, page: Page) -> Tuple[bool, str]:
        """点击修改密码按钮"""
        log_info("点击修改密码按钮", "密码修改")

        change_button_selectors = SELECTORS.get('change_password_button', [])

        for selector in change_button_selectors:
            try:
                # 使用 locator 方法获取元素
                locator = page.locator(selector)

                # 等待元素出现并可见
                locator.wait_for(timeout=min(self.element_timeout, 5000))

                # 确保元素可见和可交互
                if not locator.is_visible():
                    log_warning(f"修改密码按钮不可见: {selector}", "密码修改")
                    continue

                if not locator.is_enabled():
                    log_warning(f"修改密码按钮不可用: {selector}", "密码修改")
                    continue

                # 点击按钮
                locator.click()
                time.sleep(0.5)  # 等待点击响应

                log_success(f"修改密码按钮点击成功: {selector}", "密码修改")
                return True, "修改密码按钮点击成功"

            except Exception as e:
                log_warning(f"修改密码按钮选择器失败: {selector} - {str(e)}", "密码修改")
                continue

        return False, "未找到修改密码按钮"

    def _wait_for_change_result(self, page: Page) -> Tuple[bool, str]:
        """等待密码修改结果"""
        log_info("等待密码修改结果", "密码修改")

        try:
            # 等待页面响应，使用较短的超时时间
            try:
                page.wait_for_load_state("networkidle", timeout=min(self.page_timeout, 15000))
            except Exception as timeout_error:
                log_warning(f"等待页面稳定超时: {str(timeout_error)}", "密码修改")
                # 继续处理，不因为超时而失败

            time.sleep(2)

            current_url = page.url
            page_title = page.title()

            log_info(f"密码修改后URL: {current_url}", "密码修改")
            log_info(f"密码修改后标题: {page_title}", "密码修改")

            # 获取页面内容，但限制大小避免内存问题
            try:
                page_content = page.content()
                # 限制内容长度，避免处理过大的页面
                if len(page_content) > 50000:
                    page_content = page_content[:50000]
            except Exception as content_error:
                log_warning(f"获取页面内容失败: {str(content_error)}", "密码修改")
                page_content = ""

            # 检查成功指标
            success_indicators = [
                "成功", "修改成功", "密码修改成功", "操作成功", "完成",
                "success", "successful", "complete", "done", "ok"
            ]

            # 检查错误指标
            error_indicators = [
                "错误", "失败", "修改失败", "密码错误", "操作失败", "异常",
                "error", "fail", "failed", "invalid", "wrong", "exception"
            ]

            # 检查成功指标
            for indicator in success_indicators:
                if indicator in page_content.lower() or indicator in page_title.lower():
                    return True, f"密码修改成功，检测到成功指标: {indicator}"

            # 检查错误指标
            for indicator in error_indicators:
                if indicator in page_content.lower() or indicator in page_title.lower():
                    return False, f"密码修改失败，检测到错误指标: {indicator}"

            # 如果页面跳转了，认为可能成功
            if "password" not in current_url.lower() and "修改" not in current_url:
                return True, "密码修改可能成功，页面已跳转"

            # 检查是否还在密码修改页面
            if any(keyword in current_url.lower() for keyword in ["password", "修改", "change"]):
                return False, "仍在密码修改页面，可能修改失败"

            # 默认认为成功（保守策略）
            return True, "密码修改状态不明确，默认认为成功"

        except Exception as e:
            log_error("密码修改", e)
            return False, f"等待密码修改结果异常: {str(e)}"

    def _update_average_detection_time(self, detection_time: float):
        """更新平均检测时间"""
        current_avg = self.statistics['average_detection_time']
        attempts = self.statistics['detection_attempts']

        if attempts <= 1:
            self.statistics['average_detection_time'] = detection_time
        else:
            # 计算新的平均值
            self.statistics['average_detection_time'] = (current_avg * (attempts - 1) + detection_time) / attempts

    def _update_average_change_time(self, change_time: float):
        """更新平均修改时间"""
        current_avg = self.statistics['average_change_time']
        attempts = self.statistics['change_attempts']

        if attempts <= 1:
            self.statistics['average_change_time'] = change_time
        else:
            # 计算新的平均值
            self.statistics['average_change_time'] = (current_avg * (attempts - 1) + change_time) / attempts

    def _log_statistics(self):
        """记录统计信息"""
        stats = self.statistics
        log_debug(f"统计信息 - 检测: {stats['detection_success']}/{stats['detection_attempts']} "
                 f"修改: {stats['change_success']}/{stats['change_attempts']} "
                 f"重试: {stats['total_retries']} "
                 f"超时: 检测{stats['detection_timeouts']} 修改{stats['change_timeouts']} "
                 f"自动跳过: {stats['auto_skips']} "
                 f"恢复: {stats['failure_recoveries']} "
                 f"平均检测时间: {stats['average_detection_time']:.2f}s "
                 f"平均修改时间: {stats['average_change_time']:.2f}s", "密码修改")

    def get_statistics(self) -> dict:
        """获取统计信息"""
        return self.statistics.copy()

    def reset_statistics(self):
        """重置统计信息"""
        self.statistics = {
            'detection_attempts': 0,
            'detection_success': 0,
            'change_attempts': 0,
            'change_success': 0,
            'total_retries': 0,
            'average_detection_time': 0.0,
            'average_change_time': 0.0
        }
        log_debug("统计信息已重置", "密码修改")
