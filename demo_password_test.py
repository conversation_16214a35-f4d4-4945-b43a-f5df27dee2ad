#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
条件性密码修改功能演示脚本
展示如何使用测试套件验证密码修改功能
"""

import sys
import time
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from modules.data import load_students_data
from modules.browser import BrowserManager
from modules.login import LoginManager
from modules.logger import get_logger_manager, log_info, log_success, log_error
from config import CONFIG


def demo_single_student_test():
    """演示单个学员的条件性密码修改测试"""
    print("🎯 条件性密码修改功能演示")
    print("=" * 50)
    
    # 初始化日志
    logger_manager = get_logger_manager()
    
    # 加载测试数据
    print("📋 加载测试数据...")
    try:
        students = load_students_data("data/students.csv")
        if not students:
            print("❌ 无法加载测试数据")
            return False
        
        # 使用第一个学员进行演示
        student = students[0]
        student_name = student["姓名"]
        username = student["身份证号"]
        password = student["密码"]
        
        print(f"✅ 成功加载测试数据，将演示学员: {student_name}")
        print(f"   身份证号: {username}")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False
    
    # 初始化浏览器
    print("\n🌐 初始化浏览器...")
    browser_manager = BrowserManager()
    
    if not browser_manager.init_browser():
        print("❌ 浏览器初始化失败")
        return False
    
    print("✅ 浏览器初始化成功")
    
    try:
        # 演示登录流程（包含条件性密码修改）
        print(f"\n🔐 开始登录演示: {student_name}")
        print("   此过程将自动检测是否需要修改密码...")
        
        login_manager = LoginManager(browser_manager)
        success, error_msg, page = login_manager.perform_login(username, password, max_retries=2)
        
        if success:
            print("✅ 登录成功！")
            
            # 分析登录结果
            if "密码修改" in error_msg:
                print("🔑 检测到密码修改处理:")
                if "成功" in error_msg:
                    print("   ✅ 密码修改已自动处理成功")
                    print("   📝 系统自动使用CSV中的密码作为新密码")
                    print("   🔄 密码修改完成后继续正常流程")
                else:
                    print("   ⚠️ 密码修改处理失败，但登录成功")
                    print("   📝 可能需要手动检查密码修改页面")
            else:
                print("ℹ️ 无需密码修改，直接登录成功")
                print("   📝 系统智能跳过了密码修改步骤")
            
            # 显示当前页面信息
            if page:
                try:
                    current_url = page.url
                    page_title = page.title()
                    print(f"\n📄 当前页面信息:")
                    print(f"   URL: {current_url}")
                    print(f"   标题: {page_title}")
                except Exception as e:
                    print(f"   ⚠️ 无法获取页面信息: {e}")
                
                # 清理页面
                browser_manager.close_page(page)
            
            print("\n🎉 演示完成！条件性密码修改功能工作正常")
            return True
            
        else:
            print(f"❌ 登录失败: {error_msg}")
            return False
    
    except Exception as e:
        print(f"❌ 演示过程异常: {e}")
        return False
    
    finally:
        # 确保浏览器关闭
        try:
            browser_manager.close()
            print("\n🌐 浏览器已关闭")
        except Exception as e:
            print(f"\n⚠️ 浏览器关闭异常: {e}")


def show_feature_overview():
    """显示功能概览"""
    print("🎯 条件性密码修改功能概览")
    print("=" * 50)
    print()
    print("📋 功能特点:")
    print("   ✅ 智能检测密码修改页面")
    print("   ✅ 自动填写密码修改表单")
    print("   ✅ 条件性跳过无需修改的情况")
    print("   ✅ 完全集成到登录流程中")
    print("   ✅ 详细的日志记录和统计")
    print()
    print("🔧 技术实现:")
    print("   • 使用多种选择器策略确保兼容性")
    print("   • 基于页面内容和元素的智能检测")
    print("   • 自动重试机制提高成功率")
    print("   • 完整的错误处理和恢复")
    print()
    print("📊 测试覆盖:")
    print("   • 密码修改页面检测准确性")
    print("   • 自动密码修改流程完整性")
    print("   • 无密码修改时的跳过逻辑")
    print("   • 批量处理的稳定性")
    print("   • 异常情况的处理能力")
    print()
    print("🚀 使用方式:")
    print("   1. 快速测试: python tests/quick_password_test.py")
    print("   2. 完整测试: python tests/test_conditional_password_change.py")
    print("   3. 测试菜单: python run_password_tests.py")
    print("   4. 演示功能: python demo_password_test.py")
    print()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="条件性密码修改功能演示")
    parser.add_argument("--demo", "-d", action="store_true", help="运行演示")
    parser.add_argument("--overview", "-o", action="store_true", help="显示功能概览")
    parser.add_argument("--headless", action="store_true", help="使用无头模式")
    
    args = parser.parse_args()
    
    # 更新配置
    if args.headless:
        CONFIG["headless"] = True
    
    try:
        if args.overview:
            show_feature_overview()
        elif args.demo:
            success = demo_single_student_test()
            sys.exit(0 if success else 1)
        else:
            # 默认显示概览和运行演示
            show_feature_overview()
            
            confirm = input("\n是否运行演示？(y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                success = demo_single_student_test()
                sys.exit(0 if success else 1)
            else:
                print("👋 再见！")
                sys.exit(0)
    
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 系统异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
