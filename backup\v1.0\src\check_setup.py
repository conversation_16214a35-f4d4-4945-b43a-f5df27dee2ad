#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查系统设置脚本
"""

import os
from pathlib import Path

def check_setup():
    """检查系统设置"""
    print("🔍 检查系统设置...")
    print("=" * 40)
    
    current_dir = Path.cwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 检查必要文件
    required_files = [
        "main.py",
        "config.py",
        "../data/students.csv",
        "../README.md"
    ]

    required_dirs = [
        "modules",
        "../output/screenshots",
        "../output/logs"
    ]
    
    print(f"\n📄 检查必要文件:")
    all_files_exist = True
    for file in required_files:
        if Path(file).exists():
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - 缺失")
            all_files_exist = False
    
    print(f"\n📁 检查必要目录:")
    all_dirs_exist = True
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"   ✅ {dir_name}/")
        else:
            print(f"   ❌ {dir_name}/ - 缺失")
            all_dirs_exist = False
    
    # 检查modules目录内容
    modules_dir = Path("modules")
    if modules_dir.exists():
        print(f"\n🔧 检查模块文件:")
        module_files = [
            "data.py",
            "login.py", 
            "navigation.py",
            "screenshot.py",
            "captcha.py",
            "browser.py",
            "logger.py"
        ]
        
        for module in module_files:
            module_path = modules_dir / module
            if module_path.exists():
                print(f"   ✅ modules/{module}")
            else:
                print(f"   ❌ modules/{module} - 缺失")
    
    # 检查学员数据
    if Path("../data/students.csv").exists():
        print(f"\n👥 检查学员数据:")
        try:
            with open("../data/students.csv", 'r', encoding='utf-8-sig') as f:
                lines = f.readlines()
                print(f"   📊 数据行数: {len(lines)}")
                if len(lines) > 1:
                    print(f"   👤 学员数量: {len(lines) - 1}")
                    print(f"   📋 表头: {lines[0].strip()}")
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")
    
    print(f"\n🎯 系统状态:")
    if all_files_exist and all_dirs_exist:
        print(f"   ✅ 系统设置完整，可以运行!")
        print(f"\n🚀 运行命令:")
        print(f"   python main.py")
        print(f"   python main.py --headless")
        print(f"   python main.py --debug")
    else:
        print(f"   ❌ 系统设置不完整，请检查缺失的文件")
        print(f"\n💡 可能的解决方案:")
        print(f"   1. 确保您在正确的目录 (包含main.py的目录)")
        print(f"   2. 检查文件是否被意外删除")
        print(f"   3. 重新下载或恢复缺失的文件")

if __name__ == "__main__":
    check_setup()
