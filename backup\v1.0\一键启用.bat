@echo off
chcp 65001 >nul
title 自考成绩查询系统 - 一键启用脚本

echo.
echo ========================================
echo    自考成绩查询自动化系统 v1.0
echo    一键启用脚本
echo ========================================
echo.

:: 检查Python是否安装
echo [1/8] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 💡 请先安装Python 3.8+并添加到系统PATH
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

:: 检查pip是否可用
echo [2/8] 检查pip工具...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip工具不可用
    echo 💡 请重新安装Python或修复pip
    pause
    exit /b 1
)
echo ✅ pip工具检查通过

:: 创建必要目录
echo [3/8] 创建必要目录...
if not exist "output" mkdir output
if not exist "output\screenshots" mkdir output\screenshots
if not exist "output\logs" mkdir output\logs
if not exist "data" mkdir data
echo ✅ 目录创建完成

:: 安装Python依赖
echo [4/8] 安装Python依赖包...
echo 📦 正在安装依赖，请稍候...
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --quiet
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，尝试使用默认源...
    pip install -r requirements.txt --quiet
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        echo 💡 请检查网络连接或手动安装: pip install -r requirements.txt
        pause
        exit /b 1
    )
)
echo ✅ Python依赖安装完成

:: 安装Playwright浏览器
echo [5/8] 安装Playwright浏览器...
echo 🌐 正在下载Chromium浏览器，请稍候...
playwright install chromium --quiet
if %errorlevel% neq 0 (
    echo ❌ Playwright浏览器安装失败
    echo 💡 请检查网络连接或手动安装: playwright install chromium
    pause
    exit /b 1
)
echo ✅ Playwright浏览器安装完成

:: 检查配置文件
echo [6/8] 检查配置文件...
if not exist "config.yaml" (
    echo ❌ config.yaml配置文件缺失
    echo 💡 请确保config.yaml文件存在
    pause
    exit /b 1
)
echo ✅ 配置文件检查通过

:: 检查数据文件
echo [7/8] 检查数据文件...
if not exist "data\students.csv" (
    echo ⚠️  学员数据文件不存在，创建示例文件...
    echo 姓名,身份证号,密码 > data\students.csv
    echo 张三,123456789012345678,password123 >> data\students.csv
    echo ✅ 已创建示例数据文件，请编辑 data\students.csv 添加真实数据
) else (
    echo ✅ 数据文件检查通过
)

:: 运行系统检查
echo [8/8] 运行系统自检...
python -c "import sys; sys.path.append('.'); from src.config import CONFIG; print('✅ 配置系统正常')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ 系统配置检查失败
    echo 💡 请检查源代码文件是否完整
    pause
    exit /b 1
)

python -c "import playwright; print('✅ Playwright正常')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Playwright检查失败
    echo 💡 请重新运行脚本或手动安装: playwright install chromium
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 系统启用完成！
echo ========================================
echo.
echo 📋 系统信息:
echo    📁 项目目录: %CD%
echo    📄 配置文件: config.yaml
echo    📊 数据文件: data\students.csv
echo    📸 截图目录: output\screenshots
echo    📝 日志目录: output\logs
echo.
echo 🚀 启动命令:
echo    python src\main.py              # 标准模式
echo    python src\main.py --headless   # 无头模式
echo    python src\main.py --debug      # 调试模式
echo.
echo 📖 使用说明:
echo    1. 编辑 data\students.csv 添加学员信息
echo    2. 根据需要修改 config.yaml 配置
echo    3. 运行上述启动命令开始处理
echo.

:: 询问是否立即启动
set /p choice="是否立即启动系统？(y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🚀 启动系统...
    python src\main.py
) else (
    echo.
    echo 💡 您可以稍后使用以下命令启动系统:
    echo    python src\main.py
)

echo.
echo 按任意键退出...
pause >nul
