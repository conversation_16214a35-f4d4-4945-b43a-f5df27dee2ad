<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml"><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>福建省高等教育自学考试</title>
<script language="javascript" src="/framework/pjs/jquery.min.20120321.js"></script>
<style type="text/css">
<!--
body {
	font-family: "宋体";
	font-size: 12px;
	color: #494949;
	text-align: center;
	margin: 0px;
	padding: 0px;
	background-color: #ffffff;
	line-height: 18px;
}

div, form, img, ul, ol, li, dl, dt, dd {
	padding: 0;
	border: 0;
	margin-top: 0;
	margin-right: 0;
	margin-bottom: 0;
	margin-left: 0;
}

li {
	list-style-type: none;
}

ol, ul {
	list-style: none;
}

a:link {
	text-decoration: none;
	color: #000000;
}

a:visited {
	text-decoration: none;
	color: #000000;
}

a:hover {
	text-decoration: underline;
	color: #990000;
}

a:active {
	text-decoration: none;
	color: #000000;
}

.public-table {
	border: 0;
	margin: 0;
	border-collapse: collapse;
	border-spacing: 0;
}
/*cellspacing*/
.public-table td {
	padding: 0;
}
/*cellpadding*/
/*border="0" cellspacing="0" cellpadding="0"*/
.top_img {
	background: url(/framework/pimg/fjzk/fjzk960.jpg) -0px -0px repeat;
	height: 112px;
}

.bmbox_title {
	width: 960px;
	height: 3px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
	background-color: #dfefff;
	background: url(/framework/pimg/fjzk/member_bg.jpg) -0px -0px repeat;
}

.bmbox_bottom {
	width: 960px;
	height: 3px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
	background-color: #dfefff;
}

.bmbox_mod {
	background-color: #dfefff;
	color: #000000;
	width: 960px;
	font-size: 14px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
}

.mod_next {
	color: #000000;
	width: 954px;
	font-size: 14px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
}

.menu1 {
	margin-top: 0px;
	margin-bottom: 0px;
	height: 44px;
	overflow: hidden;
	background: url(/framework/pimg/fjzk/fjzk960.jpg) -0px -112px repeat;
}

.menubutton {
	height: 30px;
	overflow: hidden;
}

.menu_index {
	margin-top: 0px;
	margin-bottom: 0px;
	text-align: center;
	width: 90px;
	height: 30px;
	overflow: hidden;
	background: url(/framework/pimg/fjzk/menu1.gif) -6px -8px repeat;
	border-right: #07467c 1px solid;
}

.menuselect {
	margin-top: 0px;
	margin-bottom: 0px;
	text-align: center;
	padding-left: 10px;
	padding-right: 10px;
	height: 30px;
	overflow: hidden;
	background: url(/framework/pimg/fjzk/menu1.gif) -6px -98px repeat;
	border-right: #07467c 1px solid;
}

.ML1 { /*background:url('/framework/pimg/b_1.gif');*/
	width: 6px;
	height: 30px;
	overflow: hidden;
}

.MR1 { /*background:url('/framework/pimg/b_3.gif');*/
	width: 6px;
	height: 30px;
	overflow: hidden;
}

.menu1 a:link {
	text-decoration: none;
	color: #ffffff;
}

.menu1 a:visited {
	text-decoration: none;
	color: #ffffff;
}

.menu1 a:hover {
	text-decoration: underline;
	color: #990000;
}

.menu1 a:active {
	text-decoration: none;
	color: #ffffff;
}

.leftmenu {
	float: left;
	background-color: #ffffff;
	color: #000000;
	width: 168px;
	font-size: 14px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
	border: 1px #b4d2ea solid;
	border-bottom: 1px #c7dfd6 solid;
}

.rightmain {
	float: right;
	background-color: #ffffff;
	color: #000000;
	width: 780px;
	font-size: 14px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
	border: 1px #c7dfd6 solid;
	border-bottom: 1px #c7dfd6 solid;
}

.rightmain_dh {
	background: url(/framework/pimg/fjzk/member_dh.jpg) -1px 0px repeat;
	height: 65px;
	font-size: 12px;
	color: #5b89aa;
	text-align: left;
}

.editmain {
	background-color: #ffffff;
	color: #000000;
	width: 946px;
	font-size: 14px; /*cursor:pointer;*/
	overflow: hidden;
	margin: auto;
	margin-top: 0px;
	border: 1px #c7dfd6 solid;
	border-bottom: 1px #c7dfd6 solid;
}

.box2 {
	width: 193px;
	height: 398px;
	background: url(/framework/pimg/stmenu.png) -2px -0px no-repeat;
}

.box2 .box_title2 {
	padding-top: 20px;
	font-size: 14px;
	font-weight: bold;
}

.box2 .box_title2 {
	padding-top: 20px;
	font-size: 14px;
	font-weight: bold;
}

.box2 ul {
	margin-top: 12px;
}

.box2 li {
	margin: 3px;
	width: 170px;
	height: 33px;
	line-height: 33px;
	border: 0px #000 solid;
	background: url(/framework/pimg/stmenu.png) -12px -433px no-repeat;
}

.box2 .now2 {
	background: url(/framework/pimg/stmenu.png) -12px -398px no-repeat;
}

.box2 .now2 {
	font-size: 12px;
}

.box2 .now1 {
	font-size: 12px;
}
-->
</style>
<style type="text/css">
<!--
.ml_title {
	cursor: pointer;
	padding-left: 6px;
	color: #296599;
	text-align: left;
	font-weight: bold;
	font-size: 12px;
	color: #296599;
	line-height: 30px;
}

.ml_title a {
	color: #296599;
	text-align: left;
	font-size: 12px;
	color: #296599;
}

.span_list_title {
	text-align: left;
	padding-left: 20px;
}

.span_list_title a {
	padding-left: 6px;
	color: #296599;
	text-align: left;
	font-size: 12px;
	color: #296599;
}

.span_1a {
	border-bottom: #e0eef9 1px solid;
	text-align: left;
}

.span_1b {
	border-bottom: #e0eef9 1px solid;
	text-align: left;
}

.span_1a .uml_ico {
	width: 12px;
	background: url(/framework/pimg/fjzk/ico1.gif) 3px 6px repeat;
}

.span_1b .uml_ico {
	width: 12px;
	background: url(/framework/pimg/fjzk/ico1.gif) -9px 6px repeat;
}

.span_1a .span_list {
	DISPLAY: none;
}

.span_1b .span_list {
	DISPLAY: block;
}
-->
</style>

<script>
function jsmenus_onclick(o){
	var v=document.getElementById('span_'+o).className;
	if(v=="span_1a"){
		document.getElementById('span_'+o).className= 'span_1b';
	}else{
		document.getElementById('span_'+o).className= 'span_1a';
	}
}
</script>
</head>
<!--BODY oncontextmenu="return false" onselectstart="return false" ondragstart="return false" onbeforecopy="return false" oncopy="return false" oncut="return false;"-->
<body>
	<!-- top img begin -->
	<table width="960" border="0" cellspacing="0" cellpadding="0" align="center">
		<tbody><tr>
			<td><img src="/framework/online/skzy/top.jpg" width="960" height="136"></td>
		</tr>
	</tbody></table>
	<!-- top img end -->


	<div class="bmbox_title"></div>
	<div class="bmbox_mod">
		<div class="mod_next">
			<div class="leftmenu"><div style="font-size:12px;line-height:24px;"><div style="background-color:#5d99cf;color:#ffffff;line-height:30px;"><b> </b></div><div id="span_1" class="span_1a"><table border="0" cellspacing="0" cellpadding="0">	<tbody><tr>		<td class="uml_ico"></td>		<td class="ml_title"><a href="./?p=uMain&amp;a=index&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">平台首页</a></td>	</tr></tbody></table></div><div id="span_1" class="span_1b"><table border="0" cellspacing="0" cellpadding="0">	<tbody><tr>		<td class="uml_ico"></td>		<td class="ml_title">2025年4月报考报名</td>	</tr></tbody></table><table border="0" cellspacing="0" cellpadding="0" class="span_list">	<tbody><tr><td class="span_list_title"><a href="./?p=uBkbmInfo&amp;a=ubkbmskgotosn&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">网上报名</a></td></tr>	<tr><td class="span_list_title"><a href="./?p=uBkbmKcbkSk&amp;a=ubkbmskzkz&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">增报、补报考课程</a></td></tr>	<tr><td class="span_list_title"><a href="./?p=uBkbmInfo&amp;a=uksxxinfo&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">报考状态结果查询</a></td></tr>	<tr><td class="span_list_title"><a href="./?p=uPayMain&amp;a=uddlssk&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">我的订单</a></td></tr></tbody></table></div><div id="span_2" class="span_1b"><table onclick="javascript:jsmenus_onclick(2);" border="0" cellspacing="0" cellpadding="0">	<tbody><tr>		<td class="uml_ico"></td>		<td class="ml_title">准考证考试信息通知单</td>	</tr></tbody></table><table border="0" cellspacing="0" cellpadding="0" class="span_list">	<tbody><tr><td class="span_list_title"><font color="#999999"><a href="./?p=uZwhInfoMain&amp;a=fjzklszkzhzwh&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">考试座位号查询</a></font></td></tr>	<tr><td class="span_list_title"><font color="#999999"><a href="./?p=uZwhInfoMain&amp;a=zwhkddz&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">考点地址参考</a></font></td></tr></tbody></table></div><div id="span_7" class="span_1b"><table onclick="javascript:jsmenus_onclick(7);" border="0" cellspacing="0" cellpadding="0">	<tbody><tr>		<td class="uml_ico"></td>		<td class="ml_title">网上转考</td>	</tr></tbody></table><table border="0" cellspacing="0" cellpadding="0" class="span_list">	<tbody><tr><td class="span_list_title"><font color="#999999"><a href="./?p=uFjzkZkzhList&amp;a=pagezksn&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">网上转考（省内）</a></font></td></tr>	<tr><td class="span_list_title"><font color="#999999"><a href="./?p=uFjzkZkzhList&amp;a=pagezksj&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">省际转考（转出）</a></font></td></tr>	<tr><td class="span_list_title"><font color="#999999"><a href="./?p=uFjzkZkzhList&amp;a=pagezksjzlcx&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">省际转考（转入）查询</a></font></td></tr></tbody></table></div><div id="span_4" class="span_1b"><table onclick="javascript:jsmenus_onclick(4);" border="0" cellspacing="0" cellpadding="0">	<tbody><tr>		<td class="uml_ico"></td>		<td class="ml_title">网上免考</td>	</tr></tbody></table><table border="0" cellspacing="0" cellpadding="0" class="span_list">	<tbody><tr><td class="span_list_title"><font color="#999999"><a href="./?p=uFjzkZkzhList&amp;a=pagezsmk&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">非学历证书免考</a></font></td></tr>	<tr><td class="span_list_title"><font color="#999999"><a href="./?p=uFjzkZkzhList&amp;a=pagekcmk&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">课程免考</a></font></td></tr></tbody></table></div><div id="span_32" class="span_1b"><table onclick="javascript:jsmenus_onclick(32);" border="0" cellspacing="0" cellpadding="0">	<tbody><tr>		<td class="uml_ico"></td>		<td class="ml_title">预毕业申请</td>	</tr></tbody></table><table border="0" cellspacing="0" cellpadding="0" class="span_list">	<tbody><tr><td class="span_list_title"><font color="#999999"><a href="./?p=uFjzkZkzhByList&amp;a=pagebysq&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">预毕业申请</a></font></td></tr>	<tr><td class="span_list_title"><font color="#999999"><a href="./?p=uBiyeQzxlMain&amp;a=qzxllist&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">预毕业前置学历认证</a></font></td></tr></tbody></table></div><div id="span_40" class="span_1b"><table onclick="javascript:jsmenus_onclick(40);" border="0" cellspacing="0" cellpadding="0">	<tbody><tr>		<td class="uml_ico"></td>		<td class="ml_title">成绩查询</td>	</tr></tbody></table><table border="0" cellspacing="0" cellpadding="0" class="span_list">	<tbody><tr><td class="span_list_title"><font color="#999999"><a href="./?p=uFjzkZkzhCjList&amp;a=pagedccj&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">当次成绩查询</a></font></td></tr>	<tr><td class="span_list_title"><font color="#999999"><a href="./?p=uFjzkZkzhCjList&amp;a=pagesjcj&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">实践课成绩查询</a></font></td></tr>	<tr><td class="span_list_title"><font color="#999999"><a href="./?p=uFjzkZkzhCjList&amp;a=pagehzcj&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">历史成绩查询</a></font></td></tr>	<tr><td class="span_list_title"><font color="#999999"><a href="./?p=uFjzkZkzhCjList&amp;a=pagebylwcj&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">毕业论文答辩申请表</a></font></td></tr></tbody></table></div><div id="span_10" class="span_1b"><table border="0" cellspacing="0" cellpadding="0">	<tbody><tr><td class="uml_ico"></td><td class="ml_title">个人信息维护</td></tr></tbody></table><table border="0" cellspacing="0" cellpadding="0" class="span_list">	<tbody><tr><td class="span_list_title"><a href="./?p=uFjzkZkzhList&amp;a=pzkzhgl&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">准考证号管理</a></td></tr>	<tr><td class="span_list_title"><a href="./?p=uFjzkZkzhList&amp;a=pageZkzhYdsqList&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">准考证信息异动</a></td></tr>	<tr><td class="span_list_title"><a href="./?p=uRegInfo&amp;a=preginfo&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">注册信息修改</a></td></tr>	<tr><td class="span_list_title"><a href="./?p=uRegInfo&amp;a=pregpwd&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">登录密码修改</a></td></tr>	<tr><td class="span_list_title"><a href="./?p=uMain&amp;a=out&amp;r=1-WEB83-218-2f8-1131697-1753950588-U">退出</a></td></tr></tbody></table></div></div></div>
			<div class="rightmain">
				<div class="rightmain_dh">
					<div style="line-height: 33px; padding-left: 10px;">您当前的位置：个人报考平台 &gt; 平台首页</div>
					<div style="line-height: 36px; padding-left: 30px; font-weight: bold;">平台首页</div>
				</div>
				<br>
						<div style="text-align:left; padding:8px; font-size:12px;">&nbsp;&nbsp;&nbsp;&nbsp;您好！: 您已经成功登录福建省高等教育自学考试网络报考助学系统。祝您使用愉快！</div><div style="margin:0 auto;border-bottom:#aaaaaa 1px dashed; width:95%"></div><br><div style="margin:0 auto;width:700px;"><div style="background-color:#2170a8;border:#cadae9 1px solid;border-bottom:#cadae9 0px solid;font-weight:bold;font-size:12px;text-align:center;line-height:24px;color:#fff;width:90px;">近期工作</div><div style="background-color:#f9fdff;border:#2170a8 1px solid;font-size:14px;text-align:left;line-height:30px;padding:16px;">2025年上半年自学考试毕业申请时间：5月21日9:00—5月28日15:00<br>“网上转考”及“非学历证书免考”将于5月15日至5月18日暂时关闭进行维护,5月19日至28日将再开启<br></div></div><br><br><div style="margin:0 auto;width:700px;"><div style="background-color:#ddeefe;border:#cadae9 1px solid;border-bottom:#cadae9 0px solid;font-weight:bold;font-size:12px;text-align:center;line-height:24px;color:#2170a8;width:90px;">最新公告</div><div style="background-color:#f9fdff;border:#cadae9 1px solid;font-size:12px;text-align:left;line-height:24px;height:300px;padding:16px;">&nbsp;<strong>[公告]</strong><a target="_blank" href="/2013/20241008zykk.pdf">福建省教育考试院关于同意面向社会开考高等教育自学考试体育教育等3个专业的通知</a><br>&nbsp;<strong>[公告]</strong><a target="_blank" href="https://121.204.170.198:8082/2013/?p=FjzkGg&amp;a=bysc">福建省高等教育自学考试考生网络预申请毕业操作手册</a><br>&nbsp;<strong>[公告]</strong><a target="_blank" href="http://www.eeafj.cn/zkgsgg/20190422/9158.html">关于公布高等教育自学考试部分停考专业课程顶替对照表的通知</a><br>&nbsp;<strong>[公告]</strong><a target="_blank" href="http://www.eeafj.cn/zkgsgg/20190705/9419.html">关于调整高等教育自学考试部分面向社会开考专业主考学校等事宜的通知</a><br>&nbsp;<strong>[公告]</strong><a target="_blank" href="http://www.eeafj.cn/zkgsgg/20170323/6639.html">关于调整非学历证书与自学考试相关课程学分互认及免考办法的通知</a><br>&nbsp;<strong>[公告]</strong><a target="_blank" href="http://www.eeafj.cn/syzxzx/20151110/5313.html">自学考试在籍考生错误信息更改办法</a><br>&nbsp;<strong>[公告]</strong><a target="_blank" href="http://www.eeafj.cn/zkzkzc/20130930/3575.html">福建省自学考试转考工作实施细则</a><br>&nbsp;<strong>[公告]</strong><a target="_blank" href="http://www.eeafj.cn/ffcms/zkzkzc/5490.jhtml">福建省自学考试课程免考实施细则</a><br>&nbsp;<strong>[公告]</strong><a target="_blank" href="http://www.eeafj.cn/zkzkzc/20130930/3534.html">福建省自学考试毕业审核及毕业证书发放实施细则</a><br></div></div><br><br>			<br> <br>
			</div>
		</div>
	</div>
	<div class="bmbox_bottom"></div>
	<!--底部-->
	<div style="margin-top:0px;text-align:center;margin:0 auto;width:960px;background-color:#ADD3DB;">
		<div style="line-height:1.5;text-align:center;padding-top:10px;padding-bottom:10px;font-size:12px;text-align:center;font-family:Arial;">© 2024 福建省高等教育自学考试考务考籍管理信息系统</div>
	</div>

</body></html>