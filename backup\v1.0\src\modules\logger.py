#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统模块
提供统一的日志记录和管理功能
"""

import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
from loguru import logger

from config import CONFIG

# 导入进度管理器
try:
    from core.progress import ProgressManager
    PROGRESS_AVAILABLE = True
except ImportError:
    PROGRESS_AVAILABLE = False
    print("警告: 进度管理器不可用")


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        """初始化日志管理器"""
        self.log_dir = Path(CONFIG["log_dir"])
        self.log_dir.mkdir(parents=True, exist_ok=True)

        self.session_data = {
            "start_time": None,
            "end_time": None,
            "students_processed": [],
            "statistics": {
                "total": 0,
                "success": 0,
                "failed": 0
            }
        }

        # 初始化进度管理器
        self.progress_manager = None
        if PROGRESS_AVAILABLE:
            try:
                self.progress_manager = ProgressManager()
                logger.info("进度管理器初始化成功")
            except Exception as e:
                logger.warning(f"进度管理器初始化失败: {e}")

        self._setup_loggers()
    
    def _setup_loggers(self):
        """设置日志记录器"""
        # 移除默认处理器
        logger.remove()
        
        # 控制台输出
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level=CONFIG["log_level"],
            colorize=True
        )
        
        # 主日志文件
        logger.add(
            self.log_dir / "auto_query.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="INFO",
            rotation=CONFIG["log_rotation"],
            retention=CONFIG["log_retention"],
            encoding="utf-8"
        )
        
        # 错误日志文件
        logger.add(
            self.log_dir / "errors.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="ERROR",
            rotation=CONFIG["log_rotation"],
            retention=CONFIG["log_retention"],
            encoding="utf-8"
        )
        
        # 统计日志文件
        logger.add(
            self.log_dir / "statistics.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {message}",
            level="INFO",
            rotation=CONFIG["log_rotation"],
            retention=CONFIG["log_retention"],
            encoding="utf-8",
            filter=lambda record: "STATS" in record["extra"]
        )
    
    def start_session(self):
        """开始会话记录"""
        self.session_data["start_time"] = datetime.now()
        self.session_data["students_processed"] = []
        self.session_data["statistics"] = {"total": 0, "success": 0, "failed": 0}
        
        logger.info("=" * 60)
        logger.info("开始新的处理会话")
        logger.info(f"会话开始时间: {self.session_data['start_time']}")
        logger.info("=" * 60)
    
    def end_session(self):
        """结束会话记录"""
        self.session_data["end_time"] = datetime.now()
        
        # 计算总耗时
        duration = self.session_data["end_time"] - self.session_data["start_time"]
        
        # 记录会话统计
        logger.info("=" * 60)
        logger.info("处理会话结束")
        logger.info(f"会话结束时间: {self.session_data['end_time']}")
        logger.info(f"总耗时: {duration}")
        logger.info(f"处理总数: {self.session_data['statistics']['total']}")
        logger.info(f"成功数量: {self.session_data['statistics']['success']}")
        logger.info(f"失败数量: {self.session_data['statistics']['failed']}")
        
        if self.session_data['statistics']['total'] > 0:
            success_rate = (self.session_data['statistics']['success'] / self.session_data['statistics']['total']) * 100
            logger.info(f"成功率: {success_rate:.1f}%")
        
        logger.info("=" * 60)
        
        # 保存会话报告
        self._save_session_report()
        
        # 记录统计信息
        self._log_statistics()
    
    def start_student_processing(self, student_name: str, student_id: str):
        """开始学员处理记录"""
        student_data = {
            "name": student_name,
            "id": student_id,
            "start_time": datetime.now(),
            "end_time": None,
            "success": False,
            "error_message": None,
            "processing_time": None
        }
        
        self.session_data["students_processed"].append(student_data)
        self.session_data["statistics"]["total"] += 1
        
        logger.info(f"开始处理学员: {student_name} ({student_id})")
    
    def end_student_processing(self, success: bool, error_message: Optional[str] = None):
        """结束学员处理记录"""
        if not self.session_data["students_processed"]:
            logger.warning("没有正在处理的学员记录")
            return
        
        current_student = self.session_data["students_processed"][-1]
        current_student["end_time"] = datetime.now()
        current_student["success"] = success
        current_student["error_message"] = error_message
        
        # 计算处理时间
        if current_student["start_time"]:
            duration = current_student["end_time"] - current_student["start_time"]
            current_student["processing_time"] = duration.total_seconds()
        
        # 更新统计
        if success:
            self.session_data["statistics"]["success"] += 1
            logger.info(f"学员处理成功: {current_student['name']} (耗时: {current_student['processing_time']:.1f}秒)")
        else:
            self.session_data["statistics"]["failed"] += 1
            logger.error(f"学员处理失败: {current_student['name']} - {error_message}")
    
    def _save_session_report(self):
        """保存会话报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = self.log_dir / f"report_session_{timestamp}.json"
            
            # 准备报告数据
            report_data = {
                "session_info": {
                    "start_time": self.session_data["start_time"].isoformat(),
                    "end_time": self.session_data["end_time"].isoformat(),
                    "duration": str(self.session_data["end_time"] - self.session_data["start_time"])
                },
                "statistics": self.session_data["statistics"],
                "students": []
            }
            
            # 添加学员处理详情
            for student in self.session_data["students_processed"]:
                student_report = {
                    "name": student["name"],
                    "id": student["id"],
                    "start_time": student["start_time"].isoformat(),
                    "end_time": student["end_time"].isoformat() if student["end_time"] else None,
                    "success": student["success"],
                    "error_message": student["error_message"],
                    "processing_time": student["processing_time"]
                }
                report_data["students"].append(student_report)
            
            # 保存报告
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"会话报告已保存: {report_file}")
            
        except Exception as e:
            logger.error(f"保存会话报告失败: {e}")
    
    def _log_statistics(self):
        """记录统计信息"""
        try:
            stats_data = {
                "timestamp": datetime.now().isoformat(),
                "session_duration": str(self.session_data["end_time"] - self.session_data["start_time"]),
                "total_processed": self.session_data["statistics"]["total"],
                "success_count": self.session_data["statistics"]["success"],
                "failed_count": self.session_data["statistics"]["failed"],
                "success_rate": (self.session_data["statistics"]["success"] / self.session_data["statistics"]["total"] * 100) if self.session_data["statistics"]["total"] > 0 else 0
            }
            
            logger.bind(STATS=True).info(f"SESSION_STATS: {json.dumps(stats_data, ensure_ascii=False)}")
            
        except Exception as e:
            logger.error(f"记录统计信息失败: {e}")
    
    def get_session_statistics(self) -> Dict:
        """获取会话统计信息"""
        return self.session_data["statistics"].copy()
    
    def get_failed_students(self) -> List[Dict]:
        """获取失败的学员列表"""
        failed_students = []
        for student in self.session_data["students_processed"]:
            if not student["success"]:
                failed_students.append({
                    "name": student["name"],
                    "id": student["id"],
                    "error": student["error_message"]
                })
        return failed_students

    # ============================================================================
    # 进度管理集成方法
    # ============================================================================

    def start_progress_session(self, total_students: int) -> Optional[str]:
        """
        开始进度管理会话

        Args:
            total_students: 总学员数量

        Returns:
            会话ID（如果进度管理器可用）
        """
        if self.progress_manager:
            try:
                session_id = self.progress_manager.start_session(total_students)
                logger.info(f"进度管理会话开始: {session_id}")
                return session_id
            except Exception as e:
                logger.warning(f"进度管理会话开始失败: {e}")
        return None

    def save_student_progress(self, student_id: str, student_name: str,
                            success: bool, error_message: Optional[str] = None) -> bool:
        """
        保存学员处理进度

        Args:
            student_id: 学员身份证号
            student_name: 学员姓名
            success: 是否处理成功
            error_message: 错误信息（可选）

        Returns:
            是否保存成功
        """
        if self.progress_manager:
            try:
                status = 'completed' if success else 'failed'
                return self.progress_manager.save_student_progress(
                    student_id, student_name, status, error_message
                )
            except Exception as e:
                logger.warning(f"保存学员进度失败: {e}")
        return False

    def end_progress_session(self) -> bool:
        """
        结束进度管理会话

        Returns:
            是否成功结束
        """
        if self.progress_manager:
            try:
                success = self.progress_manager.end_session()
                if success:
                    logger.info("进度管理会话结束")
                return success
            except Exception as e:
                logger.warning(f"进度管理会话结束失败: {e}")
        return False

    def filter_remaining_students(self, all_students: List[Dict]) -> List[Dict]:
        """
        过滤出尚未处理的学员

        Args:
            all_students: 所有学员列表

        Returns:
            尚未处理的学员列表
        """
        if self.progress_manager:
            try:
                return self.progress_manager.filter_remaining_students(all_students)
            except Exception as e:
                logger.warning(f"过滤学员列表失败: {e}")
        return all_students

    def has_unfinished_progress(self) -> bool:
        """
        检查是否有未完成的进度

        Returns:
            是否有未完成的进度
        """
        if self.progress_manager:
            try:
                return self.progress_manager.has_unfinished_session()
            except Exception as e:
                logger.warning(f"检查未完成进度失败: {e}")
        return False

    def get_progress_statistics(self) -> Dict:
        """
        获取进度统计信息

        Returns:
            统计信息字典
        """
        if self.progress_manager:
            try:
                return self.progress_manager.get_statistics()
            except Exception as e:
                logger.warning(f"获取进度统计失败: {e}")
        return {}

    def clear_progress(self) -> bool:
        """
        清除进度数据

        Returns:
            是否清除成功
        """
        if self.progress_manager:
            try:
                success = self.progress_manager.clear_progress()
                if success:
                    logger.info("进度数据已清除")
                return success
            except Exception as e:
                logger.warning(f"清除进度数据失败: {e}")
        return False


# 全局日志管理器实例
_logger_manager = None


def get_logger_manager() -> LoggerManager:
    """获取日志管理器实例"""
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = LoggerManager()
    return _logger_manager


# 便捷日志函数
def log_info(message: str, category: str = "INFO"):
    """记录信息日志"""
    logger.info(f"[{category}] {message}")


def log_success(message: str, category: str = "SUCCESS"):
    """记录成功日志"""
    logger.success(f"[{category}] {message}")


def log_warning(message: str, category: str = "WARNING"):
    """记录警告日志"""
    logger.warning(f"[{category}] {message}")


def log_error(category: str, error: Exception):
    """记录错误日志"""
    logger.error(f"[{category}] {type(error).__name__}: {str(error)}")


def log_debug(message: str, category: str = "DEBUG"):
    """记录调试日志"""
    logger.debug(f"[{category}] {message}")


def log_critical(message: str, category: str = "CRITICAL"):
    """记录严重错误日志"""
    logger.critical(f"[{category}] {message}")


# 装饰器
def log_function_call(func):
    """函数调用日志装饰器"""
    def wrapper(*args, **kwargs):
        logger.debug(f"调用函数: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    return wrapper
