#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自考成绩查询自动化系统配置文件
现在使用新的ConfigManager系统，支持配置文件和环境变量
"""

from pathlib import Path

# 导入新的配置管理系统
from config import CONFIG, SELECTORS, get_config_manager, update_config, reload_config

# ============================================================================
# 配置使用说明
# ============================================================================

# 1. 默认配置
#    所有默认配置在 config/defaults.py 中定义
#    包括网站URL、浏览器设置、重试配置、目录配置等

# 2. 配置文件覆盖
#    您可以创建以下配置文件来覆盖默认配置：
#    - config.yaml (推荐)
#    - config.json
#    - config.yml
#    
#    配置文件示例请参考项目根目录的 config.yaml

# 3. 环境变量覆盖（最高优先级）
#    您可以通过环境变量覆盖任何配置项：
#    
#    EXAM_HEADLESS=true          # 无头模式
#    EXAM_MAX_RETRIES=5          # 最大重试次数
#    EXAM_LOG_LEVEL=DEBUG        # 日志级别
#    EXAM_SCREENSHOT_DIR=/path   # 截图目录
#    
#    更多环境变量请参考 config/defaults.py 中的 ENV_MAPPINGS

# 4. 程序中使用配置
#    # 原有方式（向后兼容）
#    from config import CONFIG
#    url = CONFIG["login_url"]
#    
#    # 新方式
#    from config import get_config_manager
#    manager = get_config_manager()
#    url = manager.get("login_url")
#    
#    # 动态更新配置
#    from config import update_config
#    update_config({"headless": True})

# ============================================================================
# 实用函数
# ============================================================================

def get_config(key, default=None):
    """
    获取配置值的便捷函数
    
    Args:
        key: 配置键名
        default: 默认值
        
    Returns:
        配置值
    """
    return CONFIG.get(key, default)

def get_selector(key, default=None):
    """
    获取选择器配置的便捷函数
    
    Args:
        key: 选择器键名
        default: 默认值
        
    Returns:
        选择器列表
    """
    if default is None:
        default = []
    return SELECTORS.get(key, default)

def is_headless():
    """检查是否为无头模式"""
    return CONFIG.get("headless", False)

def get_timeout(timeout_type="page"):
    """
    获取超时配置
    
    Args:
        timeout_type: 超时类型 ("page", "navigation", "element")
        
    Returns:
        超时时间（毫秒）
    """
    timeout_key = f"{timeout_type}_timeout"
    return CONFIG.get(timeout_key, 30000)

def get_retry_config():
    """
    获取重试配置
    
    Returns:
        重试配置字典
    """
    return {
        "max_retries": CONFIG.get("max_retries", 3),
        "retry_delay": CONFIG.get("retry_delay", 2.0),
        "captcha_retries": CONFIG.get("captcha_retries", 3)
    }

# ============================================================================
# 目录初始化
# ============================================================================

def ensure_directories():
    """确保必要目录存在"""
    try:
        screenshot_dir = Path(CONFIG.get("screenshot_dir", "output/screenshots"))
        log_dir = Path(CONFIG.get("log_dir", "output/logs"))
        captcha_save_path = Path(CONFIG.get("captcha_save_path", "output/logs/captcha_debug"))
        
        screenshot_dir.mkdir(parents=True, exist_ok=True)
        log_dir.mkdir(parents=True, exist_ok=True)
        captcha_save_path.mkdir(parents=True, exist_ok=True)
        
        print(f"目录初始化完成:")
        print(f"  截图目录: {screenshot_dir}")
        print(f"  日志目录: {log_dir}")
        print(f"  验证码调试目录: {captcha_save_path}")
    except Exception as e:
        print(f"目录初始化失败: {e}")

# 初始化时创建目录
ensure_directories()

# ============================================================================
# 向后兼容性说明
# ============================================================================

# CONFIG 和 SELECTORS 现在是智能字典，支持：
# - 默认配置
# - 配置文件覆盖
# - 环境变量覆盖
# - 向后兼容的字典访问方式

# 使用示例：
# headless_mode = CONFIG["headless"]
# login_selectors = SELECTORS["username_input"]

# 如果您需要查看当前的所有配置，可以使用：
# print(CONFIG.to_dict())  # 查看所有配置
# print(dict(SELECTORS))   # 查看所有选择器
