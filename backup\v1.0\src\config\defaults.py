#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
默认配置定义
"""

from pathlib import Path

# 默认配置字典
DEFAULT_CONFIG = {
    # 网站配置
    "login_url": "https://121.204.170.198:8082/zk/reg/v3/?rlx=1752850109&p=FIXED06FA3SKREG",
    
    # 浏览器配置
    "headless": False,              # 是否无头模式
    "slow_mo": 500,                # 操作间隔(毫秒)
    "page_timeout": 30000,         # 页面超时(毫秒)
    "navigation_timeout": 20000,   # 导航超时(毫秒)
    "element_timeout": 10000,      # 元素等待超时(毫秒)
    
    # 重试配置
    "max_retries": 3,              # 最大重试次数
    "retry_delay": 2.0,            # 重试间隔(秒)
    "captcha_retries": 3,          # 验证码重试次数
    
    # 目录配置
    "screenshot_dir": Path("output/screenshots"),
    "log_dir": Path("output/logs"),
    "data_file": "data/students.csv",
    
    # 日志配置
    "log_level": "INFO",
    "log_rotation": "10 MB",
    "log_retention": "7 days",
    
    # 验证码配置
    "captcha_debug": True,         # 验证码调试模式
    "captcha_save_path": Path("../output/logs/captcha_debug"),
    
    # 截图配置
    "screenshot_quality": 90,      # 截图质量
    "screenshot_format": "png",    # 截图格式
    "viewport_width": 1920,        # 视口宽度
    "viewport_height": 1080,       # 视口高度
}

# 默认选择器配置
DEFAULT_SELECTORS = {
    # 登录页面选择器
    "username_input": [
        "input[name='myname']",
        "#myname",
        "input[name='username']",
        "input[id='username']",
        "#username",
        "input[placeholder*='身份证']",
        "input[placeholder*='用户名']"
    ],
    
    "password_input": [
        "input[name='mypwd']",
        "#mypwd",
        "input[name='password']",
        "input[id='password']",
        "#password",
        "input[type='password']"
    ],
    
    "captcha_input": [
        "input[name='verifycode']",
        "#verifycode",
        "input[name='captcha']",
        "input[id='captcha']",
        "#captcha",
        "input[placeholder*='验证码']"
    ],
    
    "captcha_image": [
        "#seccodeimg",
        "img[src*='captcha']",
        "img[alt*='验证码']",
        "#captcha_img",
        ".captcha-img"
    ],
    
    "login_button": [
        "#loginBtn",
        ".loginBtn",
        "button[type='submit']",
        "input[type='submit']",
        ".login-btn",
        "#login_btn"
    ],
    
    # 导航选择器
    "score_query_menu": [
        "text=当次成绩查询",
        "//a[contains(text(), '当次成绩查询')]",
        "//td[contains(text(), '当次成绩查询')]",
        "//span[contains(text(), '当次成绩查询')]",
        "[title*='当次成绩查询']",
        "[alt*='当次成绩查询']"
    ],

    "query_options": [
        "text=点击进入",
        "//a[contains(text(), '点击进入')]",
        "//button[contains(text(), '点击进入')]",
        "//input[contains(@value, '点击进入')]",
        "[title*='点击进入']",
        "[alt*='点击进入']"
    ],

    "menu_items": [
        "text=当次成绩查询",
        "//a[contains(text(), '当次成绩查询')]",
        "[title*='成绩查询']",
        "//li[contains(text(), '当次成绩查询')]"
    ],
    
    # 成绩页面选择器
    "score_table": [
        "table",
        ".score-table",
        "#score_table",
        "[class*='table']"
    ],

    "score_container": [
        ".content",
        ".main-content",
        "#main",
        ".score-content"
    ],

    # 密码修改页面选择器
    "password_change_indicators": [
        "text=修改密码",
        "text=密码修改",
        "text=修改登录密码",
        "text=密码即将过期",
        "text=请修改密码",
        "//text()[contains(., '修改密码')]",
        "//text()[contains(., '密码修改')]",
        "//text()[contains(., '修改登录密码')]",
        "//text()[contains(., '密码即将过期')]",
        "//text()[contains(., '请修改密码')]",
        "//div[contains(text(), '修改密码')]",
        "//span[contains(text(), '修改密码')]",
        "//h1[contains(text(), '修改密码')]",
        "//h2[contains(text(), '修改密码')]",
        "//h3[contains(text(), '修改密码')]",
        "[title*='修改密码']",
        "[alt*='修改密码']"
    ],

    "old_password_input": [
        "input[name='oldpwd']",
        "input[name='old_password']",
        "input[name='oldPassword']",
        "input[id='oldpwd']",
        "input[id='old_password']",
        "input[id='oldPassword']",
        "#oldpwd",
        "#old_password",
        "#oldPassword",
        "input[placeholder*='原密码']",
        "input[placeholder*='旧密码']",
        "input[placeholder*='当前密码']",
        "input[placeholder*='输入原密码']",
        "input[type='password'][name*='old']",
        "input[type='password'][id*='old']"
    ],

    "new_password_input": [
        "input[name='newpwd']",
        "input[name='new_password']",
        "input[name='newPassword']",
        "input[name='mypwd']",
        "input[id='newpwd']",
        "input[id='new_password']",
        "input[id='newPassword']",
        "#newpwd",
        "#new_password",
        "#newPassword",
        "input[placeholder*='新密码']",
        "input[placeholder*='输入新密码']",
        "input[placeholder*='请输入新密码']",
        "input[type='password'][name*='new']",
        "input[type='password'][id*='new']"
    ],

    "confirm_password_input": [
        "input[name='confirmpwd']",
        "input[name='confirm_password']",
        "input[name='confirmPassword']",
        "input[name='repwd']",
        "input[name='re_password']",
        "input[id='confirmpwd']",
        "input[id='confirm_password']",
        "input[id='confirmPassword']",
        "input[id='repwd']",
        "input[id='re_password']",
        "#confirmpwd",
        "#confirm_password",
        "#confirmPassword",
        "#repwd",
        "#re_password",
        "input[placeholder*='确认密码']",
        "input[placeholder*='再次输入']",
        "input[placeholder*='重复密码']",
        "input[placeholder*='再次输入密码']",
        "input[type='password'][name*='confirm']",
        "input[type='password'][id*='confirm']",
        "input[type='password'][name*='re']",
        "input[type='password'][id*='re']"
    ],

    "change_password_button": [
        "input[value*='修改密码']",
        "input[value*='修改登录密码']",
        "input[value*='确认修改']",
        "input[value*='提交']",
        "button:has-text('修改密码')",
        "button:has-text('修改登录密码')",
        "button:has-text('确认修改')",
        "button:has-text('提交')",
        "//button[contains(text(), '修改密码')]",
        "//button[contains(text(), '修改登录密码')]",
        "//button[contains(text(), '确认修改')]",
        "//input[contains(@value, '修改密码')]",
        "//input[contains(@value, '修改登录密码')]",
        "//input[contains(@value, '确认修改')]",
        "#changePasswordBtn",
        "#modifyPasswordBtn",
        ".change-password-btn",
        ".modify-password-btn",
        "[onclick*='changePassword']",
        "[onclick*='modifyPassword']"
    ]
}

# 环境变量映射配置
ENV_MAPPINGS = {
    # 网站配置
    'EXAM_LOGIN_URL': 'login_url',
    
    # 浏览器配置
    'EXAM_HEADLESS': 'headless',
    'EXAM_SLOW_MO': 'slow_mo',
    'EXAM_PAGE_TIMEOUT': 'page_timeout',
    'EXAM_NAVIGATION_TIMEOUT': 'navigation_timeout',
    'EXAM_ELEMENT_TIMEOUT': 'element_timeout',
    
    # 重试配置
    'EXAM_MAX_RETRIES': 'max_retries',
    'EXAM_RETRY_DELAY': 'retry_delay',
    'EXAM_CAPTCHA_RETRIES': 'captcha_retries',
    
    # 目录配置
    'EXAM_SCREENSHOT_DIR': 'screenshot_dir',
    'EXAM_LOG_DIR': 'log_dir',
    'EXAM_DATA_FILE': 'data_file',
    
    # 日志配置
    'EXAM_LOG_LEVEL': 'log_level',
    'EXAM_LOG_ROTATION': 'log_rotation',
    'EXAM_LOG_RETENTION': 'log_retention',
    
    # 验证码配置
    'EXAM_CAPTCHA_DEBUG': 'captcha_debug',
    'EXAM_CAPTCHA_SAVE_PATH': 'captcha_save_path',
    
    # 截图配置
    'EXAM_SCREENSHOT_QUALITY': 'screenshot_quality',
    'EXAM_SCREENSHOT_FORMAT': 'screenshot_format',
    'EXAM_VIEWPORT_WIDTH': 'viewport_width',
    'EXAM_VIEWPORT_HEIGHT': 'viewport_height',
}

# 配置类型转换映射
TYPE_CONVERTERS = {
    'headless': lambda x: str(x).lower() in ('true', '1', 'yes', 'on'),
    'slow_mo': int,
    'page_timeout': int,
    'navigation_timeout': int,
    'element_timeout': int,
    'max_retries': int,
    'retry_delay': float,
    'captcha_retries': int,
    'screenshot_dir': Path,
    'log_dir': Path,
    'captcha_save_path': Path,
    'captcha_debug': lambda x: str(x).lower() in ('true', '1', 'yes', 'on'),
    'screenshot_quality': int,
    'viewport_width': int,
    'viewport_height': int,
}
