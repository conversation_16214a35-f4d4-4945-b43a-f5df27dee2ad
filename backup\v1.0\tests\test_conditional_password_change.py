#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
条件性密码修改测试程序
基于主程序架构实现完整的自动化测试，包含智能密码修改检测和处理
"""

import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent / "src"))

from modules.data import load_students_data
from modules.browser import BrowserManager
from modules.login import LoginManager
from modules.navigation import navigate_after_login
from modules.screenshot import capture_student_screenshot
from modules.password_change import PasswordChangeHandler
from modules.logger import get_logger_manager, log_info, log_success, log_error, log_warning
from config import CONFIG


class ConditionalPasswordChangeTestSuite:
    """条件性密码修改测试套件 - 基于主程序架构的完整实现"""
    
    def __init__(self, data_file: str = "data/students.csv"):
        """
        初始化测试套件
        
        Args:
            data_file: 测试数据文件路径
        """
        self.data_file = data_file
        self.logger_manager = get_logger_manager()
        self.students = []
        self.test_results = {
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "password_change_triggered": 0,
            "password_change_skipped": 0,
            "errors": [],
            "start_time": None,
            "end_time": None,
            "details": []
        }
        
        log_info("条件性密码修改测试套件初始化完成", "测试系统")
    
    def load_test_data(self) -> bool:
        """加载测试数据"""
        log_info(f"加载测试数据文件: {self.data_file}", "数据加载")
        
        try:
            self.students = load_students_data(self.data_file)
            
            if not self.students:
                log_error("数据加载", Exception("未能加载任何测试数据"))
                return False
            
            self.test_results["total_tests"] = len(self.students)
            log_success(f"成功加载 {len(self.students)} 条测试数据", "数据加载")
            
            # 显示测试数据概览
            print(f"\n📋 测试数据概览 ({len(self.students)} 条):")
            for i, student in enumerate(self.students[:3], 1):  # 只显示前3条
                print(f"   {i}. {student['姓名']} ({student['身份证号']})")
            if len(self.students) > 3:
                print(f"   ... 还有 {len(self.students) - 3} 条数据")
            
            return True
            
        except Exception as e:
            log_error("数据加载", e)
            return False

    def test_single_student_with_conditional_password_change(self, student: Dict, index: int, browser_manager: BrowserManager) -> Tuple[bool, str, Dict]:
        """
        测试单个学员的完整流程（包含条件性密码修改）
        
        Args:
            student: 学员信息字典
            index: 学员索引
            browser_manager: 浏览器管理器实例
            
        Returns:
            (是否成功, 错误信息, 详细结果)
        """
        student_name = student["姓名"]
        username = student["身份证号"]
        password = student["密码"]
        
        log_info(f"开始测试学员 {index}/{self.test_results['total_tests']}: {student_name}", "学员测试")
        
        # 详细结果记录
        detail_result = {
            "index": index,
            "name": student_name,
            "id": username,
            "success": False,
            "password_change_detected": False,
            "password_change_success": False,
            "error": None,
            "processing_time": 0,
            "steps_completed": []
        }
        
        start_time = time.time()
        page = None
        
        try:
            # 步骤1: 执行登录（包含条件性密码修改检测）
            log_info("步骤1: 执行自动登录（含密码修改检测）", "学员测试")
            success, error_msg, page = self._execute_login_with_conditional_password_change(
                student, browser_manager, detail_result
            )
            
            if not success:
                detail_result["error"] = error_msg
                detail_result["processing_time"] = time.time() - start_time
                return False, error_msg, detail_result
            
            detail_result["steps_completed"].append("登录成功")
            
            # 步骤2: 执行页面导航
            log_info("步骤2: 执行页面导航", "学员测试")
            success, error_msg = self._execute_navigation_step(page)
            
            if not success:
                self._cleanup_resources(page, browser_manager)
                detail_result["error"] = error_msg
                detail_result["processing_time"] = time.time() - start_time
                return False, error_msg, detail_result
            
            detail_result["steps_completed"].append("页面导航成功")
            
            # 步骤3: 执行截图
            log_info("步骤3: 执行成绩单截图", "学员测试")
            success, error_msg, screenshot_path = self._execute_screenshot_step(page, student_name)
            
            if not success:
                self._cleanup_resources(page, browser_manager)
                detail_result["error"] = error_msg
                detail_result["processing_time"] = time.time() - start_time
                return False, error_msg, detail_result
            
            detail_result["steps_completed"].append(f"截图成功: {screenshot_path}")
            
            # 步骤4: 清理资源
            self._cleanup_resources(page, browser_manager)
            
            # 测试成功
            detail_result["success"] = True
            detail_result["processing_time"] = time.time() - start_time
            
            log_success(f"学员 {student_name} 测试完成", "学员测试")
            return True, "测试成功", detail_result
            
        except Exception as e:
            error_message = f"测试异常: {str(e)}"
            log_error("学员测试", e)
            
            # 确保资源清理
            self._cleanup_resources(page, browser_manager)
            
            detail_result["error"] = error_message
            detail_result["processing_time"] = time.time() - start_time
            
            return False, error_message, detail_result

    def _execute_login_with_conditional_password_change(self, student: Dict, browser_manager: BrowserManager, detail_result: Dict) -> Tuple[bool, str, Optional[object]]:
        """
        执行登录步骤（包含条件性密码修改处理）
        
        Args:
            student: 学员信息字典
            browser_manager: 浏览器管理器实例
            detail_result: 详细结果记录
            
        Returns:
            (是否成功, 错误信息, 页面对象)
        """
        try:
            student_name = student["姓名"]
            username = student["身份证号"]
            password = student["密码"]
            
            log_info("执行自动登录（含条件性密码修改）", "登录流程")
            
            # 使用主程序的LoginManager，它已经集成了条件性密码修改逻辑
            login_manager = LoginManager(browser_manager)
            success, error_msg, page = login_manager.perform_login(username, password, max_retries=3)
            
            if not success:
                error_message = f"登录失败: {error_msg}"
                log_error("登录流程", Exception(error_message))
                return False, error_message, None
            
            # 检查登录结果中是否包含密码修改信息
            if "密码修改" in error_msg:
                detail_result["password_change_detected"] = True
                if "成功" in error_msg:
                    detail_result["password_change_success"] = True
                    log_success(f"学员 {student_name} 登录成功，已自动处理密码修改", "登录流程")
                else:
                    detail_result["password_change_success"] = False
                    log_warning(f"学员 {student_name} 登录成功，但密码修改处理失败", "登录流程")
            else:
                detail_result["password_change_detected"] = False
                log_info(f"学员 {student_name} 登录成功，无需密码修改", "登录流程")
            
            log_success("登录流程完成", "登录流程")
            return True, "登录成功", page
            
        except Exception as e:
            error_message = f"登录步骤异常: {str(e)}"
            log_error("登录流程", e)
            return False, error_message, None

    def _execute_navigation_step(self, page) -> Tuple[bool, str]:
        """执行页面导航步骤"""
        try:
            log_info("执行页面导航", "导航流程")
            if not navigate_after_login(page):
                error_message = "页面导航失败"
                log_error("导航流程", Exception(error_message))
                return False, error_message
            
            log_success("页面导航成功", "导航流程")
            return True, "导航成功"
            
        except Exception as e:
            error_message = f"导航步骤异常: {str(e)}"
            log_error("导航流程", e)
            return False, error_message

    def _execute_screenshot_step(self, page, student_name: str) -> Tuple[bool, str, Optional[str]]:
        """执行截图步骤"""
        try:
            log_info("执行成绩单截图", "截图流程")
            screenshot_path = capture_student_screenshot(page, student_name)
            
            if not screenshot_path:
                error_message = "成绩单截图失败"
                log_error("截图流程", Exception(error_message))
                return False, error_message, None
            
            log_success(f"成绩单截图成功: {screenshot_path}", "截图流程")
            return True, "截图成功", screenshot_path
            
        except Exception as e:
            error_message = f"截图步骤异常: {str(e)}"
            log_error("截图流程", e)
            return False, error_message, None

    def _cleanup_resources(self, page, browser_manager: BrowserManager) -> None:
        """清理资源"""
        try:
            if page:
                browser_manager.close_page(page)
                log_info("页面资源清理完成", "资源清理")
        except Exception as e:
            log_warning(f"页面资源清理异常: {e}", "资源清理")

    def run_batch_test(self) -> bool:
        """运行批量测试"""
        log_info("开始批量条件性密码修改测试", "批量测试")

        self.test_results["start_time"] = datetime.now()

        print(f"\n🚀 开始批量测试 {self.test_results['total_tests']} 名学员...")
        print("=" * 60)

        # 创建单一浏览器实例
        browser_manager = BrowserManager()

        print(f"\n🌐 初始化浏览器...")
        if not browser_manager.init_browser():
            log_error("批量测试", Exception("浏览器初始化失败"))
            print("❌ 浏览器初始化失败，无法继续测试")
            return False

        log_success("浏览器初始化成功", "批量测试")
        print("✅ 浏览器初始化成功")

        try:
            for i, student in enumerate(self.students, 1):
                student_name = student["姓名"]

                print(f"\n📝 测试第 {i}/{self.test_results['total_tests']} 名学员: {student_name}")
                print(f"   身份证号: {student['身份证号']}")

                # 显示进度
                progress = (i / self.test_results['total_tests']) * 100
                print(f"   进度: {progress:.1f}% ({i}/{self.test_results['total_tests']})")

                # 检查浏览器健康状态
                if not browser_manager.is_browser_alive():
                    log_warning(f"浏览器实例失效，尝试重新初始化", "批量测试")
                    print("   ⚠️ 浏览器实例失效，尝试重新初始化...")

                    browser_manager.close()
                    browser_manager = BrowserManager()

                    if not browser_manager.init_browser():
                        log_error("批量测试", Exception("浏览器重新初始化失败"))
                        print("   ❌ 浏览器重新初始化失败，停止测试")
                        break

                    log_success("浏览器重新初始化成功", "批量测试")
                    print("   ✅ 浏览器重新初始化成功")

                # 执行单个学员测试
                start_time = time.time()
                success, error_msg, detail_result = self.test_single_student_with_conditional_password_change(
                    student, i, browser_manager
                )
                processing_time = time.time() - start_time

                # 记录结果
                self.test_results["details"].append(detail_result)

                if success:
                    self.test_results["passed"] += 1
                    print(f"   ✅ 成功 (耗时: {processing_time:.1f}秒)")

                    # 统计密码修改情况
                    if detail_result["password_change_detected"]:
                        self.test_results["password_change_triggered"] += 1
                        if detail_result["password_change_success"]:
                            print(f"   🔑 已自动处理密码修改")
                        else:
                            print(f"   ⚠️ 密码修改处理失败，但测试继续")
                    else:
                        self.test_results["password_change_skipped"] += 1
                        print(f"   ℹ️ 无需密码修改")
                else:
                    self.test_results["failed"] += 1
                    self.test_results["errors"].append({
                        "student": student_name,
                        "error": error_msg
                    })
                    print(f"   ❌ 失败: {error_msg} (耗时: {processing_time:.1f}秒)")

                # 测试间隔
                if i < self.test_results['total_tests']:
                    print("   ⏳ 等待2秒后测试下一名学员...")
                    time.sleep(2)

        finally:
            # 确保浏览器正确关闭
            try:
                browser_manager.close()
                log_success("浏览器已关闭", "批量测试")
                print("\n🌐 浏览器已关闭")
            except Exception as e:
                log_warning(f"浏览器关闭异常: {e}", "批量测试")
                print(f"\n⚠️ 浏览器关闭异常: {e}")

        # 结束测试
        self.test_results["end_time"] = datetime.now()

        # 显示最终结果
        self._display_test_results()

        # 计算成功率
        success_rate = (self.test_results["passed"] / self.test_results["total_tests"]) * 100

        log_success(f"批量测试完成，成功率: {success_rate:.1f}%", "批量测试")

        return success_rate >= 90.0  # 要求90%以上成功率

    def _display_test_results(self):
        """显示测试结果"""
        total_time = self.test_results["end_time"] - self.test_results["start_time"]
        success_rate = (self.test_results["passed"] / self.test_results["total_tests"]) * 100

        print("\n" + "=" * 60)
        print("📊 条件性密码修改测试结果统计")
        print("=" * 60)
        print(f"测试时间: {self.test_results['start_time'].strftime('%Y-%m-%d %H:%M:%S')} - {self.test_results['end_time'].strftime('%H:%M:%S')}")
        print(f"总耗时: {total_time}")
        print(f"测试总数: {self.test_results['total_tests']} 名学员")
        print(f"成功数量: {self.test_results['passed']} 名")
        print(f"失败数量: {self.test_results['failed']} 名")
        print(f"成功率: {success_rate:.1f}%")
        print()
        print("🔑 密码修改统计:")
        print(f"   触发密码修改: {self.test_results['password_change_triggered']} 名学员")
        print(f"   跳过密码修改: {self.test_results['password_change_skipped']} 名学员")

        # 显示密码修改详情
        if self.test_results["password_change_triggered"] > 0:
            print(f"\n🔑 密码修改详情:")
            for detail in self.test_results["details"]:
                if detail["password_change_detected"]:
                    status = "✅ 成功" if detail["password_change_success"] else "❌ 失败"
                    print(f"   - {detail['name']} ({detail['id']}): {status}")

        # 显示失败详情
        if self.test_results["failed"] > 0:
            print(f"\n❌ 失败学员详情:")
            for error in self.test_results["errors"]:
                print(f"   - {error['student']}: {error['error']}")

        # 显示截图目录
        screenshot_dir = Path(CONFIG["screenshot_dir"])
        if screenshot_dir.exists():
            screenshots = list(screenshot_dir.glob("*.png"))
            print(f"\n📸 生成截图: {len(screenshots)} 个文件")
            print(f"📁 截图目录: {screenshot_dir}")

        print("=" * 60)

    def run(self) -> bool:
        """运行测试程序"""
        try:
            print("🎯 条件性密码修改自动化测试程序")
            print("=" * 50)

            # 加载测试数据
            if not self.load_test_data():
                return False

            # 确认开始测试
            print(f"\n⚠️  即将开始批量测试 {self.test_results['total_tests']} 名学员")
            print("   测试将自动进行以下操作：")
            print("   1. 自动登录福建省自考系统")
            print("   2. 智能检测是否需要修改密码")
            print("   3. 如需修改密码，自动完成密码修改流程")
            print("   4. 导航到成绩查询页面")
            print("   5. 截图保存成绩单")

            confirm = input("\n是否继续？(y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ 用户取消操作")
                return False

            # 执行批量测试
            return self.run_batch_test()

        except KeyboardInterrupt:
            print("\n\n⚠️ 用户中断操作")
            log_warning("用户中断批量测试", "系统控制")
            return False
        except Exception as e:
            print(f"\n❌ 系统异常: {e}")
            log_error("系统运行", e)
            return False


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="条件性密码修改自动化测试程序")
    parser.add_argument("--data", "-d", default="data/students.csv", help="学员数据文件路径")
    parser.add_argument("--headless", action="store_true", help="使用无头模式")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")

    args = parser.parse_args()

    # 更新配置
    if args.headless:
        CONFIG["headless"] = True

    if args.debug:
        CONFIG["log_level"] = "DEBUG"

    # 创建并运行测试套件
    test_suite = ConditionalPasswordChangeTestSuite(args.data)
    success = test_suite.run()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
