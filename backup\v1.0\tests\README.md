# 条件性密码修改功能测试文档

## 📋 概述

本测试套件专门用于验证自考成绩查询系统中的**条件性密码修改功能**。该功能能够智能检测登录后是否出现密码修改提示，并自动完成密码修改流程。

## 🎯 功能特点

### 核心功能
- ✅ **智能检测**：自动识别密码修改页面/弹窗
- ✅ **条件性处理**：仅在需要时执行密码修改
- ✅ **自动填写**：使用CSV中的密码自动填写新密码
- ✅ **无缝集成**：完全集成到现有登录流程中
- ✅ **详细日志**：记录每个步骤的执行情况

### 测试覆盖
- 🔍 密码修改页面检测准确性
- 🔄 自动密码修改流程完整性
- ⏭️ 无密码修改时的跳过逻辑
- 📊 批量处理的稳定性
- 🛡️ 异常情况的处理能力

## 📁 文件结构

```
tests/
├── README.md                           # 本文档
├── test_conditional_password_change.py # 完整批量测试程序
├── quick_password_test.py              # 快速功能测试程序
└── run_password_tests.py               # 测试运行器（推荐使用）
```

## 🚀 快速开始

### 方法一：使用测试运行器（推荐）

```bash
# 在项目根目录运行
python run_password_tests.py
```

然后根据菜单选择测试模式：
- **快速测试**：验证前3名学员的密码修改功能
- **交互式测试**：自定义测试数量和参数
- **完整测试**：测试所有学员并生成详细报告
- **自定义测试**：完全自定义的测试配置

### 方法二：直接运行测试脚本

#### 快速功能测试
```bash
# 测试前3名学员（默认）
python tests/quick_password_test.py

# 测试前5名学员
python tests/quick_password_test.py --count 5

# 交互式模式
python tests/quick_password_test.py --interactive

# 无头模式
python tests/quick_password_test.py --headless
```

#### 完整批量测试
```bash
# 测试所有学员
python tests/test_conditional_password_change.py

# 使用自定义数据文件
python tests/test_conditional_password_change.py --data data/test_students.csv

# 无头模式
python tests/test_conditional_password_change.py --headless

# 调试模式
python tests/test_conditional_password_change.py --debug
```

## 📊 测试结果说明

### 快速测试结果
```
📊 快速测试结果
========================================
测试总数: 3 名学员
成功登录: 3 名
登录失败: 0 名
成功率: 100.0%

🔑 密码修改功能:
检测到密码修改: 1 名学员
密码修改成功: 1 名学员
密码修改成功率: 100.0%
========================================
```

### 完整测试结果
```
📊 条件性密码修改测试结果统计
============================================================
测试时间: 2024-01-15 14:30:00 - 14:35:30
总耗时: 0:05:30
测试总数: 10 名学员
成功数量: 9 名
失败数量: 1 名
成功率: 90.0%

🔑 密码修改统计:
   触发密码修改: 3 名学员
   跳过密码修改: 6 名学员

🔑 密码修改详情:
   - 张三 (350123199001011234): ✅ 成功
   - 李四 (350123199002022345): ✅ 成功
   - 王五 (350123199003033456): ❌ 失败

📸 生成截图: 9 个文件
📁 截图目录: screenshots
============================================================
```

## 🔧 配置说明

### 密码修改相关配置（config.yaml）
```yaml
password_change:
  new_password_selector: "#mypwd"      # 新密码输入框
  confirm_password_selector: "#mypwd2" # 确认密码输入框
  submit_button_selector: "#btnsubmit" # 提交按钮
  detection_keywords:                  # 检测关键词
    - "修改登录密码"
    - "密码修改"
    - "更改密码"
  max_wait_time: 10                   # 最大等待时间（秒）
  retry_count: 3                      # 重试次数
```

## 🧪 测试场景

### 场景1：需要密码修改
1. 学员登录成功
2. 系统检测到"修改登录密码"页面
3. 自动填写新密码（使用CSV中的密码）
4. 点击提交按钮
5. 密码修改成功，继续后续流程

### 场景2：无需密码修改
1. 学员登录成功
2. 系统未检测到密码修改页面
3. 直接跳过密码修改步骤
4. 继续正常业务流程

### 场景3：密码修改失败
1. 学员登录成功
2. 检测到密码修改页面
3. 尝试自动修改密码
4. 修改失败，记录错误信息
5. 根据配置决定是否继续或终止

## 📝 日志说明

### 日志级别
- **INFO**：一般信息，如步骤开始/完成
- **SUCCESS**：成功操作，如登录成功、密码修改成功
- **WARNING**：警告信息，如密码修改失败但测试继续
- **ERROR**：错误信息，如登录失败、系统异常

### 关键日志示例
```
[INFO] 学员测试 - 开始测试学员 1/10: 张三
[INFO] 登录流程 - 执行自动登录（含条件性密码修改）
[SUCCESS] 登录流程 - 学员 张三 登录成功，已自动处理密码修改
[SUCCESS] 导航流程 - 页面导航成功
[SUCCESS] 截图流程 - 成绩单截图成功: screenshots/张三_成绩单_20240115_143000.png
```

## ⚠️ 注意事项

### 使用前准备
1. 确保 `data/students.csv` 文件存在且格式正确
2. 确保网络连接正常
3. 确保系统时间正确（影响截图文件名）

### 测试建议
1. **首次使用**：建议先运行快速测试验证功能
2. **生产环境**：使用无头模式提高效率
3. **调试问题**：使用调试模式获取详细信息
4. **大批量测试**：建议分批进行，避免系统负载过高

### 常见问题
1. **浏览器启动失败**：检查Chrome是否正确安装
2. **登录失败**：检查网络连接和账号信息
3. **密码修改检测失败**：检查选择器配置是否正确
4. **截图失败**：检查screenshots目录权限

## 🔄 持续改进

### 已知限制
- 依赖特定的HTML选择器
- 需要稳定的网络连接
- 浏览器版本兼容性

### 改进计划
- 增加更多的密码修改页面检测方式
- 优化错误处理和重试机制
- 增加更详细的测试报告
- 支持更多浏览器类型

## 📞 技术支持

如果在使用过程中遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 检查配置文件是否正确
3. 确认测试数据格式是否符合要求
4. 尝试使用调试模式获取更多信息
