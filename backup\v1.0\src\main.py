#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自考成绩查询自动化系统主程序
整合所有模块，实现批量处理学员数据的完整流程
"""

import sys
import time
import argparse
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from datetime import datetime
from playwright.sync_api import Page

# 添加modules目录到Python路径
sys.path.append(str(Path(__file__).parent / "modules"))

from modules.data import load_students_data
from modules.login import LoginManager
from modules.navigation import navigate_after_login
from modules.screenshot import capture_student_screenshot
from modules.browser import BrowserManager
from modules.logger import get_logger_manager, log_info, log_success, log_error, log_warning
from config import CONFIG


class AutoQueryManager:
    """自动查询管理器 - 主程序控制器"""
    
    def __init__(self, data_file: str = "students.csv"):
        """
        初始化自动查询管理器
        
        Args:
            data_file: 学员数据文件路径
        """
        self.data_file = data_file
        self.logger_manager = get_logger_manager()
        self.students = []
        self.results = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "start_time": None,
            "end_time": None,
            "details": []
        }
        
        log_info("自动查询管理器初始化完成", "系统初始化")
    
    def load_student_data(self) -> bool:
        """加载学员数据"""
        log_info(f"加载学员数据文件: {self.data_file}", "数据加载")
        
        try:
            self.students = load_students_data(self.data_file)
            
            if not self.students:
                log_error("数据加载", Exception("未能加载任何学员数据"))
                return False
            
            self.results["total"] = len(self.students)
            log_success(f"成功加载 {len(self.students)} 名学员数据", "数据加载")
            
            # 显示学员列表
            print(f"\n📋 学员列表 ({len(self.students)} 名):")
            for i, student in enumerate(self.students, 1):
                print(f"   {i:2d}. {student['姓名']} ({student['身份证号']})")
            
            return True
            
        except Exception as e:
            log_error("数据加载", e)
            return False
    
    def process_single_student_with_recovery(self, student: Dict, index: int, browser_manager: BrowserManager) -> Tuple[bool, str]:
        """
        带恢复机制的单个学员处理流程

        Args:
            student: 学员信息字典
            index: 学员索引（用于显示进度）
            browser_manager: 浏览器管理器实例

        Returns:
            (是否成功, 错误信息)
        """
        max_page_retries = 2
        student_name = student["姓名"]

        for attempt in range(max_page_retries):
            try:
                log_info(f"学员 {student_name} 处理尝试 {attempt + 1}/{max_page_retries}", "异常恢复")

                # 检查浏览器健康状态
                if not browser_manager.is_browser_alive():
                    log_warning("浏览器实例已失效，无法继续处理", "异常恢复")
                    return False, "浏览器实例失效"

                # 执行正常的学员处理流程
                success, error_msg = self.process_single_student(student, index, browser_manager)

                if success:
                    if attempt > 0:
                        log_success(f"学员 {student_name} 在第 {attempt + 1} 次尝试后成功", "异常恢复")
                    return True, "处理成功"
                else:
                    # 如果是最后一次尝试，直接返回失败
                    if attempt == max_page_retries - 1:
                        log_warning(f"学员 {student_name} 所有重试尝试均失败", "异常恢复")
                        return False, error_msg

                    # 尝试页面级别恢复
                    log_info(f"学员 {student_name} 处理失败，尝试页面恢复", "异常恢复")
                    if browser_manager.recover_from_page_error():
                        log_info("页面恢复成功，准备重试", "异常恢复")
                        time.sleep(2)  # 等待页面稳定
                        continue
                    else:
                        log_warning("页面恢复失败", "异常恢复")
                        return False, f"页面恢复失败: {error_msg}"

            except Exception as e:
                error_message = f"学员处理异常: {str(e)}"
                log_error("异常恢复", e)

                # 如果是最后一次尝试，返回失败
                if attempt == max_page_retries - 1:
                    return False, error_message

                # 尝试恢复
                log_info(f"尝试从异常中恢复 (尝试 {attempt + 1}/{max_page_retries})", "异常恢复")
                try:
                    if browser_manager.recover_from_page_error():
                        log_info("异常恢复成功，准备重试", "异常恢复")
                        time.sleep(2)
                        continue
                    else:
                        log_warning("异常恢复失败", "异常恢复")
                        return False, error_message
                except Exception as recovery_error:
                    log_error("异常恢复", recovery_error)
                    return False, f"恢复过程异常: {str(recovery_error)}"

        return False, "所有重试尝试均失败"

    def _execute_login_step(self, student: Dict, browser_manager: BrowserManager) -> Tuple[bool, str, Optional[Page]]:
        """
        执行登录步骤

        Args:
            student: 学员信息字典
            browser_manager: 浏览器管理器实例

        Returns:
            (是否成功, 错误信息, 页面对象)
        """
        try:
            student_name = student["姓名"]
            username = student["身份证号"]
            password = student["密码"]

            log_info("执行自动登录", "登录流程")
            login_manager = LoginManager(browser_manager)
            success, error_msg, page = login_manager.perform_login(username, password, max_retries=3)

            if not success:
                error_message = f"登录失败: {error_msg}"
                log_error("登录流程", Exception(error_message))
                return False, error_message, None

            log_success("登录成功", "登录流程")
            return True, "登录成功", page

        except Exception as e:
            error_message = f"登录步骤异常: {str(e)}"
            log_error("登录流程", e)
            return False, error_message, None

    def _execute_navigation_step(self, page: Page) -> Tuple[bool, str]:
        """
        执行页面导航步骤

        Args:
            page: 页面对象

        Returns:
            (是否成功, 错误信息)
        """
        try:
            log_info("执行页面导航", "导航流程")
            if not navigate_after_login(page):
                error_message = "页面导航失败"
                log_error("导航流程", Exception(error_message))
                return False, error_message

            log_success("页面导航成功", "导航流程")
            return True, "导航成功"

        except Exception as e:
            error_message = f"导航步骤异常: {str(e)}"
            log_error("导航流程", e)
            return False, error_message

    def _execute_screenshot_step(self, page: Page, student_name: str) -> Tuple[bool, str, Optional[str]]:
        """
        执行截图步骤

        Args:
            page: 页面对象
            student_name: 学员姓名

        Returns:
            (是否成功, 错误信息, 截图路径)
        """
        try:
            log_info("执行成绩单截图", "截图流程")
            screenshot_path = capture_student_screenshot(page, student_name)

            if not screenshot_path:
                error_message = "成绩单截图失败"
                log_error("截图流程", Exception(error_message))
                return False, error_message, None

            log_success(f"成绩单截图成功: {screenshot_path}", "截图流程")
            return True, "截图成功", screenshot_path

        except Exception as e:
            error_message = f"截图步骤异常: {str(e)}"
            log_error("截图流程", e)
            return False, error_message, None

    def _cleanup_resources(self, page: Optional[Page], browser_manager: BrowserManager) -> None:
        """
        清理资源

        Args:
            page: 页面对象（可能为None）
            browser_manager: 浏览器管理器实例
        """
        try:
            if page:
                browser_manager.close_page(page)
                log_info("页面资源清理完成", "资源清理")
        except Exception as e:
            log_warning(f"页面资源清理异常: {e}", "资源清理")

    def process_single_student(self, student: Dict, index: int, browser_manager: BrowserManager) -> Tuple[bool, str]:
        """
        处理单个学员的完整流程

        Args:
            student: 学员信息字典
            index: 学员索引（用于显示进度）
            browser_manager: 浏览器管理器实例

        Returns:
            (是否成功, 错误信息)
        """
        student_name = student["姓名"]
        username = student["身份证号"]

        log_info(f"开始处理学员 {index}/{self.results['total']}: {student_name}", "学员处理")

        # 开始学员处理记录
        self.logger_manager.start_student_processing(student_name, username)

        page = None
        
        try:
            # 步骤1: 执行登录
            success, error_msg, page = self._execute_login_step(student, browser_manager)
            if not success:
                self.logger_manager.end_student_processing(False, error_msg)
                return False, error_msg

            # 步骤2: 执行导航
            success, error_msg = self._execute_navigation_step(page)
            if not success:
                self._cleanup_resources(page, browser_manager)
                self.logger_manager.end_student_processing(False, error_msg)
                return False, error_msg

            # 步骤3: 执行截图
            success, error_msg, screenshot_path = self._execute_screenshot_step(page, student_name)
            if not success:
                self._cleanup_resources(page, browser_manager)
                self.logger_manager.end_student_processing(False, error_msg)
                return False, error_msg

            # 步骤4: 清理资源
            self._cleanup_resources(page, browser_manager)

            # 结束学员处理记录
            self.logger_manager.end_student_processing(True)

            # 保存进度
            self.logger_manager.save_student_progress(username, student_name, True)

            log_success(f"学员 {student_name} 处理完成", "学员处理")
            return True, "处理成功"

        except Exception as e:
            error_message = f"处理异常: {str(e)}"
            log_error("学员处理", e)

            # 确保资源清理
            self._cleanup_resources(page, browser_manager)

            self.logger_manager.end_student_processing(False, error_message)

            # 保存失败进度
            self.logger_manager.save_student_progress(username, student_name, False, error_message)

            return False, error_message
    
    def batch_process(self) -> bool:
        """批量处理所有学员（使用单一浏览器实例）"""
        log_info("开始批量处理学员", "批量处理")

        # 检查是否有未完成的进度
        if self.logger_manager.has_unfinished_progress():
            print("\n🔄 发现未完成的处理进度")
            response = input("是否从上次中断的地方继续处理？(y/n): ").lower().strip()

            if response == 'y':
                print("📋 正在过滤已处理的学员...")
                original_count = len(self.students)
                self.students = self.logger_manager.filter_remaining_students(self.students)
                filtered_count = len(self.students)

                if filtered_count < original_count:
                    print(f"✅ 已过滤 {original_count - filtered_count} 名已处理学员")
                    print(f"📝 剩余待处理学员: {filtered_count} 名")
                    self.results["total"] = filtered_count
                else:
                    print("ℹ️ 没有找到已处理的学员，将重新开始")
            else:
                print("🗑️ 清除旧的进度数据，重新开始处理")
                self.logger_manager.clear_progress()

        # 开始会话记录
        self.logger_manager.start_session()
        self.logger_manager.start_progress_session(self.results["total"])
        self.results["start_time"] = datetime.now()

        print(f"\n🚀 开始批量处理 {self.results['total']} 名学员...")
        print("=" * 60)

        # 创建单一浏览器实例
        browser_manager = BrowserManager()

        print(f"\n🌐 初始化浏览器...")
        if not browser_manager.init_browser():
            log_error("批量处理", Exception("浏览器初始化失败"))
            print("❌ 浏览器初始化失败，无法继续处理")
            self.logger_manager.end_session()
            return False

        log_success("浏览器初始化成功", "批量处理")
        print("✅ 浏览器初始化成功")

        try:

            for i, student in enumerate(self.students, 1):
                student_name = student["姓名"]

                print(f"\n📝 处理第 {i}/{self.results['total']} 名学员: {student_name}")
                print(f"   身份证号: {student['身份证号']}")

                # 显示进度
                progress = (i / self.results['total']) * 100
                print(f"   进度: {progress:.1f}% ({i}/{self.results['total']})")

                # 处理前检查浏览器健康状态
                if not browser_manager.is_browser_alive():
                    log_warning(f"浏览器实例失效，尝试重新初始化", "批量处理")
                    print("   ⚠️ 浏览器实例失效，尝试重新初始化...")

                    # 尝试重新初始化浏览器
                    browser_manager.close()
                    browser_manager = BrowserManager()

                    if not browser_manager.init_browser():
                        log_error("批量处理", Exception("浏览器重新初始化失败"))
                        print("   ❌ 浏览器重新初始化失败，停止处理")
                        break

                    log_success("浏览器重新初始化成功", "批量处理")
                    print("   ✅ 浏览器重新初始化成功")

                # 处理单个学员（使用带恢复机制的方法）
                start_time = time.time()
                success, error_msg = self.process_single_student_with_recovery(student, i, browser_manager)
                processing_time = time.time() - start_time
            
                # 记录结果
                result_detail = {
                    "index": i,
                    "name": student_name,
                    "id": student["身份证号"],
                    "success": success,
                    "error": error_msg if not success else None,
                    "processing_time": processing_time
                }
                self.results["details"].append(result_detail)

                if success:
                    self.results["success"] += 1
                    print(f"   ✅ 成功 (耗时: {processing_time:.1f}秒)")
                else:
                    self.results["failed"] += 1
                    print(f"   ❌ 失败: {error_msg} (耗时: {processing_time:.1f}秒)")

                # 处理间隔
                if i < self.results['total']:
                    print("   ⏳ 等待2秒后处理下一名学员...")
                    time.sleep(2)

        finally:
            # 确保浏览器正确关闭
            try:
                browser_manager.close()
                log_success("浏览器已关闭", "批量处理")
                print("\n🌐 浏览器已关闭")
            except Exception as e:
                log_warning(f"浏览器关闭异常: {e}", "批量处理")
                print(f"\n⚠️ 浏览器关闭异常: {e}")

        # 结束会话记录
        self.results["end_time"] = datetime.now()
        self.logger_manager.end_session()

        # 结束进度管理会话
        self.logger_manager.end_progress_session()

        # 显示最终结果
        self._display_final_results()

        # 计算成功率
        success_rate = (self.results["success"] / self.results["total"]) * 100

        log_success(f"批量处理完成，成功率: {success_rate:.1f}%", "批量处理")

        return success_rate >= 95.0  # 要求95%以上成功率
    
    def _display_final_results(self):
        """显示最终处理结果"""
        total_time = self.results["end_time"] - self.results["start_time"]
        success_rate = (self.results["success"] / self.results["total"]) * 100
        
        print("\n" + "=" * 60)
        print("📊 批量处理结果统计")
        print("=" * 60)
        print(f"处理时间: {self.results['start_time'].strftime('%Y-%m-%d %H:%M:%S')} - {self.results['end_time'].strftime('%H:%M:%S')}")
        print(f"总耗时: {total_time}")
        print(f"处理总数: {self.results['total']} 名学员")
        print(f"成功数量: {self.results['success']} 名")
        print(f"失败数量: {self.results['failed']} 名")
        print(f"成功率: {success_rate:.1f}%")
        
        # 显示失败详情
        if self.results["failed"] > 0:
            print(f"\n❌ 失败学员详情:")
            for detail in self.results["details"]:
                if not detail["success"]:
                    print(f"   - {detail['name']} ({detail['id']}): {detail['error']}")
        
        # 显示截图目录
        screenshot_dir = Path(CONFIG["screenshot_dir"])
        if screenshot_dir.exists():
            screenshots = list(screenshot_dir.glob("*.png"))
            print(f"\n📸 生成截图: {len(screenshots)} 个文件")
            print(f"📁 截图目录: {screenshot_dir}")
        
        print("=" * 60)
    
    def run(self) -> bool:
        """运行主程序"""
        try:
            print("🎯 自考成绩查询自动化系统")
            print("=" * 40)
            
            # 加载学员数据
            if not self.load_student_data():
                return False
            
            # 确认开始处理
            print(f"\n⚠️  即将开始批量处理 {self.results['total']} 名学员")
            print("   请确保网络连接正常，系统将自动进行以下操作：")
            print("   1. 自动登录福建省自考系统")
            print("   2. 导航到成绩查询页面")
            print("   3. 截图保存成绩单")
            
            confirm = input("\n是否继续？(y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ 用户取消操作")
                return False
            
            # 执行批量处理
            return self.batch_process()
            
        except KeyboardInterrupt:
            print("\n\n⚠️ 用户中断操作")
            log_warning("用户中断批量处理", "系统控制")
            return False
        except Exception as e:
            print(f"\n❌ 系统异常: {e}")
            log_error("系统运行", e)
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="自考成绩查询自动化系统")
    parser.add_argument("--data", "-d", default="data/students.csv", help="学员数据文件路径")
    parser.add_argument("--headless", action="store_true", help="使用无头模式")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    
    args = parser.parse_args()
    
    # 更新配置
    if args.headless:
        CONFIG["headless"] = True
    
    if args.debug:
        CONFIG["log_level"] = "DEBUG"
    
    # 创建并运行自动查询管理器
    manager = AutoQueryManager(args.data)
    success = manager.run()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
