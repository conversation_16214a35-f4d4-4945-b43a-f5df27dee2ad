#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试密码修改功能
验证更新后的选择器是否能正确识别页面元素
"""

import sys
import time
import yaml
from pathlib import Path
from playwright.sync_api import sync_playwright

# 直接加载配置文件
def load_config():
    """直接加载配置文件"""
    config_path = Path(__file__).parent / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

CONFIG = load_config()

# 简单的日志函数
def log_info(message, module="测试"):
    """信息日志"""
    print(f"[INFO] [{module}] {message}")

def log_success(message, module="测试"):
    """成功日志"""
    print(f"[SUCCESS] [{module}] ✅ {message}")

def log_error(message, module="测试"):
    """错误日志"""
    print(f"[ERROR] [{module}] ❌ {message}")

def log_warning(message, module="测试"):
    """警告日志"""
    print(f"[WARNING] [{module}] ⚠️ {message}")

def test_password_change_selectors():
    """测试密码修改页面的选择器"""

    print("开始测试密码修改选择器...")
    
    with sync_playwright() as p:
        # 启动浏览器
        browser = p.chromium.launch(
            headless=False,
            slow_mo=500
        )
        
        try:
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            page = context.new_page()
            
            # 导航到登录页面
            login_url = CONFIG['config']['login_url']

            log_info(f"导航到登录页面: {login_url}", "测试")
            page.goto(login_url, timeout=30000)

            # 注意：这个测试假设用户已经在浏览器中手动登录并到达了密码修改页面
            # 或者我们需要等待用户手动操作到密码修改页面
            log_info("请在浏览器中手动登录并导航到密码修改页面，然后按回车继续测试...", "测试")
            input("按回车键继续...")  # 等待用户手动操作
            
            # 等待页面加载
            time.sleep(2)
            
            # 测试选择器
            selectors = CONFIG['selectors']
            
            # 测试新密码输入框
            log_info("测试新密码输入框选择器", "测试")
            new_password_found = False
            for selector in selectors['new_password_input']:
                try:
                    element = page.query_selector(selector)
                    if element:
                        log_success(f"新密码输入框选择器有效: {selector}", "测试")
                        new_password_found = True
                        break
                except Exception as e:
                    continue
            
            if not new_password_found:
                log_error("未找到新密码输入框", "测试")
            
            # 测试确认密码输入框
            log_info("测试确认密码输入框选择器", "测试")
            confirm_password_found = False
            for selector in selectors['confirm_password_input']:
                try:
                    element = page.query_selector(selector)
                    if element:
                        log_success(f"确认密码输入框选择器有效: {selector}", "测试")
                        confirm_password_found = True
                        break
                except Exception as e:
                    continue
            
            if not confirm_password_found:
                log_error("未找到确认密码输入框", "测试")
            
            # 测试提交按钮
            log_info("测试提交按钮选择器", "测试")
            submit_button_found = False
            for selector in selectors['change_password_button']:
                try:
                    element = page.query_selector(selector)
                    if element:
                        log_success(f"提交按钮选择器有效: {selector}", "测试")
                        submit_button_found = True
                        break
                except Exception as e:
                    continue
            
            if not submit_button_found:
                log_error("未找到提交按钮", "测试")
            
            # 总结测试结果
            if new_password_found and confirm_password_found and submit_button_found:
                log_success("所有密码修改选择器测试通过！", "测试")
                return True
            else:
                log_error("部分选择器测试失败", "测试")
                return False
                
        except Exception as e:
            log_error(f"测试过程中发生错误: {str(e)}", "测试")
            return False
            
        finally:
            browser.close()

if __name__ == "__main__":
    success = test_password_change_selectors()
    if success:
        print("\n✅ 密码修改选择器测试成功！")
    else:
        print("\n❌ 密码修改选择器测试失败！")
