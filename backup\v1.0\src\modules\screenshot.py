#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
截图模块
实现精确的成绩单定位和截图功能
"""

import time
from typing import Optional, Dict
from pathlib import Path
from playwright.sync_api import Page, Locator

import sys
sys.path.append('..')
from config import CONFIG, SELECTORS
from modules.logger import log_info, log_success, log_error, log_warning


class ScreenshotManager:
    """截图管理器"""
    
    def __init__(self, page: Page):
        """
        初始化截图管理器
        
        Args:
            page: 成绩页面对象
        """
        self.page = page
        
        # 确保截图目录存在
        self.screenshot_dir = Path(CONFIG["screenshot_dir"])
        self.screenshot_dir.mkdir(parents=True, exist_ok=True)
        
        log_info("截图管理器初始化完成", "截图")
    
    def locate_score_table(self) -> Optional[Locator]:
        """
        定位成绩单表格区域
        
        Returns:
            成绩单表格元素，如果未找到返回None
        """
        log_info("开始定位成绩单表格区域", "截图")
        
        try:
            # 等待页面稳定
            self.page.wait_for_load_state("networkidle")
            time.sleep(2)
            
            best_table = None
            best_score = 0
            
            for selector in SELECTORS["score_table"]:
                try:
                    tables = self.page.locator(selector)
                    table_count = tables.count()
                    
                    if table_count > 0:
                        log_info(f"选择器 {selector} 找到 {table_count} 个表格", "截图")
                        
                        # 评估每个表格的相关性
                        for i in range(table_count):
                            table = tables.nth(i)
                            score = self._evaluate_table_relevance(table)
                            
                            if score > best_score:
                                best_table = table
                                best_score = score
                                log_info(f"找到更好的表格候选 (评分: {score})", "截图")
                
                except Exception as e:
                    log_warning(f"选择器 {selector} 评估失败: {e}", "截图")
                    continue
            
            if best_table:
                log_success(f"成功定位成绩单表格 (最终评分: {best_score})", "截图")
                return best_table
            else:
                log_warning("未找到合适的成绩单表格", "截图")
                return None
                
        except Exception as e:
            log_error("截图", e)
            return None
    
    def _evaluate_table_relevance(self, table: Locator) -> int:
        """
        评估表格与成绩单的相关性
        
        Args:
            table: 表格元素
            
        Returns:
            相关性评分（0-100）
        """
        try:
            score = 0
            
            # 获取表格文本内容
            table_text = table.text_content() or ""
            
            # 关键词评分
            keywords = {
                "成绩": 20,
                "科目": 15,
                "分数": 15,
                "合格": 10,
                "不合格": 10,
                "学分": 8,
                "考试": 8,
                "课程": 5,
                "专业": 5,
                "学号": 3,
                "姓名": 3
            }
            
            for keyword, points in keywords.items():
                if keyword in table_text:
                    score += points
            
            # 表格结构评分
            try:
                row_count = table.locator("tr").count()
                col_count = table.locator("tr").first.locator("td, th").count() if row_count > 0 else 0
                
                # 合理的行数和列数加分
                if 2 <= row_count <= 50:  # 合理的行数
                    score += min(row_count * 2, 20)
                
                if 3 <= col_count <= 10:  # 合理的列数
                    score += col_count * 2
                
                # 表格大小评分
                bbox = table.bounding_box()
                if bbox:
                    area = bbox["width"] * bbox["height"]
                    if area > 10000:  # 足够大的表格
                        score += 10
                        
            except Exception:
                pass
            
            return min(score, 100)  # 最高100分
            
        except Exception:
            return 0
    
    def capture_score_screenshot(self, student_name: str, table_element: Optional[Locator] = None) -> Optional[str]:
        """
        截取成绩单截图
        
        Args:
            student_name: 学员姓名
            table_element: 指定的表格元素，如果为None则自动定位
            
        Returns:
            截图文件路径，失败返回None
        """
        log_info(f"开始截取 {student_name} 的成绩单", "截图")
        
        try:
            # 如果没有指定表格元素，自动定位
            if table_element is None:
                table_element = self.locate_score_table()
            
            # 生成文件名
            filename = f"{student_name}.png"
            filepath = self.screenshot_dir / filename
            
            # 截图策略1: 精确表格截图
            if table_element:
                try:
                    log_info("使用精确表格截图策略", "截图")
                    
                    # 滚动到表格位置
                    table_element.scroll_into_view_if_needed()
                    time.sleep(1)
                    
                    # 截取表格区域
                    table_element.screenshot(path=str(filepath))
                    
                    # 验证截图文件
                    if filepath.exists() and filepath.stat().st_size > 1000:
                        log_success(f"精确表格截图成功: {filepath}", "截图")
                        return str(filepath)
                    else:
                        log_warning("精确表格截图文件过小，尝试备选方案", "截图")
                        
                except Exception as e:
                    log_warning(f"精确表格截图失败: {e}，尝试备选方案", "截图")
            
            # 截图策略2: 扩展区域截图
            try:
                log_info("使用扩展区域截图策略", "截图")
                
                # 查找包含成绩信息的容器
                container_selectors = [
                    "//div[.//table]",
                    "//form[.//table]",
                    ".content",
                    ".main",
                    "#content",
                    "#main",
                    "body"
                ]
                
                for selector in container_selectors:
                    try:
                        container = self.page.locator(selector).first
                        if container.count() > 0:
                            container.screenshot(path=str(filepath))
                            
                            if filepath.exists() and filepath.stat().st_size > 5000:
                                log_success(f"扩展区域截图成功: {filepath}", "截图")
                                return str(filepath)
                    except Exception:
                        continue
                        
            except Exception as e:
                log_warning(f"扩展区域截图失败: {e}，尝试全页面截图", "截图")
            
            # 截图策略3: 全页面截图（备选方案）
            try:
                log_info("使用全页面截图策略", "截图")
                
                self.page.screenshot(path=str(filepath), full_page=True)
                
                if filepath.exists() and filepath.stat().st_size > 10000:
                    log_success(f"全页面截图成功: {filepath}", "截图")
                    return str(filepath)
                else:
                    log_error("截图", Exception("所有截图策略都失败"))
                    return None
                    
            except Exception as e:
                log_error("截图", e)
                return None
                
        except Exception as e:
            log_error("截图", e)
            return None
    
    def save_screenshot(self, student_name: str, custom_path: Optional[str] = None) -> Optional[str]:
        """
        保存成绩单截图的便捷方法
        
        Args:
            student_name: 学员姓名
            custom_path: 自定义保存路径
            
        Returns:
            截图文件路径，失败返回None
        """
        log_info(f"保存 {student_name} 的成绩单截图", "截图")
        
        try:
            # 如果指定了自定义路径，使用自定义路径
            if custom_path:
                filepath = Path(custom_path)
                filepath.parent.mkdir(parents=True, exist_ok=True)
                
                self.page.screenshot(path=str(filepath), full_page=True)
                
                if filepath.exists():
                    log_success(f"截图保存成功: {filepath}", "截图")
                    return str(filepath)
            else:
                # 使用默认的截图捕获方法
                return self.capture_score_screenshot(student_name)
                
        except Exception as e:
            log_error("截图", e)
            return None
    
    def get_screenshot_info(self, filepath: str) -> Dict:
        """获取截图文件信息"""
        try:
            path = Path(filepath)
            if path.exists():
                stat = path.stat()
                return {
                    "filename": path.name,
                    "size": stat.st_size,
                    "size_mb": round(stat.st_size / 1024 / 1024, 2),
                    "exists": True
                }
            else:
                return {"exists": False}
        except Exception as e:
            log_error("截图", e)
            return {"error": str(e)}
    
    def optimize_screenshot_quality(self) -> bool:
        """优化截图质量设置"""
        try:
            # 设置页面缩放和视口
            self.page.set_viewport_size({
                "width": CONFIG["viewport_width"], 
                "height": CONFIG["viewport_height"]
            })
            
            # 等待页面完全渲染
            self.page.wait_for_load_state("networkidle")
            time.sleep(2)
            
            log_success("截图质量优化完成", "截图")
            return True
            
        except Exception as e:
            log_error("截图", e)
            return False


# 便捷函数
def capture_student_screenshot(page: Page, student_name: str) -> Optional[str]:
    """
    截取学员成绩单的便捷函数
    
    Args:
        page: 成绩页面对象
        student_name: 学员姓名
        
    Returns:
        截图文件路径，失败返回None
    """
    screenshot_manager = ScreenshotManager(page)
    screenshot_manager.optimize_screenshot_quality()
    return screenshot_manager.capture_score_screenshot(student_name)
