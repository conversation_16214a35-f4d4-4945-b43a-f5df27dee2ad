# 自考成绩查询自动化系统技术文档

> **文档版本**: v1.0.0
> **创建日期**: 2025年1月31日
> **最后更新**: 2025年1月31日
> **文档状态**: 正式版
> **适用版本**: 系统 v2.0+

## 📋 文档信息

| 项目 | 信息 |
|------|------|
| **项目名称** | 自考成绩查询自动化系统 |
| **项目版本** | v2.0+ |
| **技术栈** | Python 3.8+, Playwright, ddddocr, loguru |
| **文档类型** | 技术文档 |
| **目标读者** | 开发人员、运维人员、技术管理人员 |
| **维护团队** | 系统开发团队 |

## 📖 阅读指南

### 文档结构说明
本文档采用模块化结构，共分为9个主要章节：

- **第1-2章**：项目概述和架构设计 - 适合初次了解项目的读者
- **第3-4章**：核心模块和业务流程 - 适合开发人员深入理解系统
- **第5-6章**：技术亮点和配置系统 - 适合技术人员学习创新实现
- **第7-8章**：日志调试和性能优化 - 适合运维人员和性能优化
- **第9章**：未来优化方向 - 适合技术管理人员和项目规划

### 代码示例说明
- 所有代码示例均基于实际项目代码
- 代码片段已经过简化处理，突出核心逻辑
- 完整代码请参考项目源码目录

### 更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v1.0.0 | 2025-01-31 | 初始版本，完整技术文档 | 系统开发团队 |

---

## 目录

- [1. 项目概述](#1-项目概述)
- [2. 架构设计](#2-架构设计)
- [3. 核心模块分析](#3-核心模块分析)
- [4. 业务流程](#4-业务流程)
- [5. 技术亮点](#5-技术亮点)
- [6. 配置系统](#6-配置系统)
- [7. 日志和调试](#7-日志和调试)
- [8. 性能优化](#8-性能优化)
- [9. 未来优化方向](#9-未来优化方向)

---

## 1. 项目概述

### 1.1 系统功能介绍

自考成绩查询自动化系统是一个基于Python和Playwright的Web自动化工具，专门用于批量查询福建省高等教育自学考试成绩。系统能够自动完成登录、导航、截图等操作，大大提高成绩查询的效率。

#### 核心功能
- **自动登录**：支持身份证号登录，集成智能验证码识别
- **智能导航**：自动定位成绩查询菜单，智能选择查询选项
- **精确截图**：智能定位成绩单表格，生成高质量截图
- **批量处理**：支持多学员连续处理，显示实时进度
- **中断恢复**：支持处理中断后从上次停止位置继续
- **错误处理**：完善的异常处理和自动重试机制

### 1.2 技术栈说明

#### 核心技术栈
| 技术组件 | 版本要求 | 用途说明 |
|---------|---------|----------|
| **Python** | 3.8+ | 主要开发语言，推荐3.9-3.11 |
| **Playwright** | 1.40.0+ | 浏览器自动化框架，提供强大的Web操作能力 |
| **ddddocr** | 1.4.11+ | 验证码识别库，基于深度学习模型 |
| **loguru** | 0.7.2+ | 现代化日志库，提供优雅的日志记录 |
| **PyYAML** | 6.0.1+ | YAML配置文件解析 |
| **Pillow** | 10.1.0+ | 图像处理库，用于验证码图像处理 |

#### 可选依赖
| 组件 | 用途 | 安装方式 |
|------|------|----------|
| **pandas** | Excel文件支持 | `pip install pandas` |
| **openpyxl** | Excel文件读写 | `pip install openpyxl` |

#### 技术选型理由
- **Playwright**：相比Selenium更稳定，支持现代Web特性，性能更优
- **ddddocr**：专门针对中文验证码优化，识别率高
- **loguru**：比标准logging更易用，支持结构化日志
- **模块化设计**：便于维护和扩展，支持独立测试

### 1.3 核心特性列表

#### 🤖 智能化特性
- **智能验证码识别**：基于深度学习的自动识别，支持手动备选
- **智能元素查找**：6种查找策略，大幅提高元素定位成功率
- **智能页面导航**：自动识别菜单结构，智能选择查询选项
- **智能错误恢复**：多层级错误恢复机制，自动处理异常情况

#### 🔧 技术特性
- **分层验证机制**：三层递进式验证，确保密码修改检测准确性
- **多策略截图**：精确表格定位、质量优化、多种截图策略
- **资源管理优化**：精确的浏览器资源管理和内存优化
- **配置系统**：多层级配置加载，支持环境变量覆盖

#### 📊 运维特性
- **详细日志记录**：分类日志记录，支持问题追溯和性能分析
- **实时进度显示**：批量处理进度实时显示，支持中断恢复
- **统计报告**：完整的处理统计和成功率分析
- **调试支持**：自动保存调试信息，便于问题诊断

### 1.4 应用场景描述

#### 主要应用场景
1. **教育机构批量查询**：培训机构批量查询学员成绩
2. **个人成绩管理**：个人或家庭多个考生成绩统一管理
3. **成绩统计分析**：教育管理部门的成绩数据收集
4. **自动化办公**：减少重复性人工操作，提高工作效率

#### 适用环境
- **操作系统**：Windows 10/11、macOS 10.14+、Ubuntu 18.04+
- **网络环境**：稳定的互联网连接，支持HTTPS访问
- **硬件要求**：最低4GB内存，推荐8GB以上
- **使用场景**：适合需要批量处理的场景，单次可处理数十到数百名学员

---

## 2. 架构设计

### 2.1 整体架构概述

系统采用模块化分层架构设计，将功能按职责分离到不同模块中，通过清晰的接口进行交互。整体架构遵循单一职责原则和开闭原则，便于维护和扩展。

#### 架构层次
```
┌─────────────────────────────────────────┐
│              应用层 (Application)        │
│  ┌─────────────────────────────────────┐ │
│  │        主程序控制器                  │ │
│  │     (AutoQueryManager)              │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              业务层 (Business)           │
│  ┌─────────┬─────────┬─────────┬───────┐ │
│  │ 登录模块 │ 导航模块 │ 截图模块 │ 数据  │ │
│  │ Login   │ Navi    │ Screen  │ Data  │ │
│  └─────────┴─────────┴─────────┴───────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              支撑层 (Support)            │
│  ┌─────────┬─────────┬─────────┬───────┐ │
│  │ 浏览器  │ 验证码  │ 日志    │ 配置  │ │
│  │ Browser │ Captcha │ Logger  │ Config│ │
│  └─────────┴─────────┴─────────┴───────┘ │
└─────────────────────────────────────────┘
```

### 2.2 模块结构说明

#### 核心模块组成
| 模块名称 | 文件路径 | 主要职责 | 依赖关系 |
|---------|----------|----------|----------|
| **主控制器** | `src/main.py` | 批量处理协调、流程控制 | 依赖所有业务模块 |
| **登录模块** | `src/modules/login.py` | 用户认证、会话管理 | Browser, Captcha, PasswordChange |
| **导航模块** | `src/modules/navigation.py` | 页面导航、菜单操作 | Browser, Config |
| **截图模块** | `src/modules/screenshot.py` | 表格定位、截图生成 | Browser, Config |
| **数据模块** | `src/modules/data.py` | 数据加载、验证、清理 | Config |
| **浏览器模块** | `src/modules/browser.py` | 浏览器生命周期管理 | Config |
| **验证码模块** | `src/modules/captcha.py` | 验证码识别、处理 | Config |
| **日志模块** | `src/modules/logger.py` | 日志记录、统计分析 | Config |
| **配置模块** | `src/config/` | 配置管理、环境适配 | 无依赖 |

### 2.3 组件关系说明

#### 依赖关系图
```
AutoQueryManager (主控制器)
    ├── DataManager (数据管理)
    ├── BrowserManager (浏览器管理)
    └── 业务模块组
        ├── LoginManager
        │   ├── CaptchaRecognizer
        │   └── PasswordChangeHandler
        ├── NavigationManager
        └── ScreenshotManager

支撑组件
    ├── ConfigManager (配置管理)
    └── LoggerManager (日志管理)
```

#### 数据流向
1. **配置加载**：ConfigManager → 各业务模块
2. **数据流转**：DataManager → AutoQueryManager → 业务模块
3. **状态传递**：LoginManager → NavigationManager → ScreenshotManager
4. **日志收集**：各模块 → LoggerManager → 日志文件

### 2.4 设计模式分析

#### 应用的设计模式
| 设计模式 | 应用场景 | 实现示例 |
|---------|----------|----------|
| **管理器模式** | 各功能模块 | LoginManager, BrowserManager等 |
| **策略模式** | 多种实现策略 | 截图策略、元素查找策略 |
| **工厂模式** | 对象创建 | 浏览器会话创建、配置管理器创建 |
| **单例模式** | 全局资源 | 配置管理器、日志管理器 |
| **观察者模式** | 事件处理 | 页面事件监听、进度更新 |

#### 设计原则遵循
- **单一职责原则**：每个模块职责明确，功能内聚
- **开闭原则**：支持扩展新功能，无需修改现有代码
- **依赖倒置原则**：依赖抽象接口，不依赖具体实现
- **接口隔离原则**：接口设计精简，避免不必要的依赖

### 2.5 分层架构介绍

#### 应用层 (Application Layer)
- **职责**：业务流程编排、用户交互、结果输出
- **核心组件**：AutoQueryManager主控制器
- **特点**：不包含具体业务逻辑，专注于流程控制

#### 业务层 (Business Layer)  
- **职责**：具体业务功能实现、业务规则处理
- **核心组件**：Login、Navigation、Screenshot、Data模块
- **特点**：包含核心业务逻辑，相互协作完成业务目标

#### 支撑层 (Support Layer)
- **职责**：基础设施服务、通用功能支持
- **核心组件**：Browser、Captcha、Logger、Config模块
- **特点**：为上层提供基础服务，可独立测试和复用

#### 层间交互规则
- **向下依赖**：上层可以调用下层服务，下层不能直接调用上层
- **接口隔离**：层间通过明确的接口进行交互
- **配置共享**：所有层共享配置系统，保证一致性
- **日志统一**：所有层使用统一的日志系统，便于问题追踪

### 2.6 关键接口设计

#### 主控制器接口
```python
class AutoQueryManager:
    """自动查询管理器 - 主程序控制器"""

    def __init__(self, data_file: str = "students.csv"):
        """初始化管理器"""
        self.data_file = data_file
        self.students = []
        self.results = {"total": 0, "success": 0, "failed": 0}

    def run_batch_processing(self) -> bool:
        """执行批量处理"""
        # 1. 加载学员数据
        # 2. 创建浏览器实例
        # 3. 批量处理学员
        # 4. 生成统计报告
        pass

    def process_single_student(self, student: dict, index: int,
                             browser_manager: BrowserManager) -> Tuple[bool, str]:
        """处理单个学员"""
        # 登录 → 导航 → 截图 → 清理
        pass
```

#### 业务模块接口
```python
class LoginManager:
    """登录管理器"""

    def perform_login(self, username: str, password: str,
                     max_retries: int = 3) -> Tuple[bool, str, Optional[Page]]:
        """执行登录操作，返回(成功状态, 错误信息, 页面对象)"""
        pass

class NavigationManager:
    """导航管理器"""

    def complete_navigation_flow(self) -> bool:
        """完成完整的导航流程"""
        pass

class ScreenshotManager:
    """截图管理器"""

    def capture_score_screenshot(self, student_name: str,
                               table_element: Optional[Locator] = None) -> Optional[str]:
        """截取成绩单截图，返回文件路径"""
        pass
```

#### 支撑模块接口
```python
class BrowserManager:
    """浏览器管理器"""

    def init_browser(self) -> bool:
        """初始化浏览器"""
        pass

    def new_page(self) -> Optional[Page]:
        """创建新页面"""
        pass

    def close_page(self, page: Page) -> bool:
        """关闭指定页面"""
        pass

class ConfigManager:
    """配置管理器"""

    def get(self, key: str, default=None):
        """获取配置值"""
        pass

    def reload(self):
        """重新加载配置"""
        pass
```

### 2.7 配置系统架构

#### 多层级配置加载
```python
# 配置加载优先级（从低到高）
1. 默认配置 (src/config/defaults.py)
   ├── 基础超时设置
   ├── 默认重试次数
   └── 标准选择器

2. 配置文件 (config.yaml)
   ├── 用户自定义配置
   ├── 环境特定设置
   └── 选择器覆盖

3. 环境变量
   ├── EXAM_HEADLESS=true
   ├── EXAM_MAX_RETRIES=5
   └── EXAM_LOG_LEVEL=DEBUG
```

#### 配置使用示例
```python
from config import CONFIG, SELECTORS

# 获取配置值
headless_mode = CONFIG["headless"]
max_retries = CONFIG["max_retries"]

# 获取选择器
username_selectors = SELECTORS["username_input"]
login_button_selectors = SELECTORS["login_button"]

# 动态更新配置
from config import update_config
update_config({"headless": True, "max_retries": 5})
```

---

## 3. 核心模块分析

### 3.1 数据管理模块 (DataManager)

#### 功能职责
- **数据加载**：支持CSV和Excel格式的学员数据文件加载
- **数据验证**：验证必要字段完整性和数据格式正确性
- **数据清理**：自动清理空白字符、标准化数据格式
- **编码处理**：自动检测和处理多种文件编码格式

#### 核心接口
```python
class DataManager:
    def load_students_from_csv(self, file_path: str) -> List[Dict]:
        """从CSV文件加载学员数据，支持多种编码"""

    def load_students_from_excel(self, file_path: str) -> List[Dict]:
        """从Excel文件加载学员数据，需要pandas支持"""

    def _validate_students_data(self, students: List[Dict]) -> List[Dict]:
        """验证和清理学员数据"""

    def _validate_id_card(self, id_card: str) -> bool:
        """验证身份证号格式"""
```

#### 实现细节
- **多编码支持**：按优先级尝试utf-8-sig、utf-8、gbk、gb2312编码
- **数据验证**：检查姓名、身份证号、密码三个必要字段
- **身份证验证**：支持15位和18位身份证号格式验证
- **错误处理**：详细的错误日志记录和异常处理

### 3.2 浏览器管理模块 (BrowserManager)

#### 功能职责
- **浏览器生命周期管理**：初始化、使用、清理的完整生命周期
- **多页面管理**：支持单一浏览器实例创建多个页面
- **资源优化**：精确的内存管理和资源释放
- **错误恢复**：多层级的错误恢复和健康检查机制

#### 核心接口
```python
class BrowserManager:
    def init_browser(self) -> bool:
        """初始化浏览器，配置启动参数"""

    def new_page(self) -> Optional[Page]:
        """创建新页面，设置事件监听"""

    def close_page(self, page: Page) -> bool:
        """关闭指定页面"""

    def recover_from_page_error(self) -> bool:
        """从页面错误中恢复"""

    def perform_health_check(self) -> bool:
        """执行浏览器健康检查"""
```

#### 实现细节
- **启动参数优化**：禁用自动化检测、沙箱模式等
- **上下文管理**：支持Python上下文管理器模式
- **事件监听**：页面加载、错误、对话框等事件处理
- **健康检查**：实时监控浏览器连接状态

### 3.3 登录管理模块 (LoginManager)

#### 功能职责
- **用户认证**：处理用户名、密码输入和验证
- **验证码处理**：集成验证码识别和输入功能
- **密码修改检测**：检测并处理密码修改页面
- **会话管理**：管理登录会话和状态维护

#### 核心接口
```python
class LoginManager:
    def perform_login(self, username: str, password: str,
                     max_retries: int = 3) -> Tuple[bool, str, Optional[Page]]:
        """执行完整登录流程"""

    def _attempt_login(self, page: Page, username: str, password: str) -> Tuple[bool, str]:
        """单次登录尝试"""

    def _handle_captcha(self, page: Page) -> bool:
        """处理验证码识别和输入"""

    def _wait_for_login_result_with_password_change(self, page: Page, password: str) -> Tuple[bool, str]:
        """等待登录结果并处理密码修改"""
```

#### 实现细节
- **重试机制**：最大3次登录重试，每次失败后刷新页面
- **验证码集成**：自动调用CaptchaRecognizer进行验证码处理
- **密码修改集成**：自动检测和处理密码修改页面
- **状态判断**：智能判断登录成功、失败和密码修改状态

### 3.4 验证码识别模块 (CaptchaRecognizer)

#### 功能职责
- **图像处理**：验证码图像的截取和预处理
- **智能识别**：基于ddddocr的验证码自动识别
- **重试机制**：支持验证码刷新和多次重试
- **手动备选**：自动识别失败时提供手动输入选项

#### 核心接口
```python
class CaptchaRecognizer:
    def process_captcha(self, page: Page) -> bool:
        """处理验证码的完整流程"""

    def recognize_captcha(self, image_bytes: bytes) -> Optional[str]:
        """识别验证码文本"""

    def handle_captcha_with_retry(self, page: Page, max_retries: int = 3) -> bool:
        """带重试的验证码处理"""

    def refresh_captcha(self, page: Page) -> bool:
        """刷新验证码"""
```

#### 实现细节
- **ddddocr集成**：支持新旧版本的ddddocr库
- **PIL兼容性**：自动处理PIL版本差异问题
- **调试支持**：自动保存验证码图片到debug目录
- **手动输入**：识别失败时保存图片并提示用户手动输入

### 3.5 导航管理模块 (NavigationManager)

#### 功能职责
- **页面导航**：自动导航到成绩查询页面
- **菜单识别**：智能识别和定位查询菜单
- **选项选择**：智能选择最合适的查询选项
- **页面验证**：验证导航结果和页面状态

#### 核心接口
```python
class NavigationManager:
    def complete_navigation_flow(self) -> bool:
        """完成完整的导航流程"""

    def navigate_to_score_query(self) -> bool:
        """导航到成绩查询页面"""

    def detect_query_options(self) -> List[Dict]:
        """检测可用的查询选项"""

    def smart_select_option(self, options: List[Dict]) -> Optional[Dict]:
        """智能选择查询选项"""
```

#### 实现细节
- **多策略导航**：支持多种页面结构的导航方式
- **智能选择**：基于关键词匹配选择最合适的查询选项
- **页面验证**：验证导航成功和页面加载完成
- **错误处理**：导航失败时的重试和恢复机制

### 3.6 截图管理模块 (ScreenshotManager)

#### 功能职责
- **表格定位**：智能定位成绩单表格区域
- **截图生成**：生成高质量的成绩单截图
- **多策略截图**：支持多种截图策略和备选方案
- **质量优化**：截图质量优化和文件管理

#### 核心接口
```python
class ScreenshotManager:
    def capture_score_screenshot(self, student_name: str,
                               table_element: Optional[Locator] = None) -> Optional[str]:
        """截取成绩单截图"""

    def locate_score_table(self) -> Optional[Locator]:
        """定位成绩单表格区域"""

    def optimize_screenshot_quality(self) -> bool:
        """优化截图质量设置"""

    def _capture_with_fallback_strategies(self, student_name: str) -> Optional[str]:
        """使用备选策略截图"""
```

#### 实现细节
- **多策略定位**：精确表格截图、可视区域截图、全页面截图
- **质量优化**：页面缩放、等待加载、滚动定位
- **文件管理**：按学员姓名命名，自动创建目录
- **错误恢复**：截图失败时的多种备选策略

### 3.7 日志管理模块 (LoggerManager)

#### 功能职责
- **多级日志记录**：支持不同级别的日志记录
- **文件管理**：自动日志轮转和文件管理
- **统计功能**：处理统计和性能监控
- **会话管理**：会话级别的日志管理

#### 核心接口
```python
class LoggerManager:
    def start_session(self):
        """开始新的处理会话"""

    def end_session(self):
        """结束当前会话并生成统计"""

    def start_progress_session(self, total_count: int):
        """开始进度跟踪会话"""

    def update_progress(self, current: int, student_name: str, status: str):
        """更新处理进度"""
```

#### 实现细节
- **loguru集成**：使用现代化的loguru日志库
- **多文件输出**：主日志、错误日志、统计日志分离
- **自动轮转**：按大小和时间自动轮转日志文件
- **结构化日志**：支持JSON格式的结构化日志记录

### 3.8 密码修改检测模块 (PasswordChangeHandler)

#### 功能职责
- **页面检测**：智能检测密码修改页面
- **分层验证**：三层递进式验证机制
- **智能查找**：6种元素查找策略
- **容错处理**：完善的超时和错误处理

#### 核心接口
```python
class PasswordChangeHandler:
    def detect_password_change_page(self, page: Page) -> Tuple[bool, str]:
        """检测是否为密码修改页面"""

    def handle_password_change_with_retry(self, page: Page, student_name: str,
                                        new_password: str) -> Tuple[bool, str]:
        """带重试的密码修改处理"""

    def _find_password_inputs(self, page: Page) -> List[Locator]:
        """查找密码输入框"""

    def _save_debug_info(self, page: Page, detection_result: Dict, reason: str):
        """保存调试信息"""
```

#### 实现细节
- **三层验证**：粗略检测→精确验证→功能验证
- **智能查找**：配置选择器、通用选择器、iframe检测等6种策略
- **超时控制**：检测超时、修改超时、页面加载超时
- **调试支持**：自动保存HTML、截图、JSON详情

---

## 4. 业务流程

### 4.1 整体业务流程概述

系统的业务流程采用批量处理模式，通过单一浏览器实例顺序处理多个学员的成绩查询任务。整个流程分为初始化、批量处理、结果统计三个主要阶段。

#### 流程阶段划分
```
阶段1: 系统初始化
├── 加载配置文件
├── 初始化日志系统
├── 加载学员数据
└── 创建浏览器实例

阶段2: 批量处理循环
├── 学员A处理
│   ├── 登录流程
│   ├── 密码修改检测
│   ├── 导航流程
│   ├── 截图流程
│   └── 页面清理
├── 学员B处理
│   └── ... (重复上述流程)
└── 学员N处理

阶段3: 结果统计
├── 生成处理报告
├── 统计成功率
├── 保存日志文件
└── 清理系统资源
```

### 4.2 详细业务流程说明

#### 4.2.1 系统初始化流程
```python
def initialize_system():
    """系统初始化流程"""
    # 1. 配置系统初始化
    config_manager = ConfigManager()
    config_manager.load_all_configs()

    # 2. 日志系统初始化
    logger_manager = LoggerManager()
    logger_manager.setup_loggers()

    # 3. 数据加载和验证
    data_manager = DataManager()
    students = data_manager.load_students_from_csv("students.csv")

    # 4. 浏览器实例创建
    browser_manager = BrowserManager()
    browser_manager.init_browser()

    return students, browser_manager
```

#### 4.2.2 单个学员处理流程
```python
def process_single_student(student: dict, browser_manager: BrowserManager):
    """单个学员完整处理流程"""

    # 步骤1: 登录流程
    login_manager = LoginManager(browser_manager)
    success, error_msg, page = login_manager.perform_login(
        username=student["身份证号"],
        password=student["密码"]
    )

    if not success:
        return False, f"登录失败: {error_msg}"

    # 步骤2: 导航流程
    navigation_manager = NavigationManager(page)
    if not navigation_manager.complete_navigation_flow():
        return False, "导航失败"

    # 步骤3: 截图流程
    screenshot_manager = ScreenshotManager(page)
    screenshot_path = screenshot_manager.capture_score_screenshot(student["姓名"])

    if not screenshot_path:
        return False, "截图失败"

    # 步骤4: 页面清理
    browser_manager.close_page(page)

    return True, f"处理成功，截图保存至: {screenshot_path}"
```

### 4.3 登录流程详细说明

#### 4.3.1 登录流程步骤
```
登录流程 (perform_login)
├── 1. 页面准备
│   ├── 创建新页面或导航到登录页
│   └── 等待页面加载完成
├── 2. 表单填写 (最多3次重试)
│   ├── 填写用户名 (身份证号)
│   ├── 填写密码
│   ├── 处理验证码 (如果存在)
│   └── 点击登录按钮
├── 3. 结果等待和判断
│   ├── 等待页面跳转
│   ├── 检查错误信息
│   ├── 检测密码修改页面
│   └── 判断登录成功状态
└── 4. 密码修改处理 (如果需要)
    ├── 三层验证检测
    ├── 智能元素查找
    ├── 密码修改操作
    └── 验证修改结果
```

#### 4.3.2 验证码处理子流程
```python
def handle_captcha_flow(page: Page):
    """验证码处理子流程"""
    # 1. 检测验证码元素
    captcha_image, captcha_input = find_captcha_elements(page)

    if not captcha_image or not captcha_input:
        return True  # 无验证码，直接成功

    # 2. 验证码处理循环 (最多3次)
    for attempt in range(3):
        # 2.1 截取验证码图片
        image_bytes = capture_captcha_image(page, captcha_image)

        # 2.2 识别验证码文本
        captcha_text = recognize_captcha(image_bytes)

        # 2.3 输入验证码
        if input_captcha(page, captcha_input, captcha_text):
            return True

        # 2.4 失败处理：刷新验证码
        if attempt < 2:
            refresh_captcha(page)

    # 3. 自动识别失败，尝试手动输入
    return manual_captcha_input(page)
```

### 4.4 密码修改检测流程

#### 4.4.1 三层验证机制
```
密码修改检测 (detect_password_change_page)
├── 第一层: 粗略检测 (5秒超时)
│   ├── 关键词匹配检测
│   │   ├── "修改密码"、"密码修改"
│   │   ├── "修改登录密码"、"change password"
│   │   └── "password change"、"update password"
│   └── 基础选择器检测
│       ├── input[type="password"]
│       ├── #password, #newPassword
│       └── .password-input, .pwd-input
├── 第二层: 精确验证 (10秒超时)
│   ├── 智能元素查找 (6种策略)
│   │   ├── 配置选择器查找
│   │   ├── 通用选择器查找
│   │   ├── iframe检测查找
│   │   ├── 基于文本内容查找
│   │   ├── 基于位置关系查找
│   │   └── 动态等待查找
│   └── 密码输入框验证
│       ├── 检查元素可见性
│       ├── 检查元素可交互性
│       └── 验证输入框数量 (≥2个)
└── 第三层: 功能验证 (15秒超时)
    ├── 交互性测试
    │   ├── 尝试点击输入框
    │   ├── 尝试输入测试文本
    │   └── 验证输入响应
    └── 页面结构验证
        ├── 检查提交按钮存在
        ├── 验证表单结构完整
        └── 确认页面功能可用
```

#### 4.4.2 智能元素查找策略
```python
def find_password_inputs_with_strategies(page: Page):
    """6种智能元素查找策略"""

    strategies = [
        # 策略1: 配置选择器查找 (优先级最高)
        lambda: find_by_config_selectors(page),

        # 策略2: 通用选择器查找
        lambda: find_by_common_selectors(page),

        # 策略3: iframe检测查找
        lambda: find_in_iframes(page),

        # 策略4: 基于文本内容查找
        lambda: find_by_text_content(page),

        # 策略5: 基于位置关系查找
        lambda: find_by_position_relation(page),

        # 策略6: 动态等待查找
        lambda: find_with_dynamic_wait(page)
    ]

    for i, strategy in enumerate(strategies, 1):
        try:
            elements = strategy()
            if elements:
                log_success(f"策略{i}查找成功，找到{len(elements)}个元素")
                return elements
        except Exception as e:
            log_warning(f"策略{i}查找失败: {e}")

    return []
```

### 4.5 导航流程详细说明

#### 4.5.1 导航流程步骤
```
导航流程 (complete_navigation_flow)
├── 1. 导航到成绩查询页面
│   ├── 查找成绩查询相关链接
│   ├── 点击进入查询页面
│   └── 等待页面加载完成
├── 2. 检测查询选项
│   ├── 扫描页面中的查询选项
│   ├── 识别选项文本和链接
│   └── 过滤有效的查询选项
├── 3. 智能选择选项
│   ├── 基于关键词匹配评分
│   ├── 优先选择"当次成绩"相关选项
│   └── 选择评分最高的选项
├── 4. 点击选中的选项
│   ├── 滚动到选项位置
│   ├── 点击选项链接
│   └── 等待页面跳转
└── 5. 验证最终页面
    ├── 检查页面URL变化
    ├── 验证页面标题
    └── 确认成绩表格存在
```

#### 4.5.2 智能选项选择算法
```python
def smart_select_option(options: List[Dict]) -> Optional[Dict]:
    """智能选择查询选项算法"""

    # 关键词权重配置
    keyword_weights = {
        "当次": 10,      # 最高优先级
        "成绩": 8,
        "查询": 6,
        "考试": 5,
        "结果": 4,
        "分数": 3
    }

    best_option = None
    best_score = 0

    for option in options:
        score = 0
        text = option.get("text", "").lower()

        # 计算关键词匹配得分
        for keyword, weight in keyword_weights.items():
            if keyword in text:
                score += weight

        # 长度惩罚 (避免选择过长的选项)
        if len(text) > 20:
            score -= 2

        # 更新最佳选项
        if score > best_score:
            best_score = score
            best_option = option

    return best_option
```

### 4.6 截图流程详细说明

#### 4.6.1 截图流程步骤
```
截图流程 (capture_score_screenshot)
├── 1. 表格定位
│   ├── 使用配置的表格选择器
│   ├── 通用表格选择器查找
│   └── 智能表格识别算法
├── 2. 截图质量优化
│   ├── 页面缩放调整
│   ├── 等待内容加载完成
│   └── 滚动到表格位置
├── 3. 多策略截图
│   ├── 策略1: 精确表格截图
│   ├── 策略2: 可视区域截图
│   └── 策略3: 全页面截图
├── 4. 截图验证
│   ├── 检查文件大小 (>1KB)
│   ├── 验证图片格式
│   └── 确认截图质量
└── 5. 文件保存
    ├── 按学员姓名命名
    ├── 保存到指定目录
    └── 记录文件路径
```

#### 4.6.2 多策略截图实现
```python
def capture_with_multiple_strategies(student_name: str) -> Optional[str]:
    """多策略截图实现"""

    strategies = [
        # 策略1: 精确表格截图 (优先)
        {
            "name": "精确表格截图",
            "method": lambda: capture_table_screenshot(student_name),
            "priority": 1
        },

        # 策略2: 可视区域截图 (备选)
        {
            "name": "可视区域截图",
            "method": lambda: capture_viewport_screenshot(student_name),
            "priority": 2
        },

        # 策略3: 全页面截图 (最后备选)
        {
            "name": "全页面截图",
            "method": lambda: capture_fullpage_screenshot(student_name),
            "priority": 3
        }
    ]

    for strategy in strategies:
        try:
            log_info(f"尝试{strategy['name']}")
            result = strategy["method"]()

            if result and validate_screenshot(result):
                log_success(f"{strategy['name']}成功")
                return result
            else:
                log_warning(f"{strategy['name']}失败，尝试下一策略")

        except Exception as e:
            log_error(f"{strategy['name']}异常: {e}")

    return None
```

### 4.7 错误处理和恢复流程

#### 4.7.1 多层级错误恢复
```
错误恢复机制
├── 页面级恢复
│   ├── 页面刷新
│   ├── 重新加载元素
│   └── 重置页面状态
├── 上下文级恢复
│   ├── 关闭当前上下文
│   ├── 创建新的上下文
│   └── 重新初始化页面
├── 浏览器级恢复
│   ├── 检查浏览器连接
│   ├── 重新启动浏览器
│   └── 完全重新初始化
└── 系统级恢复
    ├── 记录错误信息
    ├── 跳过当前学员
    └── 继续处理下一个
```

#### 4.7.2 重试机制设计
```python
def process_with_retry(student: dict, max_retries: int = 3) -> Tuple[bool, str]:
    """带重试的学员处理"""

    for attempt in range(max_retries):
        try:
            # 执行正常处理流程
            success, message = process_single_student(student)

            if success:
                if attempt > 0:
                    log_success(f"学员在第{attempt + 1}次尝试后成功")
                return True, message

            # 处理失败，尝试恢复
            if attempt < max_retries - 1:
                log_info(f"第{attempt + 1}次尝试失败，准备重试")

                # 页面级恢复
                if browser_manager.recover_from_page_error():
                    time.sleep(2)  # 等待恢复完成
                    continue
                else:
                    log_warning("页面恢复失败")
                    return False, f"恢复失败: {message}"

        except Exception as e:
            log_error(f"处理异常: {e}")

            if attempt < max_retries - 1:
                # 尝试从异常中恢复
                if browser_manager.recover_from_page_error():
                    continue

            return False, f"异常处理失败: {str(e)}"

    return False, "所有重试尝试均失败"
```

### 4.8 数据流转和状态管理

#### 4.8.1 数据流转路径
```
数据流转路径
├── 输入数据流
│   ├── CSV/Excel文件 → DataManager
│   ├── 配置文件 → ConfigManager
│   └── 环境变量 → ConfigManager
├── 处理数据流
│   ├── 学员信息 → LoginManager
│   ├── 登录状态 → NavigationManager
│   ├── 页面对象 → ScreenshotManager
│   └── 处理结果 → LoggerManager
└── 输出数据流
    ├── 截图文件 → 文件系统
    ├── 日志文件 → 文件系统
    └── 统计报告 → 控制台/文件
```

#### 4.8.2 状态管理机制
```python
class ProcessingState:
    """处理状态管理"""

    def __init__(self):
        self.current_student = None
        self.current_page = None
        self.processing_stage = "初始化"
        self.error_count = 0
        self.success_count = 0

    def update_stage(self, stage: str, student_name: str = None):
        """更新处理阶段"""
        self.processing_stage = stage
        if student_name:
            self.current_student = student_name

        log_info(f"阶段更新: {stage} - {student_name or '系统'}")

    def record_result(self, success: bool):
        """记录处理结果"""
        if success:
            self.success_count += 1
        else:
            self.error_count += 1

    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total = self.success_count + self.error_count
        return {
            "total": total,
            "success": self.success_count,
            "failed": self.error_count,
            "success_rate": (self.success_count / total * 100) if total > 0 else 0
        }
```

---

## 5. 技术亮点

### 5.1 密码修改检测系统 - 核心技术创新

密码修改检测系统是本项目的核心技术亮点，采用了分层验证机制、智能元素查找、容错保护等多项创新技术，大幅提高了密码修改页面的检测准确率和处理成功率。

#### 5.1.1 分层验证机制 - 三层递进式检测

系统采用三层递进式验证机制，从粗略到精确，从快速到深入，确保检测的准确性和效率。

```python
def detect_password_change_page(self, page: Page) -> Tuple[bool, str]:
    """三层验证机制实现"""

    # 第一层：粗略检测 (5秒超时)
    layer1_result = self._layer1_rough_detection(page)
    if not layer1_result["passed"]:
        return False, f"第一层检测失败: {layer1_result['reason']}"

    # 第二层：精确验证 (10秒超时)
    layer2_result = self._layer2_precise_verification(page)
    if not layer2_result["passed"]:
        return False, f"第二层验证失败: {layer2_result['reason']}"

    # 第三层：功能验证 (15秒超时)
    layer3_result = self._layer3_functional_verification(page)
    if not layer3_result["passed"]:
        return False, f"第三层验证失败: {layer3_result['reason']}"

    return True, "三层验证全部通过，确认为密码修改页面"
```

**第一层：粗略检测**
- **检测目标**：快速判断页面是否可能包含密码修改功能
- **检测方法**：关键词匹配 + 基础选择器检测
- **超时设置**：5秒，确保快速响应
- **关键词列表**：
  ```python
  keywords = [
      "修改密码", "密码修改", "修改登录密码",
      "change password", "password change", "update password",
      "新密码", "确认密码", "重新输入密码"
  ]
  ```

**第二层：精确验证**
- **检测目标**：精确定位密码输入框元素
- **检测方法**：6种智能元素查找策略
- **超时设置**：10秒，平衡准确性和效率
- **验证标准**：至少找到2个密码输入框

**第三层：功能验证**
- **检测目标**：验证页面功能的完整性和可用性
- **检测方法**：交互性测试 + 页面结构验证
- **超时设置**：15秒，确保充分验证
- **验证内容**：元素可见性、可交互性、表单完整性

#### 5.1.2 智能元素查找 - 6种策略组合

系统实现了6种智能元素查找策略，大幅提高了元素定位的成功率和适应性。

```python
def _find_password_inputs(self, page: Page) -> List[Locator]:
    """6种智能元素查找策略"""

    strategies = [
        ("配置选择器查找", self._strategy_config_selectors),
        ("通用选择器查找", self._strategy_common_selectors),
        ("iframe检测查找", self._strategy_iframe_detection),
        ("基于文本内容查找", self._strategy_text_content),
        ("基于位置关系查找", self._strategy_position_relation),
        ("动态等待查找", self._strategy_dynamic_wait)
    ]

    for strategy_name, strategy_func in strategies:
        try:
            elements = strategy_func(page)
            if elements:
                self.statistics['successful_strategy'] = strategy_name
                log_success(f"{strategy_name}成功，找到{len(elements)}个元素")
                return elements
        except Exception as e:
            log_warning(f"{strategy_name}失败: {e}")

    return []
```

**策略1：配置选择器查找**
- **优先级**：最高
- **原理**：使用用户自定义的选择器配置
- **适用场景**：已知页面结构的情况
- **配置示例**：
  ```yaml
  password_change:
    new_password_selectors:
      - "#newPassword"
      - "input[name='newPwd']"
      - ".new-password-input"
  ```

**策略2：通用选择器查找**
- **优先级**：高
- **原理**：使用常见的密码输入框选择器
- **选择器列表**：
  ```python
  common_selectors = [
      "input[type='password']",
      "input[name*='password']",
      "input[id*='password']",
      "input[placeholder*='密码']",
      ".password-input", ".pwd-input"
  ]
  ```

**策略3：iframe检测查找**
- **优先级**：中高
- **原理**：检测页面中的iframe并在其中查找元素
- **实现方式**：
  ```python
  def _strategy_iframe_detection(self, page: Page) -> List[Locator]:
      elements = []
      iframes = page.query_selector_all("iframe")

      for iframe in iframes:
          try:
              frame_content = iframe.content_frame()
              if frame_content:
                  frame_elements = frame_content.query_selector_all("input[type='password']")
                  elements.extend(frame_elements)
          except Exception as e:
              log_debug(f"iframe检测异常: {e}")

      return elements
  ```

**策略4：基于文本内容查找**
- **优先级**：中
- **原理**：通过label文本和关键词查找相关输入框
- **实现逻辑**：
  ```python
  def _strategy_text_content(self, page: Page) -> List[Locator]:
      elements = []

      # 查找包含密码相关文本的label
      labels = page.query_selector_all("label")
      for label in labels:
          text = label.inner_text().lower()
          if any(keyword in text for keyword in ["密码", "password"]):
              # 查找关联的输入框
              for_attr = label.get_attribute("for")
              if for_attr:
                  input_element = page.query_selector(f"#{for_attr}")
                  if input_element:
                      elements.append(input_element)

      return elements
  ```

**策略5：基于位置关系查找**
- **优先级**：中低
- **原理**：通过相邻元素的位置关系查找输入框
- **查找逻辑**：查找密码相关文本附近的输入框

**策略6：动态等待查找**
- **优先级**：最低
- **原理**：等待页面动态加载完成后再查找
- **等待策略**：网络空闲、DOM稳定、自定义等待条件

#### 5.1.3 容错保护机制 - 多重安全保障

系统实现了完善的容错保护机制，确保在各种异常情况下都能稳定运行。

```python
class PasswordChangeHandler:
    def __init__(self):
        # 超时控制配置
        self.max_detection_time = 30  # 检测超时
        self.max_change_time = 60     # 修改超时
        self.retry_delay = 2          # 重试延迟
        self.max_retries = 3          # 最大重试次数

        # 容错配置
        self.auto_skip_on_failure = True    # 失败自动跳过
        self.failure_threshold = 5          # 失败阈值
        self.timeout_threshold = 3          # 超时阈值

        # 统计信息
        self.statistics = {
            'detection_attempts': 0,
            'detection_successes': 0,
            'change_attempts': 0,
            'change_successes': 0,
            'total_timeouts': 0,
            'total_retries': 0,
            'auto_skips': 0,
            'failure_recoveries': 0
        }
```

**超时保护机制**
```python
def _is_timeout(self, start_time: float, max_time: int, operation: str) -> bool:
    """检查是否超时"""
    elapsed = time.time() - start_time
    if elapsed > max_time:
        self.statistics['total_timeouts'] += 1
        log_warning(f"{operation}超时: {elapsed:.2f}秒 > {max_time}秒")
        return True
    return False
```

**自动跳过机制**
```python
def _should_auto_skip(self) -> bool:
    """判断是否应该自动跳过"""
    failure_rate = (self.statistics['detection_attempts'] -
                   self.statistics['detection_successes']) / max(1, self.statistics['detection_attempts'])

    timeout_rate = self.statistics['total_timeouts'] / max(1, self.statistics['detection_attempts'])

    if failure_rate > 0.8 or timeout_rate > 0.6:
        log_info(f"失败率过高({failure_rate:.2f})或超时率过高({timeout_rate:.2f})，启用自动跳过")
        return True

    return False
```

**重试机制**
```python
def handle_password_change_with_retry(self, page: Page, student_name: str,
                                    new_password: str) -> Tuple[bool, str]:
    """带重试的密码修改处理"""

    for attempt in range(self.max_retries):
        try:
            start_time = time.time()

            # 检查是否应该自动跳过
            if self.auto_skip_on_failure and self._should_auto_skip():
                self.statistics['auto_skips'] += 1
                return False, "自动跳过：失败率或超时率过高"

            # 执行密码修改
            success, message = self._handle_password_change(page, student_name, new_password)

            if success:
                return True, message

            # 失败处理
            if attempt < self.max_retries - 1:
                self.statistics['total_retries'] += 1

                # 检查重试前是否超时
                if self._is_timeout(start_time, self.max_change_time, "密码修改处理"):
                    break

                # 等待后重试
                time.sleep(self.retry_delay)

                # 尝试页面恢复
                try:
                    page.wait_for_load_state("networkidle", timeout=5000)
                    self.statistics['failure_recoveries'] += 1
                except Exception:
                    pass

        except Exception as e:
            log_error(f"密码修改异常: {e}")
            if attempt == self.max_retries - 1:
                return False, f"处理异常: {str(e)}"

    return False, "所有重试尝试均失败"
```

#### 5.1.4 调试信息系统 - 完善的问题追溯

系统实现了完善的调试信息保存机制，便于问题诊断和系统优化。

```python
def _save_debug_info(self, page: Page, detection_result: Dict, reason: str):
    """保存调试信息"""

    timestamp = int(time.time())
    debug_info = {
        "timestamp": timestamp,
        "detection_result": detection_result,
        "reason": reason,
        "page_info": {
            "url": page.url,
            "title": page.title(),
            "viewport": page.viewport_size
        },
        "statistics": self.statistics.copy()
    }

    # 保存HTML内容
    html_content = page.content()
    html_file = self.debug_dir / f"page_content_{timestamp}.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

    # 保存截图
    screenshot_file = self.debug_dir / f"page_screenshot_{timestamp}.png"
    page.screenshot(path=str(screenshot_file))

    # 保存JSON详情
    json_file = self.debug_dir / f"debug_info_{timestamp}.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(debug_info, f, ensure_ascii=False, indent=2)

    log_debug(f"调试信息已保存: {json_file}")
```

### 5.2 多策略截图系统

#### 5.2.1 智能表格定位算法
```python
def locate_score_table(self) -> Optional[Locator]:
    """智能表格定位算法"""

    # 策略1: 配置选择器定位
    for selector in SELECTORS["score_table"]:
        try:
            element = self.page.query_selector(selector)
            if element and element.is_visible():
                return element
        except Exception:
            continue

    # 策略2: 通用表格选择器
    table_selectors = [
        "table.score-table", "table.result-table",
        ".score-content table", ".result-content table",
        "table:has(th:contains('科目'))", "table:has(th:contains('成绩'))"
    ]

    for selector in table_selectors:
        try:
            element = self.page.query_selector(selector)
            if element and self._validate_table_content(element):
                return element
        except Exception:
            continue

    # 策略3: 智能内容识别
    tables = self.page.query_selector_all("table")
    for table in tables:
        if self._is_score_table(table):
            return table

    return None

def _is_score_table(self, table: Locator) -> bool:
    """判断是否为成绩表格"""
    try:
        text_content = table.inner_text().lower()
        score_keywords = ["成绩", "分数", "科目", "课程", "考试", "结果"]

        keyword_count = sum(1 for keyword in score_keywords if keyword in text_content)
        return keyword_count >= 2
    except Exception:
        return False
```

#### 5.2.2 质量优化机制
```python
def optimize_screenshot_quality(self) -> bool:
    """截图质量优化"""

    try:
        # 1. 页面缩放优化
        self.page.evaluate("document.body.style.zoom = '1.0'")

        # 2. 等待内容完全加载
        self.page.wait_for_load_state("networkidle")
        self.page.wait_for_timeout(2000)

        # 3. 移除可能影响截图的元素
        self.page.evaluate("""
            // 移除固定定位的遮挡元素
            const fixedElements = document.querySelectorAll('[style*="position: fixed"]');
            fixedElements.forEach(el => el.style.display = 'none');

            // 移除广告和弹窗
            const ads = document.querySelectorAll('.ad, .advertisement, .popup');
            ads.forEach(el => el.style.display = 'none');
        """)

        # 4. 优化表格显示
        self.page.evaluate("""
            const tables = document.querySelectorAll('table');
            tables.forEach(table => {
                table.style.border = '1px solid #ccc';
                table.style.borderCollapse = 'collapse';
            });
        """)

        return True

    except Exception as e:
        log_warning(f"截图质量优化失败: {e}")
        return False
```

### 5.3 智能验证码识别系统

#### 5.3.1 多模型兼容机制
```python
def __init__(self):
    """验证码识别器初始化"""
    self.ocr = None

    if DDDDOCR_AVAILABLE:
        try:
            # 尝试新版本初始化方式
            try:
                self.ocr = ddddocr.DdddOcr(show_ad=False)
            except TypeError:
                # 新版本不支持show_ad参数
                self.ocr = ddddocr.DdddOcr()

            log_success("验证码识别器初始化成功")
        except Exception as e:
            log_error(f"验证码识别器初始化失败: {e}")
            self.ocr = None
```

#### 5.3.2 PIL兼容性处理
```python
def recognize_captcha(self, image_bytes: bytes) -> Optional[str]:
    """验证码识别 - 处理PIL兼容性问题"""

    try:
        # 使用ddddocr识别
        result = self.ocr.classification(image_bytes)
    except AttributeError as e:
        if "ANTIALIAS" in str(e):
            # PIL版本兼容性问题，自动修复
            import PIL.Image
            if not hasattr(PIL.Image, 'ANTIALIAS'):
                PIL.Image.ANTIALIAS = PIL.Image.LANCZOS
            result = self.ocr.classification(image_bytes)
        else:
            raise e

    return str(result).strip() if result else None
```

---

## 6. 配置系统

### 6.1 多层级配置架构

系统采用多层级配置架构，支持默认配置、文件配置、环境变量三级覆盖机制，确保配置的灵活性和可维护性。

#### 6.1.1 配置加载优先级
```
配置加载优先级 (从低到高)
├── 1. 默认配置 (defaults.py)
│   ├── 基础功能配置
│   ├── 默认超时设置
│   ├── 标准选择器
│   └── 系统默认值
├── 2. 配置文件 (config.yaml/config.json)
│   ├── 用户自定义配置
│   ├── 环境特定设置
│   ├── 选择器覆盖
│   └── 功能开关
└── 3. 环境变量
    ├── EXAM_HEADLESS=true
    ├── EXAM_MAX_RETRIES=5
    ├── EXAM_LOG_LEVEL=DEBUG
    └── 运行时参数覆盖
```

#### 6.1.2 配置管理器实现
```python
class ConfigManager:
    """配置管理器 - 多层级配置加载"""

    def __init__(self, config_file: Optional[Union[str, Path]] = None):
        self.config_file = Path(config_file) if config_file else None
        self.config = {}
        self.selectors = {}
        self._load_all_configs()

    def _load_all_configs(self) -> None:
        """加载所有配置"""
        # 1. 加载默认配置
        self._load_default_config()

        # 2. 加载配置文件
        self._load_config_file()

        # 3. 加载环境变量覆盖
        self._load_env_overrides()

    def _load_default_config(self) -> None:
        """加载默认配置"""
        self.config = deepcopy(DEFAULT_CONFIG)
        self.selectors = deepcopy(DEFAULT_SELECTORS)

    def _load_config_file(self) -> None:
        """加载配置文件"""
        if not self.config_file or not self.config_file.exists():
            return

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                if self.config_file.suffix.lower() == '.yaml':
                    file_config = yaml.safe_load(f)
                else:
                    file_config = json.load(f)

            # 合并配置
            self._merge_config(file_config)

        except Exception as e:
            log_error(f"配置文件加载失败: {e}")

    def _load_env_overrides(self) -> None:
        """加载环境变量覆盖"""
        env_mappings = {
            "EXAM_HEADLESS": ("headless", lambda x: x.lower() == 'true'),
            "EXAM_MAX_RETRIES": ("max_retries", int),
            "EXAM_LOG_LEVEL": ("log_level", str),
            "EXAM_SCREENSHOT_DIR": ("screenshot_dir", Path),
            "EXAM_PAGE_TIMEOUT": ("page_timeout", int),
            "EXAM_NAVIGATION_TIMEOUT": ("navigation_timeout", int)
        }

        for env_key, (config_key, converter) in env_mappings.items():
            env_value = os.getenv(env_key)
            if env_value is not None:
                try:
                    self.config[config_key] = converter(env_value)
                    log_info(f"环境变量覆盖: {config_key} = {self.config[config_key]}")
                except Exception as e:
                    log_warning(f"环境变量转换失败 {env_key}: {e}")
```

### 6.2 选择器配置系统

#### 6.2.1 选择器配置结构
```yaml
# config.yaml - 选择器配置示例
selectors:
  # 登录相关选择器
  login:
    username_input:
      - "#username"
      - "input[name='username']"
      - "input[placeholder*='用户名']"

    password_input:
      - "#password"
      - "input[name='password']"
      - "input[type='password']"

    captcha_image:
      - "#captcha_img"
      - ".captcha-image"
      - "img[src*='captcha']"

    captcha_input:
      - "#captcha"
      - "input[name='captcha']"
      - ".captcha-input"

    login_button:
      - "#login_btn"
      - "input[type='submit']"
      - "button[type='submit']"

  # 密码修改相关选择器
  password_change:
    new_password_selectors:
      - "#newPassword"
      - "#mypwd"
      - "input[name='newPwd']"
      - "input[placeholder*='新密码']"

    confirm_password_selectors:
      - "#confirmPassword"
      - "#mypwd2"
      - "input[name='confirmPwd']"
      - "input[placeholder*='确认密码']"

    submit_button_selectors:
      - "#btnsubmit"
      - "#submit_btn"
      - "input[value*='提交']"
      - "button:contains('确定')"

  # 导航相关选择器
  navigation:
    score_query_links:
      - "a:contains('成绩查询')"
      - "a:contains('当次成绩')"
      - ".menu-item:contains('查询')"

    query_options:
      - ".query-option"
      - ".score-link"
      - "a[href*='score']"

  # 截图相关选择器
  screenshot:
    score_table:
      - "table.score-table"
      - ".result-table"
      - "table:has(th:contains('科目'))"
      - "#score_content table"
```

#### 6.2.2 选择器使用机制
```python
class CompatibleSelectorsDict(dict):
    """兼容性选择器字典"""

    def __getitem__(self, key):
        """获取选择器列表"""
        try:
            value = super().__getitem__(key)

            # 确保返回列表格式
            if isinstance(value, str):
                return [value]
            elif isinstance(value, list):
                return value
            else:
                log_warning(f"选择器配置格式错误: {key}")
                return []

        except KeyError:
            log_warning(f"选择器未配置: {key}")
            return []

    def get_nested(self, *keys, default=None):
        """获取嵌套选择器配置"""
        current = self

        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default or []

        return current if isinstance(current, list) else [current] if current else []

# 使用示例
def find_element_by_selectors(page: Page, selector_key: str) -> Optional[Locator]:
    """使用选择器配置查找元素"""

    selectors = SELECTORS[selector_key]

    for selector in selectors:
        try:
            element = page.query_selector(selector)
            if element and element.is_visible():
                log_success(f"选择器匹配成功: {selector}")
                return element
        except Exception as e:
            log_debug(f"选择器失败 {selector}: {e}")

    log_warning(f"所有选择器均失败: {selector_key}")
    return None
```

### 6.3 配置扩展机制

#### 6.3.1 自定义配置支持
```python
def load_custom_config(custom_config_path: str):
    """加载自定义配置"""

    try:
        with open(custom_config_path, 'r', encoding='utf-8') as f:
            custom_config = yaml.safe_load(f)

        # 验证配置格式
        if not isinstance(custom_config, dict):
            raise ValueError("配置文件格式错误")

        # 合并到全局配置
        CONFIG.update(custom_config.get('config', {}))
        SELECTORS.update(custom_config.get('selectors', {}))

        log_success(f"自定义配置加载成功: {custom_config_path}")

    except Exception as e:
        log_error(f"自定义配置加载失败: {e}")
```

#### 6.3.2 运行时配置更新
```python
def update_config(updates: Dict[str, Any]):
    """运行时配置更新"""

    for key, value in updates.items():
        if key in CONFIG:
            old_value = CONFIG[key]
            CONFIG[key] = value
            log_info(f"配置更新: {key} = {value} (原值: {old_value})")
        else:
            log_warning(f"未知配置项: {key}")

def update_selectors(updates: Dict[str, Any]):
    """运行时选择器更新"""

    for key, value in updates.items():
        SELECTORS[key] = value if isinstance(value, list) else [value]
        log_info(f"选择器更新: {key} = {value}")

# 使用示例
update_config({
    "headless": True,
    "max_retries": 5,
    "page_timeout": 45000
})

update_selectors({
    "login_button": ["#new_login_btn", ".login-submit"],
    "score_table": ["table.new-score-table"]
})
```

---

## 7. 日志和调试

### 7.1 日志系统架构

系统采用基于loguru的现代化日志架构，支持多级日志记录、自动文件轮转、结构化日志输出等功能，为系统监控和问题诊断提供完善的支持。

#### 7.1.1 日志系统设计
```python
class LoggerManager:
    """日志管理器 - 统一日志管理"""

    def __init__(self):
        self.log_dir = Path(CONFIG["log_dir"])
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # 会话数据
        self.session_data = {
            "start_time": None,
            "end_time": None,
            "statistics": {"total": 0, "success": 0, "failed": 0},
            "progress": {"current": 0, "total": 0}
        }

        self._setup_loggers()

    def _setup_loggers(self):
        """设置日志记录器"""
        # 移除默认处理器
        logger.remove()

        # 控制台输出 - 彩色日志
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level=CONFIG["log_level"],
            colorize=True
        )

        # 主日志文件 - 完整日志
        logger.add(
            self.log_dir / "auto_query.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="INFO",
            rotation=CONFIG["log_rotation"],  # 10 MB
            retention=CONFIG["log_retention"], # 7 days
            encoding="utf-8"
        )

        # 错误日志文件 - 仅错误
        logger.add(
            self.log_dir / "errors.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="ERROR",
            rotation=CONFIG["log_rotation"],
            retention=CONFIG["log_retention"],
            encoding="utf-8"
        )

        # 统计日志文件 - 结构化统计
        logger.add(
            self.log_dir / "statistics.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {message}",
            level="INFO",
            rotation=CONFIG["log_rotation"],
            retention=CONFIG["log_retention"],
            encoding="utf-8",
            filter=lambda record: "STATS" in record["extra"]
        )
```

#### 7.1.2 多级日志记录
```python
# 便捷日志函数
def log_info(message: str, category: str = "INFO"):
    """记录信息日志"""
    logger.info(f"[{category}] {message}")

def log_success(message: str, category: str = "SUCCESS"):
    """记录成功日志"""
    logger.success(f"[{category}] {message}")

def log_warning(message: str, category: str = "WARNING"):
    """记录警告日志"""
    logger.warning(f"[{category}] {message}")

def log_error(category: str, error: Exception):
    """记录错误日志"""
    logger.error(f"[{category}] {type(error).__name__}: {str(error)}")

def log_debug(message: str, category: str = "DEBUG"):
    """记录调试日志"""
    logger.debug(f"[{category}] {message}")

# 使用示例
log_info("开始处理学员数据", "数据加载")
log_success("浏览器初始化成功", "浏览器")
log_warning("验证码识别失败，尝试重试", "验证码")
log_error("登录", Exception("用户名输入失败"))
log_debug("页面元素查找详情", "元素定位")
```

### 7.2 调试信息保存机制

#### 7.2.1 结构化调试信息
```python
def save_debug_info(self, page: Page, operation: str, details: Dict):
    """保存结构化调试信息"""

    timestamp = int(time.time())
    debug_info = {
        "timestamp": timestamp,
        "datetime": datetime.now().isoformat(),
        "operation": operation,
        "details": details,
        "page_info": {
            "url": page.url,
            "title": page.title(),
            "viewport": page.viewport_size,
            "user_agent": page.evaluate("navigator.userAgent")
        },
        "system_info": {
            "platform": platform.system(),
            "python_version": platform.python_version(),
            "memory_usage": psutil.virtual_memory().percent,
            "cpu_usage": psutil.cpu_percent()
        }
    }

    # 保存HTML内容
    html_file = self.debug_dir / f"{operation}_{timestamp}.html"
    try:
        html_content = page.content()
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        debug_info["html_file"] = str(html_file)
    except Exception as e:
        log_warning(f"HTML保存失败: {e}")

    # 保存页面截图
    screenshot_file = self.debug_dir / f"{operation}_{timestamp}.png"
    try:
        page.screenshot(path=str(screenshot_file), full_page=True)
        debug_info["screenshot_file"] = str(screenshot_file)
    except Exception as e:
        log_warning(f"截图保存失败: {e}")

    # 保存JSON详情
    json_file = self.debug_dir / f"{operation}_{timestamp}.json"
    try:
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(debug_info, f, ensure_ascii=False, indent=2)
        log_debug(f"调试信息已保存: {json_file}")
    except Exception as e:
        log_error(f"调试信息保存失败: {e}")
```

#### 7.2.2 问题追溯机制
```python
class ProblemTracker:
    """问题追溯器"""

    def __init__(self):
        self.problems = []
        self.context_stack = []

    def enter_context(self, context: str, details: Dict = None):
        """进入上下文"""
        context_info = {
            "context": context,
            "timestamp": time.time(),
            "details": details or {}
        }
        self.context_stack.append(context_info)
        log_debug(f"进入上下文: {context}")

    def exit_context(self):
        """退出上下文"""
        if self.context_stack:
            context_info = self.context_stack.pop()
            duration = time.time() - context_info["timestamp"]
            log_debug(f"退出上下文: {context_info['context']} (耗时: {duration:.2f}秒)")

    def record_problem(self, problem_type: str, description: str,
                      severity: str = "WARNING", context: Dict = None):
        """记录问题"""
        problem = {
            "type": problem_type,
            "description": description,
            "severity": severity,
            "timestamp": time.time(),
            "datetime": datetime.now().isoformat(),
            "context": context or {},
            "context_stack": self.context_stack.copy()
        }

        self.problems.append(problem)

        # 根据严重程度记录日志
        if severity == "ERROR":
            log_error(problem_type, Exception(description))
        elif severity == "WARNING":
            log_warning(description, problem_type)
        else:
            log_info(description, problem_type)

    def get_problem_summary(self) -> Dict:
        """获取问题摘要"""
        summary = {
            "total_problems": len(self.problems),
            "by_severity": {},
            "by_type": {},
            "recent_problems": self.problems[-10:]  # 最近10个问题
        }

        for problem in self.problems:
            # 按严重程度统计
            severity = problem["severity"]
            summary["by_severity"][severity] = summary["by_severity"].get(severity, 0) + 1

            # 按类型统计
            problem_type = problem["type"]
            summary["by_type"][problem_type] = summary["by_type"].get(problem_type, 0) + 1

        return summary

# 使用示例
tracker = ProblemTracker()

# 进入处理上下文
tracker.enter_context("学员登录", {"student": "张三", "attempt": 1})

# 记录问题
tracker.record_problem(
    problem_type="验证码识别",
    description="验证码识别失败，图像质量较差",
    severity="WARNING",
    context={"image_size": "120x40", "confidence": 0.3}
)

# 退出上下文
tracker.exit_context()
```

### 7.3 性能指标统计

#### 7.3.1 会话统计系统
```python
def start_session(self):
    """开始处理会话"""
    self.session_data["start_time"] = datetime.now()
    self.session_data["statistics"] = {"total": 0, "success": 0, "failed": 0}

    log_info("处理会话开始", "会话管理")

def end_session(self):
    """结束处理会话"""
    self.session_data["end_time"] = datetime.now()

    # 计算会话统计
    duration = self.session_data["end_time"] - self.session_data["start_time"]
    stats = self.session_data["statistics"]

    success_rate = (stats["success"] / stats["total"] * 100) if stats["total"] > 0 else 0

    # 记录统计日志
    stats_data = {
        "session_duration": str(duration),
        "total_processed": stats["total"],
        "success_count": stats["success"],
        "failed_count": stats["failed"],
        "success_rate": round(success_rate, 2),
        "avg_time_per_student": str(duration / stats["total"]) if stats["total"] > 0 else "0"
    }

    logger.bind(STATS=True).info(f"SESSION_STATS: {json.dumps(stats_data, ensure_ascii=False)}")

    # 生成会话报告
    self._generate_session_report(stats_data)

    log_info("处理会话结束", "会话管理")

def _generate_session_report(self, stats_data: Dict):
    """生成会话报告"""
    report_file = self.log_dir / f"session_report_{int(time.time())}.json"

    report = {
        "session_info": {
            "start_time": self.session_data["start_time"].isoformat(),
            "end_time": self.session_data["end_time"].isoformat(),
            "duration": stats_data["session_duration"]
        },
        "statistics": stats_data,
        "system_info": {
            "platform": platform.system(),
            "python_version": platform.python_version(),
            "memory_peak": psutil.virtual_memory().percent
        }
    }

    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        log_info(f"会话报告已生成: {report_file}", "会话管理")
    except Exception as e:
        log_error("会话管理", e)
```

#### 7.3.2 实时进度监控
```python
def start_progress_session(self, total_count: int):
    """开始进度跟踪会话"""
    self.session_data["progress"]["total"] = total_count
    self.session_data["progress"]["current"] = 0
    self.session_data["progress"]["start_time"] = time.time()

    log_info(f"开始进度跟踪，总计 {total_count} 项", "进度管理")

def update_progress(self, current: int, student_name: str, status: str):
    """更新处理进度"""
    self.session_data["progress"]["current"] = current

    # 计算进度百分比
    total = self.session_data["progress"]["total"]
    percentage = (current / total * 100) if total > 0 else 0

    # 计算预估剩余时间
    elapsed_time = time.time() - self.session_data["progress"]["start_time"]
    if current > 0:
        avg_time_per_item = elapsed_time / current
        remaining_items = total - current
        estimated_remaining = avg_time_per_item * remaining_items
        eta = datetime.now() + timedelta(seconds=estimated_remaining)
    else:
        eta = None

    # 记录进度日志
    progress_info = {
        "current": current,
        "total": total,
        "percentage": round(percentage, 1),
        "student_name": student_name,
        "status": status,
        "elapsed_time": round(elapsed_time, 2),
        "eta": eta.isoformat() if eta else None
    }

    logger.bind(STATS=True).info(f"PROGRESS: {json.dumps(progress_info, ensure_ascii=False)}")

    # 控制台进度显示
    print(f"\r进度: {current}/{total} ({percentage:.1f}%) - {student_name} - {status}", end="", flush=True)
```

---

## 8. 性能优化

### 8.1 浏览器资源管理优化

#### 8.1.1 单一浏览器实例模式
系统采用单一浏览器实例模式，避免重复创建浏览器实例带来的性能开销和资源浪费。

```python
class OptimizedBrowserManager:
    """优化的浏览器管理器"""

    def __init__(self):
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None

        # 性能监控
        self.performance_stats = {
            "browser_init_time": 0,
            "page_create_count": 0,
            "context_create_count": 0,
            "memory_usage_peak": 0,
            "total_navigation_time": 0
        }

    def init_browser_optimized(self) -> bool:
        """优化的浏览器初始化"""
        start_time = time.time()

        try:
            # 启动Playwright
            self.playwright = sync_playwright().start()

            # 优化的浏览器启动参数
            self.browser = self.playwright.chromium.launch(
                headless=CONFIG["headless"],
                slow_mo=CONFIG["slow_mo"],
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',  # 性能优化
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-dev-shm-usage',  # 减少内存使用
                    '--memory-pressure-off',
                    '--max_old_space_size=4096'  # 限制内存使用
                ]
            )

            # 创建优化的浏览器上下文
            self.context = self.browser.new_context(
                viewport={'width': CONFIG["viewport_width"], 'height': CONFIG["viewport_height"]},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                # 性能优化设置
                ignore_https_errors=True,
                bypass_csp=True,
                java_script_enabled=True,
                accept_downloads=False
            )

            # 设置优化的超时
            self.context.set_default_timeout(CONFIG["page_timeout"])
            self.context.set_default_navigation_timeout(CONFIG["navigation_timeout"])

            # 记录初始化时间
            self.performance_stats["browser_init_time"] = time.time() - start_time

            log_success(f"浏览器初始化完成，耗时: {self.performance_stats['browser_init_time']:.2f}秒", "性能")
            return True

        except Exception as e:
            log_error("浏览器初始化", e)
            return False

    def create_optimized_page(self) -> Optional[Page]:
        """创建优化的页面"""
        try:
            page = self.context.new_page()
            self.performance_stats["page_create_count"] += 1

            # 页面性能优化
            page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())  # 阻止资源加载

            # 设置页面事件监听
            page.on("console", lambda msg: log_debug(f"Console: {msg.text}", "页面"))
            page.on("pageerror", lambda error: log_error("页面", Exception(str(error))))

            return page

        except Exception as e:
            log_error("页面创建", e)
            return None
```

#### 8.1.2 内存管理优化
```python
def monitor_memory_usage(self):
    """监控内存使用情况"""
    try:
        # 获取系统内存信息
        memory = psutil.virtual_memory()
        process = psutil.Process()
        process_memory = process.memory_info()

        memory_stats = {
            "system_memory_percent": memory.percent,
            "system_memory_available": memory.available // (1024 * 1024),  # MB
            "process_memory_rss": process_memory.rss // (1024 * 1024),  # MB
            "process_memory_vms": process_memory.vms // (1024 * 1024)   # MB
        }

        # 更新峰值内存使用
        if memory_stats["process_memory_rss"] > self.performance_stats["memory_usage_peak"]:
            self.performance_stats["memory_usage_peak"] = memory_stats["process_memory_rss"]

        # 内存使用告警
        if memory.percent > 80:
            log_warning(f"系统内存使用率过高: {memory.percent:.1f}%", "性能")

        if memory_stats["process_memory_rss"] > 1024:  # 1GB
            log_warning(f"进程内存使用过高: {memory_stats['process_memory_rss']}MB", "性能")

        return memory_stats

    except Exception as e:
        log_error("内存监控", e)
        return {}

def cleanup_resources(self):
    """清理资源"""
    try:
        # 强制垃圾回收
        import gc
        gc.collect()

        # 清理浏览器缓存
        if self.context:
            self.context.clear_cookies()
            self.context.clear_permissions()

        log_debug("资源清理完成", "性能")

    except Exception as e:
        log_error("资源清理", e)
```

### 8.2 重试机制优化

#### 8.2.1 智能重试策略
```python
class SmartRetryManager:
    """智能重试管理器"""

    def __init__(self):
        self.retry_stats = {
            "total_retries": 0,
            "successful_retries": 0,
            "failed_operations": {},
            "retry_patterns": {}
        }

    def execute_with_smart_retry(self, operation_name: str, operation_func,
                                max_retries: int = 3, base_delay: float = 1.0):
        """智能重试执行"""

        for attempt in range(max_retries + 1):
            try:
                start_time = time.time()
                result = operation_func()

                # 记录成功
                if attempt > 0:
                    self.retry_stats["successful_retries"] += 1
                    log_success(f"{operation_name} 在第 {attempt + 1} 次尝试后成功", "重试")

                return True, result

            except Exception as e:
                operation_time = time.time() - start_time

                # 记录失败统计
                if operation_name not in self.retry_stats["failed_operations"]:
                    self.retry_stats["failed_operations"][operation_name] = 0
                self.retry_stats["failed_operations"][operation_name] += 1

                if attempt < max_retries:
                    self.retry_stats["total_retries"] += 1

                    # 计算智能延迟
                    delay = self._calculate_smart_delay(operation_name, attempt, base_delay, operation_time)

                    log_warning(f"{operation_name} 第 {attempt + 1} 次尝试失败: {e}，{delay:.1f}秒后重试", "重试")
                    time.sleep(delay)
                else:
                    log_error(f"{operation_name} 所有重试均失败", e)
                    return False, str(e)

        return False, "重试次数耗尽"

    def _calculate_smart_delay(self, operation_name: str, attempt: int,
                              base_delay: float, operation_time: float) -> float:
        """计算智能延迟"""

        # 基础指数退避
        exponential_delay = base_delay * (2 ** attempt)

        # 根据操作耗时调整
        if operation_time > 10:  # 操作耗时较长
            time_factor = 0.5  # 减少延迟
        elif operation_time < 1:  # 操作很快失败
            time_factor = 2.0  # 增加延迟
        else:
            time_factor = 1.0

        # 根据历史失败率调整
        failure_count = self.retry_stats["failed_operations"].get(operation_name, 0)
        if failure_count > 5:
            failure_factor = 1.5  # 增加延迟
        else:
            failure_factor = 1.0

        final_delay = exponential_delay * time_factor * failure_factor

        # 限制最大延迟
        return min(final_delay, 30.0)

    def get_retry_statistics(self) -> Dict:
        """获取重试统计"""
        total_operations = sum(self.retry_stats["failed_operations"].values()) + self.retry_stats["successful_retries"]

        return {
            "total_retries": self.retry_stats["total_retries"],
            "successful_retries": self.retry_stats["successful_retries"],
            "retry_success_rate": (self.retry_stats["successful_retries"] / max(1, self.retry_stats["total_retries"])) * 100,
            "failed_operations": self.retry_stats["failed_operations"],
            "most_problematic_operation": max(self.retry_stats["failed_operations"].items(),
                                            key=lambda x: x[1], default=("无", 0))
        }
```

### 8.3 超时控制优化

#### 8.3.1 分层超时管理
```python
class TimeoutManager:
    """超时管理器"""

    def __init__(self):
        self.timeout_config = {
            "page_load": CONFIG.get("page_timeout", 30000),
            "navigation": CONFIG.get("navigation_timeout", 20000),
            "element_wait": CONFIG.get("element_timeout", 10000),
            "operation": CONFIG.get("operation_timeout", 15000),
            "network_idle": 5000
        }

        self.timeout_stats = {
            "total_timeouts": 0,
            "timeout_by_type": {},
            "avg_operation_time": {}
        }

    def execute_with_timeout(self, operation_name: str, operation_func,
                           timeout_type: str = "operation", custom_timeout: int = None):
        """带超时的操作执行"""

        timeout_ms = custom_timeout or self.timeout_config[timeout_type]
        start_time = time.time()

        try:
            # 使用信号实现超时控制 (仅Unix系统)
            if hasattr(signal, 'SIGALRM'):
                def timeout_handler(signum, frame):
                    raise TimeoutError(f"{operation_name} 操作超时 ({timeout_ms}ms)")

                old_handler = signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(timeout_ms // 1000)

                try:
                    result = operation_func()
                    signal.alarm(0)  # 取消超时
                    signal.signal(signal.SIGALRM, old_handler)

                    # 记录操作时间
                    operation_time = (time.time() - start_time) * 1000
                    self._record_operation_time(operation_name, operation_time)

                    return True, result

                except TimeoutError:
                    signal.signal(signal.SIGALRM, old_handler)
                    self._record_timeout(operation_name, timeout_type)
                    return False, f"操作超时: {timeout_ms}ms"
            else:
                # Windows系统使用线程超时
                import threading
                result = [None]
                exception = [None]

                def target():
                    try:
                        result[0] = operation_func()
                    except Exception as e:
                        exception[0] = e

                thread = threading.Thread(target=target)
                thread.start()
                thread.join(timeout_ms / 1000)

                if thread.is_alive():
                    self._record_timeout(operation_name, timeout_type)
                    return False, f"操作超时: {timeout_ms}ms"

                if exception[0]:
                    raise exception[0]

                operation_time = (time.time() - start_time) * 1000
                self._record_operation_time(operation_name, operation_time)

                return True, result[0]

        except Exception as e:
            return False, str(e)

    def _record_timeout(self, operation_name: str, timeout_type: str):
        """记录超时事件"""
        self.timeout_stats["total_timeouts"] += 1

        if timeout_type not in self.timeout_stats["timeout_by_type"]:
            self.timeout_stats["timeout_by_type"][timeout_type] = 0
        self.timeout_stats["timeout_by_type"][timeout_type] += 1

        log_warning(f"{operation_name} 超时 (类型: {timeout_type})", "超时管理")

    def _record_operation_time(self, operation_name: str, operation_time: float):
        """记录操作时间"""
        if operation_name not in self.timeout_stats["avg_operation_time"]:
            self.timeout_stats["avg_operation_time"][operation_name] = []

        self.timeout_stats["avg_operation_time"][operation_name].append(operation_time)

        # 保持最近100次记录
        if len(self.timeout_stats["avg_operation_time"][operation_name]) > 100:
            self.timeout_stats["avg_operation_time"][operation_name].pop(0)

    def get_timeout_statistics(self) -> Dict:
        """获取超时统计"""
        avg_times = {}
        for operation, times in self.timeout_stats["avg_operation_time"].items():
            if times:
                avg_times[operation] = sum(times) / len(times)

        return {
            "total_timeouts": self.timeout_stats["total_timeouts"],
            "timeout_by_type": self.timeout_stats["timeout_by_type"],
            "average_operation_times": avg_times,
            "timeout_rate": (self.timeout_stats["total_timeouts"] /
                           max(1, sum(len(times) for times in self.timeout_stats["avg_operation_time"].values()))) * 100
        }
```

### 8.4 统计监控系统

#### 8.4.1 实时性能监控
```python
class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.metrics = {
            "operation_times": {},
            "success_rates": {},
            "resource_usage": [],
            "error_patterns": {},
            "performance_trends": {}
        }

        self.monitoring_active = False
        self.monitor_thread = None

    def start_monitoring(self):
        """开始性能监控"""
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        log_info("性能监控已启动", "监控")

    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        log_info("性能监控已停止", "监控")

    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集系统资源使用情况
                resource_data = {
                    "timestamp": time.time(),
                    "cpu_percent": psutil.cpu_percent(),
                    "memory_percent": psutil.virtual_memory().percent,
                    "disk_io": psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {},
                    "network_io": psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
                }

                self.metrics["resource_usage"].append(resource_data)

                # 保持最近1000条记录
                if len(self.metrics["resource_usage"]) > 1000:
                    self.metrics["resource_usage"].pop(0)

                # 检查性能告警
                self._check_performance_alerts(resource_data)

                time.sleep(5)  # 每5秒监控一次

            except Exception as e:
                log_error("性能监控", e)
                time.sleep(10)

    def _check_performance_alerts(self, resource_data: Dict):
        """检查性能告警"""
        # CPU使用率告警
        if resource_data["cpu_percent"] > 80:
            log_warning(f"CPU使用率过高: {resource_data['cpu_percent']:.1f}%", "性能告警")

        # 内存使用率告警
        if resource_data["memory_percent"] > 85:
            log_warning(f"内存使用率过高: {resource_data['memory_percent']:.1f}%", "性能告警")

    def record_operation_metric(self, operation: str, duration: float, success: bool):
        """记录操作指标"""
        # 记录操作时间
        if operation not in self.metrics["operation_times"]:
            self.metrics["operation_times"][operation] = []
        self.metrics["operation_times"][operation].append(duration)

        # 记录成功率
        if operation not in self.metrics["success_rates"]:
            self.metrics["success_rates"][operation] = {"total": 0, "success": 0}

        self.metrics["success_rates"][operation]["total"] += 1
        if success:
            self.metrics["success_rates"][operation]["success"] += 1

    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        summary = {
            "operation_performance": {},
            "resource_usage_avg": {},
            "success_rates": {},
            "performance_trends": {}
        }

        # 操作性能统计
        for operation, times in self.metrics["operation_times"].items():
            if times:
                summary["operation_performance"][operation] = {
                    "avg_time": sum(times) / len(times),
                    "min_time": min(times),
                    "max_time": max(times),
                    "total_operations": len(times)
                }

        # 资源使用平均值
        if self.metrics["resource_usage"]:
            cpu_values = [r["cpu_percent"] for r in self.metrics["resource_usage"]]
            memory_values = [r["memory_percent"] for r in self.metrics["resource_usage"]]

            summary["resource_usage_avg"] = {
                "avg_cpu": sum(cpu_values) / len(cpu_values),
                "avg_memory": sum(memory_values) / len(memory_values),
                "peak_cpu": max(cpu_values),
                "peak_memory": max(memory_values)
            }

        # 成功率统计
        for operation, stats in self.metrics["success_rates"].items():
            if stats["total"] > 0:
                summary["success_rates"][operation] = {
                    "success_rate": (stats["success"] / stats["total"]) * 100,
                    "total_attempts": stats["total"],
                    "successful_attempts": stats["success"]
                }

        return summary
```

#### 8.4.2 性能优化建议系统
```python
class PerformanceOptimizer:
    """性能优化建议器"""

    def __init__(self, monitor: PerformanceMonitor):
        self.monitor = monitor

    def analyze_and_suggest(self) -> List[Dict]:
        """分析并提供优化建议"""
        suggestions = []
        summary = self.monitor.get_performance_summary()

        # 分析操作性能
        for operation, perf in summary.get("operation_performance", {}).items():
            if perf["avg_time"] > 10:  # 平均时间超过10秒
                suggestions.append({
                    "type": "操作性能",
                    "priority": "高",
                    "operation": operation,
                    "issue": f"操作平均耗时过长: {perf['avg_time']:.2f}秒",
                    "suggestion": "考虑优化操作逻辑或增加超时控制"
                })

        # 分析成功率
        for operation, rate in summary.get("success_rates", {}).items():
            if rate["success_rate"] < 80:  # 成功率低于80%
                suggestions.append({
                    "type": "成功率",
                    "priority": "高",
                    "operation": operation,
                    "issue": f"操作成功率过低: {rate['success_rate']:.1f}%",
                    "suggestion": "检查操作逻辑，增加重试机制或改进错误处理"
                })

        # 分析资源使用
        resource_avg = summary.get("resource_usage_avg", {})
        if resource_avg.get("avg_cpu", 0) > 60:
            suggestions.append({
                "type": "资源使用",
                "priority": "中",
                "operation": "系统",
                "issue": f"CPU平均使用率过高: {resource_avg['avg_cpu']:.1f}%",
                "suggestion": "考虑优化算法复杂度或增加处理间隔"
            })

        if resource_avg.get("avg_memory", 0) > 70:
            suggestions.append({
                "type": "资源使用",
                "priority": "中",
                "operation": "系统",
                "issue": f"内存平均使用率过高: {resource_avg['avg_memory']:.1f}%",
                "suggestion": "检查内存泄漏，优化数据结构或增加垃圾回收"
            })

        return suggestions

    def generate_optimization_report(self) -> str:
        """生成优化报告"""
        suggestions = self.analyze_and_suggest()
        summary = self.monitor.get_performance_summary()

        report = []
        report.append("# 性能优化报告")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 性能摘要
        report.append("## 性能摘要")
        if summary.get("resource_usage_avg"):
            resource = summary["resource_usage_avg"]
            report.append(f"- 平均CPU使用率: {resource.get('avg_cpu', 0):.1f}%")
            report.append(f"- 平均内存使用率: {resource.get('avg_memory', 0):.1f}%")
            report.append(f"- CPU峰值: {resource.get('peak_cpu', 0):.1f}%")
            report.append(f"- 内存峰值: {resource.get('peak_memory', 0):.1f}%")
        report.append("")

        # 操作性能
        report.append("## 操作性能")
        for operation, perf in summary.get("operation_performance", {}).items():
            report.append(f"### {operation}")
            report.append(f"- 平均耗时: {perf['avg_time']:.2f}秒")
            report.append(f"- 最短耗时: {perf['min_time']:.2f}秒")
            report.append(f"- 最长耗时: {perf['max_time']:.2f}秒")
            report.append(f"- 总操作次数: {perf['total_operations']}")
            report.append("")

        # 成功率统计
        report.append("## 成功率统计")
        for operation, rate in summary.get("success_rates", {}).items():
            report.append(f"### {operation}")
            report.append(f"- 成功率: {rate['success_rate']:.1f}%")
            report.append(f"- 总尝试次数: {rate['total_attempts']}")
            report.append(f"- 成功次数: {rate['successful_attempts']}")
            report.append("")

        # 优化建议
        if suggestions:
            report.append("## 优化建议")
            for i, suggestion in enumerate(suggestions, 1):
                report.append(f"### 建议 {i} ({suggestion['priority']}优先级)")
                report.append(f"- **类型**: {suggestion['type']}")
                report.append(f"- **操作**: {suggestion['operation']}")
                report.append(f"- **问题**: {suggestion['issue']}")
                report.append(f"- **建议**: {suggestion['suggestion']}")
                report.append("")
        else:
            report.append("## 优化建议")
            report.append("当前性能表现良好，暂无优化建议。")

        return "\n".join(report)

# 使用示例
monitor = PerformanceMonitor()
monitor.start_monitoring()

# 记录操作指标
monitor.record_operation_metric("登录操作", 3.5, True)
monitor.record_operation_metric("截图操作", 2.1, True)
monitor.record_operation_metric("导航操作", 1.8, False)

# 生成优化报告
optimizer = PerformanceOptimizer(monitor)
report = optimizer.generate_optimization_report()
print(report)

monitor.stop_monitoring()
```

### 8.5 性能配置参数

#### 8.5.1 关键性能参数
```yaml
# config.yaml - 性能相关配置
performance:
  # 浏览器性能配置
  browser:
    headless: true                    # 无头模式，提高性能
    slow_mo: 0                       # 操作延迟，0为最快
    viewport_width: 1280             # 视口宽度
    viewport_height: 720             # 视口高度

  # 超时配置 (毫秒)
  timeouts:
    page_timeout: 30000              # 页面加载超时
    navigation_timeout: 20000        # 导航超时
    element_timeout: 10000           # 元素等待超时
    operation_timeout: 15000         # 操作超时
    network_idle_timeout: 5000       # 网络空闲超时

  # 重试配置
  retry:
    max_retries: 3                   # 最大重试次数
    base_delay: 1.0                  # 基础延迟(秒)
    exponential_backoff: true        # 指数退避
    max_delay: 30.0                  # 最大延迟(秒)

  # 资源管理
  resources:
    max_memory_mb: 2048              # 最大内存使用(MB)
    cleanup_interval: 100            # 清理间隔(操作次数)
    gc_threshold: 50                 # 垃圾回收阈值

  # 监控配置
  monitoring:
    enable_monitoring: true          # 启用性能监控
    monitor_interval: 5              # 监控间隔(秒)
    alert_cpu_threshold: 80          # CPU告警阈值(%)
    alert_memory_threshold: 85       # 内存告警阈值(%)

  # 日志配置
  logging:
    log_level: "INFO"                # 日志级别
    log_rotation: "10 MB"            # 日志轮转大小
    log_retention: "7 days"          # 日志保留时间
    enable_debug_save: false         # 启用调试信息保存
```

#### 8.5.2 性能优化最佳实践
```python
# 性能优化最佳实践示例

class PerformanceBestPractices:
    """性能优化最佳实践"""

    @staticmethod
    def optimize_page_loading():
        """优化页面加载"""
        # 1. 阻止不必要的资源加载
        page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}",
                  lambda route: route.abort())

        # 2. 设置合适的等待策略
        page.wait_for_load_state("domcontentloaded")  # 而不是 "load"

        # 3. 使用网络空闲等待
        page.wait_for_load_state("networkidle", timeout=5000)

    @staticmethod
    def optimize_element_operations():
        """优化元素操作"""
        # 1. 使用高效的选择器
        # 好的做法
        element = page.query_selector("#specific-id")

        # 避免的做法
        # element = page.query_selector("div > div > div > input")

        # 2. 批量操作
        elements = page.query_selector_all(".batch-elements")
        for element in elements:
            element.click()  # 批量处理

        # 3. 使用等待条件
        page.wait_for_selector("#target-element", state="visible")

    @staticmethod
    def optimize_memory_usage():
        """优化内存使用"""
        # 1. 及时清理页面
        if page:
            page.close()

        # 2. 定期垃圾回收
        import gc
        gc.collect()

        # 3. 限制并发数量
        max_concurrent_pages = 3

        # 4. 使用上下文管理器
        with browser_manager.new_page() as page:
            # 页面操作
            pass  # 自动清理
```

---

## 9. 未来优化方向

### 9.1 短期优化建议 (1-3个月)

#### 9.1.1 用户体验优化 (高优先级)

**1. 图形化配置界面**
- **需求背景**：当前配置需要手动编辑YAML文件，对非技术用户不够友好
- **实施方案**：
  ```python
  # 基于tkinter或PyQt5的简单GUI配置工具
  class ConfigGUI:
      def __init__(self):
          self.root = tk.Tk()
          self.root.title("自考成绩查询系统配置")

      def create_config_form(self):
          # 基础配置选项
          # - 无头模式开关
          # - 超时时间设置
          # - 重试次数配置
          # - 日志级别选择
          pass
  ```
- **预期收益**：降低使用门槛，减少配置错误
- **实施难度**：低
- **预估工时**：3-5天

**2. 实时进度可视化**
- **需求背景**：当前进度显示较为简单，缺乏直观的可视化界面
- **实施方案**：
  - 添加进度条显示
  - 实时成功/失败统计图表
  - 当前处理学员信息显示
  - 预估完成时间显示
- **预期收益**：提升用户体验，便于监控处理进度
- **实施难度**：中
- **预估工时**：5-7天

#### 9.1.2 稳定性增强 (高优先级)

**1. 网络异常处理优化**
- **需求背景**：网络不稳定时容易导致处理失败
- **实施方案**：
  ```python
  class NetworkResilienceManager:
      def __init__(self):
          self.network_stats = {"timeouts": 0, "errors": 0}

      def check_network_quality(self) -> bool:
          # 网络质量检测
          # - ping测试
          # - 带宽检测
          # - 延迟测试
          pass

      def adaptive_timeout_adjustment(self):
          # 根据网络质量动态调整超时时间
          pass
  ```
- **预期收益**：提高网络环境适应性，减少因网络问题导致的失败
- **实施难度**：中
- **预估工时**：4-6天

**2. 断点续传功能**
- **需求背景**：大批量处理时中断后需要重新开始，效率低下
- **实施方案**：
  - 处理状态持久化存储
  - 自动检测未完成任务
  - 支持从中断点继续处理
- **预期收益**：提高大批量处理的可靠性
- **实施难度**：中
- **预估工时**：6-8天

#### 9.1.3 性能优化 (中优先级)

**1. 并发处理支持**
- **需求背景**：当前为串行处理，大批量时耗时较长
- **实施方案**：
  ```python
  import asyncio
  from concurrent.futures import ThreadPoolExecutor

  class ConcurrentProcessor:
      def __init__(self, max_workers: int = 3):
          self.max_workers = max_workers
          self.executor = ThreadPoolExecutor(max_workers=max_workers)

      async def process_students_concurrently(self, students: List[Dict]):
          # 控制并发数量，避免过载
          semaphore = asyncio.Semaphore(self.max_workers)

          async def process_with_semaphore(student):
              async with semaphore:
                  return await self.process_single_student(student)

          tasks = [process_with_semaphore(student) for student in students]
          return await asyncio.gather(*tasks, return_exceptions=True)
  ```
- **预期收益**：大幅提升处理效率，减少总耗时
- **实施难度**：高
- **预估工时**：10-15天

### 9.2 中期优化建议 (3-6个月)

#### 9.2.1 智能化增强 (高优先级)

**1. 机器学习验证码识别**
- **需求背景**：当前验证码识别准确率有提升空间
- **实施方案**：
  - 收集验证码样本数据
  - 训练专用的验证码识别模型
  - 集成TensorFlow或PyTorch
  - 支持模型在线更新
- **预期收益**：显著提升验证码识别准确率
- **实施难度**：高
- **预估工时**：20-30天

**2. 智能页面结构识别**
- **需求背景**：页面结构变化时需要手动更新选择器
- **实施方案**：
  ```python
  class IntelligentPageAnalyzer:
      def __init__(self):
          self.page_patterns = {}

      def analyze_page_structure(self, page: Page) -> Dict:
          # 使用机器学习分析页面结构
          # - 元素位置分析
          # - 文本内容分析
          # - 页面布局识别
          pass

      def auto_generate_selectors(self, page_type: str) -> List[str]:
          # 自动生成选择器
          pass
  ```
- **预期收益**：减少维护工作量，提高适应性
- **实施难度**：高
- **预估工时**：25-35天

#### 9.2.2 功能扩展 (中优先级)

**1. 多省份支持**
- **需求背景**：当前仅支持福建省，可扩展到其他省份
- **实施方案**：
  - 抽象化页面操作接口
  - 插件化省份配置
  - 统一的数据格式标准
- **预期收益**：扩大应用范围，增加用户群体
- **实施难度**：中
- **预估工时**：15-20天

**2. 数据分析功能**
- **需求背景**：用户希望对成绩数据进行统计分析
- **实施方案**：
  - 成绩数据结构化存储
  - 基础统计分析功能
  - 图表可视化展示
  - 导出分析报告
- **预期收益**：增加产品价值，提升用户粘性
- **实施难度**：中
- **预估工时**：12-18天

### 9.3 长期优化建议 (6-12个月)

#### 9.3.1 架构升级 (高优先级)

**1. 微服务架构改造**
- **需求背景**：当前单体架构在大规模使用时存在扩展性限制
- **实施方案**：
  ```python
  # 服务拆分示例
  services = {
      "auth_service": "用户认证服务",
      "browser_service": "浏览器管理服务",
      "captcha_service": "验证码识别服务",
      "data_service": "数据处理服务",
      "notification_service": "通知服务"
  }

  # 使用FastAPI构建微服务
  from fastapi import FastAPI

  app = FastAPI()

  @app.post("/api/v1/process_student")
  async def process_student_api(student_data: dict):
      # RESTful API接口
      pass
  ```
- **预期收益**：提高系统可扩展性和可维护性
- **实施难度**：高
- **预估工时**：40-60天

**2. 云原生部署支持**
- **需求背景**：支持云环境部署，提高可用性和扩展性
- **实施方案**：
  - Docker容器化
  - Kubernetes编排支持
  - 云存储集成
  - 负载均衡支持
- **预期收益**：支持大规模部署和高可用性
- **实施难度**：高
- **预估工时**：30-45天

#### 9.3.2 企业级功能 (中优先级)

**1. 用户权限管理**
- **需求背景**：企业使用时需要用户权限控制
- **实施方案**：
  - 基于角色的权限控制(RBAC)
  - 用户认证和授权
  - 操作审计日志
  - 数据访问控制
- **预期收益**：满足企业级安全要求
- **实施难度**：中
- **预估工时**：20-30天

**2. 集成API接口**
- **需求背景**：与其他系统集成的需求
- **实施方案**：
  - RESTful API设计
  - WebHook通知支持
  - 第三方系统集成
  - API文档和SDK
- **预期收益**：提高系统集成能力
- **实施难度**：中
- **预估工时**：15-25天

### 9.4 技术债务清理

#### 9.4.1 代码质量提升 (中优先级)

**1. 代码重构和优化**
- **重构目标**：
  - 提取公共组件和工具类
  - 优化复杂方法的逻辑结构
  - 统一异常处理机制
  - 改进代码注释和文档
- **实施计划**：
  ```python
  # 重构示例：提取公共元素操作类
  class ElementOperations:
      @staticmethod
      def safe_click(element: Locator, timeout: int = 5000) -> bool:
          """安全点击元素"""
          try:
              element.wait_for(state="visible", timeout=timeout)
              element.click()
              return True
          except Exception as e:
              log_error("元素点击", e)
              return False

      @staticmethod
      def safe_fill(element: Locator, text: str, timeout: int = 5000) -> bool:
          """安全填写元素"""
          try:
              element.wait_for(state="visible", timeout=timeout)
              element.clear()
              element.fill(text)
              return True
          except Exception as e:
              log_error("元素填写", e)
              return False
  ```
- **预期收益**：提高代码可维护性和可读性
- **实施难度**：中
- **预估工时**：10-15天

**2. 单元测试覆盖率提升**
- **当前状况**：缺乏系统性的单元测试
- **改进目标**：
  - 核心模块测试覆盖率达到80%以上
  - 集成测试覆盖主要业务流程
  - 自动化测试流水线
- **实施方案**：
  ```python
  import pytest
  from unittest.mock import Mock, patch

  class TestLoginManager:
      def test_login_success(self):
          # 测试登录成功场景
          pass

      def test_login_with_captcha(self):
          # 测试验证码登录场景
          pass

      def test_login_failure_handling(self):
          # 测试登录失败处理
          pass
  ```
- **预期收益**：提高代码质量和系统稳定性
- **实施难度**：中
- **预估工时**：15-20天

### 9.5 实施优先级矩阵

| 优化项目 | 优先级 | 实施难度 | 预期收益 | 建议实施顺序 |
|---------|--------|----------|----------|-------------|
| 图形化配置界面 | 高 | 低 | 高 | 1 |
| 网络异常处理优化 | 高 | 中 | 高 | 2 |
| 断点续传功能 | 高 | 中 | 高 | 3 |
| 实时进度可视化 | 高 | 中 | 中 | 4 |
| 机器学习验证码识别 | 高 | 高 | 高 | 5 |
| 并发处理支持 | 中 | 高 | 高 | 6 |
| 多省份支持 | 中 | 中 | 中 | 7 |
| 智能页面结构识别 | 高 | 高 | 高 | 8 |
| 代码重构和优化 | 中 | 中 | 中 | 9 |
| 数据分析功能 | 中 | 中 | 中 | 10 |

### 9.6 风险评估和缓解策略

#### 9.6.1 技术风险
- **风险1**：并发处理可能导致系统不稳定
  - **缓解策略**：分阶段实施，充分测试，设置合理的并发限制
- **风险2**：机器学习模型训练数据不足
  - **缓解策略**：逐步收集数据，先实施传统算法优化
- **风险3**：微服务架构改造复杂度高
  - **缓解策略**：采用渐进式改造，保持向后兼容

#### 9.6.2 业务风险
- **风险1**：页面结构变化导致功能失效
  - **缓解策略**：增强智能识别能力，建立快速响应机制
- **风险2**：用户需求变化快
  - **缓解策略**：采用敏捷开发，快速迭代，及时响应用户反馈

### 9.7 成功指标定义

#### 9.7.1 技术指标
- **性能指标**：
  - 平均处理时间减少30%以上
  - 系统稳定性提升至99.5%以上
  - 验证码识别准确率提升至95%以上
- **质量指标**：
  - 代码测试覆盖率达到80%以上
  - 代码复杂度降低20%以上
  - 文档完整性达到90%以上

#### 9.7.2 用户体验指标
- **易用性指标**：
  - 配置时间减少50%以上
  - 用户操作步骤减少30%以上
  - 错误率降低40%以上
- **满意度指标**：
  - 用户满意度达到4.5分以上(5分制)
  - 用户推荐率达到80%以上
  - 用户留存率达到90%以上

---

## 📝 总结

### 技术文档总结

本技术文档全面介绍了自考成绩查询自动化系统的技术架构、核心实现、创新特性和优化方向。通过9个章节的详细阐述，为技术人员提供了完整的系统理解和实施指导。

#### 核心技术成就
1. **创新的密码修改检测系统**：三层验证机制 + 6种智能查找策略，大幅提升检测准确率
2. **完善的容错保护机制**：多重超时保护、智能重试、自动跳过，确保系统稳定运行
3. **智能化的元素定位**：多策略组合，适应各种页面结构变化
4. **全方位的性能优化**：从浏览器管理到资源控制的完整优化体系
5. **完善的可观测性**：详细的日志系统、调试信息保存、性能监控

#### 技术价值体现
- **稳定性**：通过多层级的错误处理和恢复机制，确保系统在各种异常情况下的稳定运行
- **智能化**：通过智能元素查找、验证码识别等技术，大幅减少人工干预需求
- **可维护性**：模块化架构、详细日志、完善文档，降低系统维护成本
- **可扩展性**：灵活的配置系统、插件化设计，支持功能扩展和定制
- **高性能**：优化的资源管理、智能重试机制，提升处理效率

#### 应用价值
- **效率提升**：自动化处理大幅提高成绩查询效率，减少重复性人工操作
- **准确性保障**：智能验证和多重检查机制，确保数据准确性
- **成本节约**：减少人力投入，降低运营成本
- **用户体验**：简化操作流程，提升用户满意度

### 未来发展方向

系统已建立了坚实的技术基础，未来将朝着更加智能化、自动化、企业级的方向发展：

1. **智能化升级**：机器学习验证码识别、智能页面结构识别
2. **架构演进**：微服务架构、云原生部署
3. **功能扩展**：多省份支持、数据分析功能
4. **企业级特性**：用户权限管理、API集成

---

## 📚 附录

### A. 常用配置参考

#### A.1 基础配置模板
```yaml
# config.yaml - 基础配置模板
# 浏览器配置
headless: true                    # 无头模式
slow_mo: 0                       # 操作延迟(毫秒)
viewport_width: 1280             # 视口宽度
viewport_height: 720             # 视口高度

# 超时配置(毫秒)
page_timeout: 30000              # 页面加载超时
navigation_timeout: 20000        # 导航超时
element_timeout: 10000           # 元素等待超时

# 重试配置
max_retries: 3                   # 最大重试次数
retry_delay: 2                   # 重试延迟(秒)

# 日志配置
log_level: "INFO"                # 日志级别
log_dir: "logs"                  # 日志目录
log_rotation: "10 MB"            # 日志轮转大小
log_retention: "7 days"          # 日志保留时间

# 截图配置
screenshot_dir: "screenshots"     # 截图目录
screenshot_quality: 90           # 截图质量(1-100)

# 调试配置
debug_mode: false                # 调试模式
debug_dir: "debug"               # 调试信息目录
save_debug_info: false           # 保存调试信息
```

#### A.2 选择器配置模板
```yaml
# 选择器配置模板
selectors:
  # 登录相关
  username_input:
    - "#username"
    - "input[name='username']"
    - "input[placeholder*='用户名']"

  password_input:
    - "#password"
    - "input[name='password']"
    - "input[type='password']"

  login_button:
    - "#login_btn"
    - "input[type='submit']"
    - "button[type='submit']"

  # 验证码相关
  captcha_image:
    - "#captcha_img"
    - ".captcha-image"
    - "img[src*='captcha']"

  captcha_input:
    - "#captcha"
    - "input[name='captcha']"
    - ".captcha-input"

  # 密码修改相关
  new_password_selectors:
    - "#newPassword"
    - "#mypwd"
    - "input[name='newPwd']"

  confirm_password_selectors:
    - "#confirmPassword"
    - "#mypwd2"
    - "input[name='confirmPwd']"

  # 导航相关
  score_query_links:
    - "a:contains('成绩查询')"
    - "a:contains('当次成绩')"
    - ".menu-item:contains('查询')"

  # 截图相关
  score_table:
    - "table.score-table"
    - ".result-table"
    - "table:has(th:contains('科目'))"
```

### B. 故障排除指南

#### B.1 常见问题及解决方案

**问题1：浏览器启动失败**
- **症状**：程序启动时报错"浏览器初始化失败"
- **可能原因**：
  - Playwright浏览器未安装
  - 系统权限不足
  - 端口被占用
- **解决方案**：
  ```bash
  # 安装Playwright浏览器
  playwright install chromium

  # 检查系统权限
  # Windows: 以管理员身份运行
  # Linux: 检查用户权限和依赖

  # 检查端口占用
  netstat -an | grep :9222
  ```

**问题2：验证码识别失败**
- **症状**：验证码识别准确率低或完全失败
- **可能原因**：
  - ddddocr库版本不兼容
  - 验证码图像质量差
  - PIL版本冲突
- **解决方案**：
  ```bash
  # 更新ddddocr
  pip install --upgrade ddddocr

  # 检查PIL版本
  pip install --upgrade Pillow

  # 启用调试模式查看验证码图像
  debug_mode: true
  save_debug_info: true
  ```

**问题3：元素定位失败**
- **症状**：页面元素无法找到或点击失败
- **可能原因**：
  - 页面结构变化
  - 选择器配置过时
  - 页面加载不完整
- **解决方案**：
  - 更新选择器配置
  - 增加页面等待时间
  - 启用调试模式检查页面结构

#### B.2 日志分析指南

**日志级别说明**：
- **DEBUG**：详细的调试信息，包含所有操作细节
- **INFO**：一般信息，包含处理进度和状态
- **WARNING**：警告信息，可能影响处理但不会中断
- **ERROR**：错误信息，导致操作失败的问题
- **SUCCESS**：成功信息，操作成功完成的确认

**关键日志模式**：
```
# 成功模式
[SUCCESS] 浏览器初始化成功
[SUCCESS] 登录成功: 张三
[SUCCESS] 截图保存成功: screenshots/张三_成绩单.png

# 警告模式
[WARNING] 验证码识别失败，尝试重试
[WARNING] 页面加载较慢，等待中...

# 错误模式
[ERROR] 登录失败: 用户名输入失败
[ERROR] 截图失败: 表格元素未找到
```

### C. 性能调优建议

#### C.1 系统资源优化
```python
# 内存优化配置
performance:
  browser:
    args:
      - '--memory-pressure-off'
      - '--max_old_space_size=4096'
      - '--disable-dev-shm-usage'

  resources:
    max_memory_mb: 2048
    cleanup_interval: 50
    gc_threshold: 30
```

#### C.2 网络优化配置
```python
# 网络优化配置
network:
  timeout_multiplier: 1.5        # 网络较慢时增加倍数
  retry_on_network_error: true   # 网络错误时自动重试
  max_network_retries: 5         # 最大网络重试次数
```

### D. 开发环境搭建

#### D.1 环境要求
- **Python**: 3.8+ (推荐 3.9-3.11)
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **内存**: 最低 4GB，推荐 8GB+
- **磁盘空间**: 最低 2GB 可用空间

#### D.2 安装步骤
```bash
# 1. 克隆项目
git clone <project-url>
cd exam-score-query

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 安装Playwright浏览器
playwright install chromium

# 5. 配置系统
cp config.yaml.example config.yaml
# 编辑 config.yaml 配置文件

# 6. 运行测试
python -m pytest tests/

# 7. 启动系统
python src/main.py
```

---

## 📞 技术支持

### 联系方式
- **技术文档**: 本文档
- **项目地址**: [项目仓库地址]
- **问题反馈**: [Issues页面]
- **技术讨论**: [讨论区地址]

### 贡献指南
欢迎提交问题报告、功能建议和代码贡献。请遵循项目的贡献指南和代码规范。

---

**文档结束**

> 本文档将根据系统更新持续维护，请关注版本更新信息。
> 最后更新时间：2025年1月31日
