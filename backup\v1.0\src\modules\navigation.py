#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导航模块
实现页面导航和成绩查询选项处理
"""

import time
from typing import List, Optional, Dict
from playwright.sync_api import Page, Locator

import sys
sys.path.append('..')
from config import CONFIG, SELECTORS
from modules.logger import log_info, log_success, log_error, log_warning


class NavigationManager:
    """导航管理器"""
    
    def __init__(self, page: Page):
        """
        初始化导航管理器
        
        Args:
            page: 页面对象
        """
        self.page = page
    
    def navigate_to_score_query(self) -> bool:
        """
        导航到成绩查询页面
        
        Returns:
            是否成功
        """
        log_info("开始导航到成绩查询页面", "导航")
        
        try:
            # 等待页面稳定
            self.page.wait_for_load_state("networkidle")
            time.sleep(2)
            
            # 查找"当次成绩查询"菜单
            menu_element = self._find_score_query_menu()
            
            if not menu_element:
                log_error("导航", Exception("未找到'当次成绩查询'菜单"))
                return False
            
            # 点击菜单
            log_info("点击'当次成绩查询'菜单", "导航")
            menu_element.click()
            
            # 等待页面跳转
            self.page.wait_for_load_state("networkidle")
            time.sleep(3)
            
            # 验证是否到达成绩查询页面
            if self._verify_score_query_page():
                log_success("成功导航到成绩查询页面", "导航")
                return True
            else:
                log_warning("页面跳转后验证失败", "导航")
                return False
                
        except Exception as e:
            log_error("导航", e)
            return False
    
    def _find_score_query_menu(self) -> Optional[Locator]:
        """
        查找"当次成绩查询"菜单
        
        Returns:
            菜单元素
        """
        log_info("查找'当次成绩查询'菜单", "导航")
        
        for selector in SELECTORS["score_query_menu"]:
            try:
                elements = self.page.locator(selector)
                element_count = elements.count()
                
                if element_count > 0:
                    log_success(f"找到'当次成绩查询'菜单: {selector} (数量: {element_count})", "导航")
                    return elements.first
                    
            except Exception as e:
                log_warning(f"选择器 {selector} 查找失败: {e}", "导航")
                continue
        
        log_warning("未找到'当次成绩查询'菜单", "导航")
        return None
    
    def _verify_score_query_page(self) -> bool:
        """
        验证是否到达成绩查询页面
        
        Returns:
            是否成功
        """
        try:
            current_url = self.page.url
            page_title = self.page.title()
            page_content = self.page.content()
            
            log_info(f"当前URL: {current_url}", "导航")
            log_info(f"页面标题: {page_title}", "导航")
            
            # 检查页面标识
            indicators = [
                "点击进入", "查询", "成绩", "科目", "分数",
                "query", "grade", "score", "subject"
            ]
            
            for indicator in indicators:
                if (indicator in page_title.lower() or 
                    indicator in page_content.lower()):
                    log_success(f"页面验证成功，包含标识: {indicator}", "导航")
                    return True
            
            # 检查是否有查询选项
            query_options = self.detect_query_options()
            if query_options:
                log_success(f"页面验证成功，检测到 {len(query_options)} 个查询选项", "导航")
                return True
            
            log_warning("页面验证失败，未找到成绩查询相关内容", "导航")
            return False
            
        except Exception as e:
            log_error("导航", e)
            return False
    
    def detect_query_options(self) -> List[Dict]:
        """
        检测查询选项
        
        Returns:
            查询选项列表
        """
        log_info("检测查询选项", "导航")
        
        options = []
        seen_positions = set()
        
        for selector in SELECTORS["query_options"]:
            try:
                elements = self.page.locator(selector)
                element_count = elements.count()
                
                if element_count > 0:
                    log_info(f"选择器 {selector} 找到 {element_count} 个选项", "导航")
                    
                    for i in range(element_count):
                        try:
                            element = elements.nth(i)
                            
                            # 获取元素位置用于去重
                            bbox = element.bounding_box()
                            if bbox:
                                position = (int(bbox["x"]), int(bbox["y"]))
                                if position in seen_positions:
                                    continue
                                seen_positions.add(position)
                            
                            # 获取元素信息
                            text = element.text_content() or ""
                            value = element.get_attribute("value") or ""
                            
                            option_info = {
                                "element": element,
                                "text": text.strip(),
                                "value": value.strip(),
                                "selector": selector,
                                "index": i
                            }
                            
                            options.append(option_info)
                            log_info(f"检测到选项: {text} (值: {value})", "导航")
                            
                        except Exception as e:
                            log_warning(f"处理选项 {i} 失败: {e}", "导航")
                            continue
                            
            except Exception as e:
                log_warning(f"选择器 {selector} 检测失败: {e}", "导航")
                continue
        
        log_info(f"总共检测到 {len(options)} 个查询选项", "导航")
        return options
    
    def smart_select_option(self, options: List[Dict]) -> Optional[Dict]:
        """
        智能选择查询选项
        
        Args:
            options: 查询选项列表
            
        Returns:
            选中的选项
        """
        if not options:
            log_warning("没有可选择的查询选项", "导航")
            return None
        
        option_count = len(options)
        log_info(f"开始智能选择查询选项，共 {option_count} 个选项", "导航")
        
        # 根据操作步骤要求：多个选第二个，单个选第一个
        if option_count == 1:
            selected_option = options[0]
            selection_reason = "单个选项，选择第一个"
        else:
            # 多个选项，选择第二个
            selected_option = options[1] if option_count > 1 else options[0]
            selection_reason = f"多个选项({option_count}个)，选择第二个"
        
        log_success(f"选择策略: {selection_reason}", "导航")
        log_success(f"选中选项: {selected_option['text']}", "导航")
        
        return selected_option
    
    def click_selected_option(self, selected_option: Dict) -> bool:
        """
        点击选中的选项
        
        Args:
            selected_option: 选中的选项
            
        Returns:
            是否成功
        """
        try:
            log_info(f"点击选中的选项: {selected_option['text']}", "导航")
            
            element = selected_option["element"]
            
            # 滚动到元素位置
            element.scroll_into_view_if_needed()
            time.sleep(1)
            
            # 点击元素
            element.click()
            
            # 等待页面响应
            self.page.wait_for_load_state("networkidle")
            time.sleep(3)
            
            log_success("选项点击成功", "导航")
            return True
            
        except Exception as e:
            log_error("导航", e)
            return False
    
    def complete_navigation_flow(self) -> bool:
        """
        完成完整的导航流程
        
        Returns:
            是否成功
        """
        log_info("开始完整导航流程", "导航")
        
        try:
            # 步骤1: 导航到成绩查询页面
            if not self.navigate_to_score_query():
                return False
            
            # 步骤2: 检测查询选项
            options = self.detect_query_options()
            if not options:
                log_warning("未检测到查询选项", "导航")
                return False
            
            # 步骤3: 智能选择选项
            selected_option = self.smart_select_option(options)
            if not selected_option:
                return False
            
            # 步骤4: 点击选中的选项
            if not self.click_selected_option(selected_option):
                return False
            
            # 步骤5: 验证最终页面
            if self._verify_final_page():
                log_success("完整导航流程成功", "导航")
                return True
            else:
                log_warning("最终页面验证失败", "导航")
                return False
                
        except Exception as e:
            log_error("导航", e)
            return False
    
    def _verify_final_page(self) -> bool:
        """
        验证最终页面（成绩显示页面）
        
        Returns:
            是否成功
        """
        try:
            current_url = self.page.url
            page_title = self.page.title()
            page_content = self.page.content()
            
            log_info(f"最终页面URL: {current_url}", "导航")
            log_info(f"最终页面标题: {page_title}", "导航")
            
            # 检查成绩页面标识
            final_indicators = [
                "成绩", "科目", "分数", "合格", "不合格",
                "grade", "score", "subject", "pass", "fail",
                "table"  # 通常成绩以表格形式显示
            ]
            
            for indicator in final_indicators:
                if (indicator in page_title.lower() or 
                    indicator in page_content.lower()):
                    log_success(f"最终页面验证成功，包含标识: {indicator}", "导航")
                    return True
            
            # 检查是否有表格
            try:
                tables = self.page.locator("table")
                if tables.count() > 0:
                    log_success("最终页面验证成功，检测到表格", "导航")
                    return True
            except:
                pass
            
            log_warning("最终页面验证失败", "导航")
            return False
            
        except Exception as e:
            log_error("导航", e)
            return False
    
    def get_current_page_info(self) -> Dict:
        """
        获取当前页面信息
        
        Returns:
            页面信息
        """
        try:
            return {
                "url": self.page.url,
                "title": self.page.title(),
                "ready": True
            }
        except Exception as e:
            log_error("导航", e)
            return {
                "url": "",
                "title": "",
                "ready": False,
                "error": str(e)
            }


# 便捷函数
def navigate_after_login(page: Page) -> bool:
    """
    登录后导航的便捷函数
    
    Args:
        page: 页面对象
        
    Returns:
        是否成功
    """
    navigation_manager = NavigationManager(page)
    return navigation_manager.complete_navigation_flow()
