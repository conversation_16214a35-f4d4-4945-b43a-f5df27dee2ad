#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速密码修改功能测试脚本
专门用于验证条件性密码修改功能的快速测试
"""

import sys
import time
from pathlib import Path
from typing import Dict, Optional, Tuple

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent / "src"))

from modules.data import load_students_data
from modules.browser import BrowserManager
from modules.login import LoginManager
from modules.logger import get_logger_manager, log_info, log_success, log_error, log_warning
from config import CONFIG


class QuickPasswordTest:
    """快速密码修改功能测试"""
    
    def __init__(self):
        """初始化测试"""
        self.logger_manager = get_logger_manager()
        log_info("快速密码修改功能测试初始化", "快速测试")
    
    def test_password_change_detection(self, test_count: int = 3) -> bool:
        """
        测试密码修改检测功能
        
        Args:
            test_count: 测试学员数量
            
        Returns:
            测试是否成功
        """
        print("🔍 快速密码修改功能测试")
        print("=" * 40)
        
        # 加载测试数据
        try:
            students = load_students_data("data/students.csv")
            if not students:
                print("❌ 无法加载测试数据")
                return False
            
            # 限制测试数量
            test_students = students[:test_count]
            print(f"📋 将测试前 {len(test_students)} 名学员的密码修改功能")
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
        
        # 初始化浏览器
        browser_manager = BrowserManager()
        if not browser_manager.init_browser():
            print("❌ 浏览器初始化失败")
            return False
        
        print("✅ 浏览器初始化成功")
        
        try:
            results = {
                "total": len(test_students),
                "success": 0,
                "password_change_detected": 0,
                "password_change_success": 0,
                "failed": 0
            }
            
            for i, student in enumerate(test_students, 1):
                student_name = student["姓名"]
                username = student["身份证号"]
                password = student["密码"]
                
                print(f"\n🧪 测试 {i}/{len(test_students)}: {student_name}")
                print(f"   身份证号: {username}")
                
                # 执行登录测试
                success, result_info = self._test_single_login(
                    browser_manager, username, password, student_name
                )
                
                if success:
                    results["success"] += 1
                    print(f"   ✅ 登录成功")
                    
                    # 检查密码修改情况
                    if "密码修改检测" in result_info:
                        results["password_change_detected"] += 1
                        print(f"   🔑 检测到密码修改页面")
                        
                        if "密码修改成功" in result_info:
                            results["password_change_success"] += 1
                            print(f"   ✅ 密码修改处理成功")
                        else:
                            print(f"   ⚠️ 密码修改处理失败")
                    else:
                        print(f"   ℹ️ 无需密码修改")
                else:
                    results["failed"] += 1
                    print(f"   ❌ 登录失败: {result_info}")
                
                # 测试间隔
                if i < len(test_students):
                    print("   ⏳ 等待2秒...")
                    time.sleep(2)
            
            # 显示测试结果
            self._display_quick_results(results)
            
            return results["success"] > 0
            
        finally:
            browser_manager.close()
            print("\n🌐 浏览器已关闭")
    
    def _test_single_login(self, browser_manager: BrowserManager, username: str, password: str, student_name: str) -> Tuple[bool, str]:
        """
        测试单个学员的登录（包含密码修改检测）
        
        Args:
            browser_manager: 浏览器管理器
            username: 用户名
            password: 密码
            student_name: 学员姓名
            
        Returns:
            (是否成功, 结果信息)
        """
        try:
            log_info(f"测试学员 {student_name} 的登录和密码修改功能", "快速测试")
            
            # 使用LoginManager进行登录（已集成密码修改功能）
            login_manager = LoginManager(browser_manager)
            success, error_msg, page = login_manager.perform_login(username, password, max_retries=2)
            
            if not success:
                return False, error_msg
            
            # 分析登录结果
            result_info = "登录成功"
            
            # 检查是否包含密码修改信息
            if "密码修改" in error_msg:
                result_info += " - 密码修改检测"
                if "成功" in error_msg:
                    result_info += " - 密码修改成功"
                else:
                    result_info += " - 密码修改失败"
            
            # 清理页面资源
            if page:
                browser_manager.close_page(page)
            
            return True, result_info
            
        except Exception as e:
            error_msg = f"测试异常: {str(e)}"
            log_error("快速测试", e)
            return False, error_msg
    
    def _display_quick_results(self, results: Dict):
        """显示快速测试结果"""
        print("\n" + "=" * 40)
        print("📊 快速测试结果")
        print("=" * 40)
        print(f"测试总数: {results['total']} 名学员")
        print(f"成功登录: {results['success']} 名")
        print(f"登录失败: {results['failed']} 名")
        
        if results['success'] > 0:
            success_rate = (results['success'] / results['total']) * 100
            print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n🔑 密码修改功能:")
        print(f"检测到密码修改: {results['password_change_detected']} 名学员")
        print(f"密码修改成功: {results['password_change_success']} 名学员")
        
        if results['password_change_detected'] > 0:
            change_success_rate = (results['password_change_success'] / results['password_change_detected']) * 100
            print(f"密码修改成功率: {change_success_rate:.1f}%")
        
        print("=" * 40)
    
    def run_interactive_test(self):
        """运行交互式测试"""
        print("🎯 快速密码修改功能测试")
        print("=" * 40)
        print("此测试将验证以下功能：")
        print("1. 自动登录功能")
        print("2. 密码修改页面检测")
        print("3. 自动密码修改处理")
        print("4. 条件性跳过逻辑")
        
        # 选择测试数量
        while True:
            try:
                test_count = input("\n请输入要测试的学员数量 (1-10, 默认3): ").strip()
                if not test_count:
                    test_count = 3
                else:
                    test_count = int(test_count)
                
                if 1 <= test_count <= 10:
                    break
                else:
                    print("❌ 请输入1-10之间的数字")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        # 确认开始测试
        confirm = input(f"\n即将测试前 {test_count} 名学员，是否继续？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 用户取消操作")
            return False
        
        # 执行测试
        return self.test_password_change_detection(test_count)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="快速密码修改功能测试")
    parser.add_argument("--count", "-c", type=int, default=3, help="测试学员数量 (1-10)")
    parser.add_argument("--headless", action="store_true", help="使用无头模式")
    parser.add_argument("--interactive", "-i", action="store_true", help="交互式模式")
    
    args = parser.parse_args()
    
    # 更新配置
    if args.headless:
        CONFIG["headless"] = True
    
    # 验证测试数量
    if not 1 <= args.count <= 10:
        print("❌ 测试数量必须在1-10之间")
        sys.exit(1)
    
    # 创建测试实例
    test = QuickPasswordTest()
    
    try:
        if args.interactive:
            success = test.run_interactive_test()
        else:
            success = test.test_password_change_detection(args.count)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 系统异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
