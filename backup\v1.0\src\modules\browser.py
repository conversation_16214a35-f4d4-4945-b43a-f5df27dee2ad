#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器管理模块
基于Playwright实现浏览器自动化操作
"""

import time
from typing import Optional, Tuple
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, BrowserContex<PERSON>, Page

from config import CONFIG
from modules.logger import log_info, log_success, log_error, log_warning


class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self):
        """初始化浏览器管理器"""
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
    
    def init_browser(self) -> bool:
        """
        初始化浏览器
        
        Returns:
            是否成功
        """
        try:
            log_info("开始初始化浏览器", "浏览器")
            
            # 启动Playwright
            self.playwright = sync_playwright().start()
            
            # 启动浏览器
            self.browser = self.playwright.chromium.launch(
                headless=CONFIG["headless"],
                slow_mo=CONFIG["slow_mo"],
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # 创建浏览器上下文
            self.context = self.browser.new_context(
                viewport={
                    'width': CONFIG["viewport_width"],
                    'height': CONFIG["viewport_height"]
                },
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            # 设置超时
            self.context.set_default_timeout(CONFIG["page_timeout"])
            self.context.set_default_navigation_timeout(CONFIG["navigation_timeout"])
            
            # 创建页面
            self.page = self.context.new_page()
            
            # 设置页面事件监听
            self._setup_page_listeners()
            
            log_success("浏览器初始化成功", "浏览器")
            return True
            
        except Exception as e:
            log_error("浏览器", e)
            self.close()
            return False
    
    def _setup_page_listeners(self, page: Optional[Page] = None):
        """设置页面事件监听"""
        target_page = page or self.page
        if not target_page:
            return

        # 监听页面错误
        def on_page_error(error):
            log_warning(f"页面错误: {error}", "浏览器")

        # 监听控制台消息
        def on_console(msg):
            if msg.type == 'error':
                log_warning(f"控制台错误: {msg.text}", "浏览器")

        # 监听请求失败
        def on_request_failed(request):
            log_warning(f"请求失败: {request.url}", "浏览器")

        target_page.on('pageerror', on_page_error)
        target_page.on('console', on_console)
        target_page.on('requestfailed', on_request_failed)
    
    def navigate_to_login(self) -> bool:
        """
        导航到登录页面
        
        Returns:
            是否成功
        """
        try:
            log_info(f"导航到登录页面: {CONFIG['login_url']}", "浏览器")
            
            if not self.page:
                log_error("浏览器", Exception("页面对象未初始化"))
                return False
            
            # 导航到登录页面
            response = self.page.goto(CONFIG["login_url"])
            
            if response and response.status == 200:
                log_success("登录页面加载成功", "浏览器")
                
                # 等待页面稳定
                self.page.wait_for_load_state("networkidle")
                time.sleep(2)
                
                return True
            else:
                log_warning(f"登录页面响应异常: {response.status if response else 'None'}", "浏览器")
                return False
                
        except Exception as e:
            log_error("浏览器", e)
            return False
    
    def wait_for_element(self, selector: str, timeout: Optional[int] = None) -> bool:
        """
        等待元素出现
        
        Args:
            selector: 元素选择器
            timeout: 超时时间(毫秒)
            
        Returns:
            是否找到元素
        """
        try:
            if not self.page:
                return False
            
            timeout = timeout or CONFIG["element_timeout"]
            
            self.page.wait_for_selector(selector, timeout=timeout)
            return True
            
        except Exception as e:
            log_warning(f"等待元素超时: {selector}", "浏览器")
            return False
    
    def click_element(self, selector: str, timeout: Optional[int] = None) -> bool:
        """
        点击元素
        
        Args:
            selector: 元素选择器
            timeout: 超时时间(毫秒)
            
        Returns:
            是否成功
        """
        try:
            if not self.page:
                return False
            
            timeout = timeout or CONFIG["element_timeout"]
            
            element = self.page.locator(selector)
            element.click(timeout=timeout)
            
            return True
            
        except Exception as e:
            log_warning(f"点击元素失败: {selector} - {e}", "浏览器")
            return False
    
    def fill_input(self, selector: str, value: str, timeout: Optional[int] = None) -> bool:
        """
        填充输入框
        
        Args:
            selector: 元素选择器
            value: 输入值
            timeout: 超时时间(毫秒)
            
        Returns:
            是否成功
        """
        try:
            if not self.page:
                return False
            
            timeout = timeout or CONFIG["element_timeout"]
            
            element = self.page.locator(selector)
            element.clear(timeout=timeout)
            element.fill(value, timeout=timeout)
            
            return True
            
        except Exception as e:
            log_warning(f"填充输入框失败: {selector} - {e}", "浏览器")
            return False
    
    def get_page_title(self) -> str:
        """
        获取页面标题
        
        Returns:
            页面标题
        """
        try:
            if not self.page:
                return ""
            
            return self.page.title()
            
        except Exception as e:
            log_warning(f"获取页面标题失败: {e}", "浏览器")
            return ""
    
    def get_current_url(self) -> str:
        """
        获取当前URL
        
        Returns:
            当前URL
        """
        try:
            if not self.page:
                return ""
            
            return self.page.url
            
        except Exception as e:
            log_warning(f"获取当前URL失败: {e}", "浏览器")
            return ""
    
    def take_screenshot(self, path: str, full_page: bool = True) -> bool:
        """
        截取页面截图
        
        Args:
            path: 保存路径
            full_page: 是否全页面截图
            
        Returns:
            是否成功
        """
        try:
            if not self.page:
                return False
            
            self.page.screenshot(path=path, full_page=full_page)
            log_success(f"页面截图已保存: {path}", "浏览器")
            return True
            
        except Exception as e:
            log_error("浏览器", e)
            return False
    
    def reload_page(self) -> bool:
        """
        重新加载页面
        
        Returns:
            是否成功
        """
        try:
            if not self.page:
                return False
            
            log_info("重新加载页面", "浏览器")
            self.page.reload()
            self.page.wait_for_load_state("networkidle")
            
            return True
            
        except Exception as e:
            log_error("浏览器", e)
            return False
    
    def new_page(self) -> Optional[Page]:
        """
        创建新页面

        Returns:
            新创建的页面对象，如果失败返回None
        """
        try:
            if not self.context:
                log_warning("浏览器上下文未初始化，无法创建新页面", "浏览器")
                return None

            # 创建新页面
            page = self.context.new_page()

            # 设置页面事件监听
            self._setup_page_listeners(page)

            log_info("成功创建新页面", "浏览器")
            return page

        except Exception as e:
            log_error("浏览器", e)
            return None

    def close_page(self, page: Page) -> bool:
        """
        关闭指定页面

        Args:
            page: 要关闭的页面对象

        Returns:
            是否成功关闭
        """
        try:
            if page:
                page.close()
                log_info("页面已关闭", "浏览器")
                return True
            return False

        except Exception as e:
            log_warning(f"页面关闭异常: {e}", "浏览器")
            return False

    def close_context_only(self) -> bool:
        """
        只关闭浏览器上下文，保留浏览器实例

        Returns:
            是否成功
        """
        try:
            if self.page:
                self.page.close()
                self.page = None

            if self.context:
                self.context.close()
                self.context = None
                log_info("浏览器上下文已关闭", "浏览器")
                return True

            return False

        except Exception as e:
            log_warning(f"上下文关闭异常: {e}", "浏览器")
            return False

    def create_new_context(self) -> bool:
        """
        创建新的浏览器上下文

        Returns:
            是否成功
        """
        try:
            if not self.browser:
                log_warning("浏览器实例未初始化", "浏览器")
                return False

            # 先关闭现有上下文
            if self.context:
                self.close_context_only()

            # 创建新的浏览器上下文
            self.context = self.browser.new_context(
                viewport={
                    'width': CONFIG["viewport_width"],
                    'height': CONFIG["viewport_height"]
                },
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )

            # 设置超时
            self.context.set_default_timeout(CONFIG["page_timeout"])
            self.context.set_default_navigation_timeout(CONFIG["navigation_timeout"])

            log_info("新浏览器上下文创建成功", "浏览器")
            return True

        except Exception as e:
            log_error("浏览器", e)
            return False

    def is_browser_alive(self) -> bool:
        """
        检查浏览器实例是否存活

        Returns:
            浏览器是否存活
        """
        try:
            if not self.browser:
                return False

            # 尝试检查浏览器是否连接
            return self.browser.is_connected()

        except Exception:
            return False

    def recover_from_page_error(self) -> bool:
        """
        从页面错误中恢复

        Returns:
            是否恢复成功
        """
        try:
            log_info("尝试从页面错误中恢复", "浏览器")

            # 检查浏览器是否存活
            if not self.is_browser_alive():
                log_warning("浏览器实例已失效，无法恢复", "浏览器")
                return False

            # 重新创建上下文
            if self.create_new_context():
                log_success("页面错误恢复成功", "浏览器")
                return True
            else:
                log_warning("页面错误恢复失败", "浏览器")
                return False

        except Exception as e:
            log_error("浏览器", e)
            return False

    def perform_health_check(self) -> bool:
        """
        执行浏览器健康检查

        Returns:
            浏览器是否健康
        """
        try:
            # 检查基本组件
            if not self.playwright or not self.browser:
                log_warning("浏览器基本组件缺失", "浏览器")
                return False

            # 检查浏览器连接
            if not self.is_browser_alive():
                log_warning("浏览器连接已断开", "浏览器")
                return False

            # 检查上下文状态
            if not self.is_context_ready():
                log_info("浏览器上下文未就绪，尝试重新创建", "浏览器")
                return self.create_new_context()

            log_info("浏览器健康检查通过", "浏览器")
            return True

        except Exception as e:
            log_warning(f"浏览器健康检查异常: {e}", "浏览器")
            return False

    def safe_close_page(self, page: Page) -> bool:
        """
        安全关闭页面（带异常处理）

        Args:
            page: 要关闭的页面对象

        Returns:
            是否成功关闭
        """
        try:
            if page and not page.is_closed():
                page.close()
                log_info("页面安全关闭成功", "浏览器")
                return True
            return True

        except Exception as e:
            log_warning(f"页面关闭异常: {e}", "浏览器")
            # 即使关闭失败，也返回True，因为页面可能已经被关闭
            return True

    def emergency_recovery(self) -> bool:
        """
        紧急恢复机制（重新初始化浏览器）

        注意：由于asyncio循环冲突问题，紧急恢复在当前进程中可能失败
        建议在批量处理中避免使用此方法，而是重新启动整个程序

        Returns:
            是否恢复成功
        """
        try:
            log_warning("执行紧急恢复，重新初始化浏览器", "浏览器")
            log_warning("注意：紧急恢复可能因asyncio冲突而失败", "浏览器")

            # 强制关闭现有资源
            try:
                if self.page:
                    self.page.close()
                if self.context:
                    self.context.close()
                if self.browser:
                    self.browser.close()
                if self.playwright:
                    self.playwright.stop()
            except:
                pass  # 忽略关闭过程中的异常

            # 重置所有状态
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None

            # 尝试重新初始化（可能因asyncio冲突而失败）
            try:
                if self.init_browser():
                    log_success("紧急恢复成功", "浏览器")
                    return True
                else:
                    log_warning("紧急恢复失败：浏览器初始化失败", "浏览器")
                    return False
            except Exception as init_error:
                if "asyncio loop" in str(init_error):
                    log_warning("紧急恢复失败：asyncio循环冲突，建议重启程序", "浏览器")
                else:
                    log_error("浏览器", init_error)
                return False

        except Exception as e:
            log_error("浏览器", e)
            return False

    def close(self):
        """关闭浏览器"""
        try:
            log_info("开始关闭浏览器", "浏览器")

            if self.page:
                self.page.close()
                self.page = None

            if self.context:
                self.context.close()
                self.context = None

            if self.browser:
                self.browser.close()
                self.browser = None

            if self.playwright:
                self.playwright.stop()
                self.playwright = None

            log_success("浏览器已关闭", "浏览器")

        except Exception as e:
            log_error("浏览器", e)
    
    def get_page(self) -> Optional[Page]:
        """
        获取页面对象
        
        Returns:
            页面对象
        """
        return self.page
    
    def is_ready(self) -> bool:
        """
        检查浏览器是否就绪（包含页面）

        Returns:
            是否就绪
        """
        return (self.playwright is not None and
                self.browser is not None and
                self.context is not None and
                self.page is not None)

    def is_browser_ready(self) -> bool:
        """
        检查浏览器实例是否就绪（不包含页面）

        Returns:
            浏览器实例是否就绪
        """
        return (self.playwright is not None and
                self.browser is not None and
                self.is_browser_alive())

    def is_context_ready(self) -> bool:
        """
        检查浏览器上下文是否就绪

        Returns:
            上下文是否就绪
        """
        return (self.is_browser_ready() and
                self.context is not None)


# 便捷函数
def create_browser_session() -> Tuple[bool, Optional[BrowserManager]]:
    """
    创建浏览器会话
    
    Returns:
        (是否成功, 浏览器管理器)
    """
    browser_manager = BrowserManager()
    
    if browser_manager.init_browser():
        return True, browser_manager
    else:
        return False, None


def navigate_to_login_page(browser_manager: BrowserManager) -> bool:
    """
    导航到登录页面的便捷函数
    
    Args:
        browser_manager: 浏览器管理器
        
    Returns:
        是否成功
    """
    return browser_manager.navigate_to_login()
