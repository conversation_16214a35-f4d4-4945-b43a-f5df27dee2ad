#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块
负责学员数据的加载、验证和处理
"""

import csv
import sys
from pathlib import Path
from typing import List, Dict, Optional
from loguru import logger

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False


class DataManager:
    """数据管理器"""
    
    def __init__(self):
        """初始化数据管理器"""
        self.students = []
        self.required_fields = ["姓名", "身份证号", "密码"]
    
    def load_students_from_csv(self, file_path: str) -> List[Dict]:
        """
        从CSV文件加载学员数据
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            学员数据列表
        """
        try:
            students = []
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.error(f"数据文件不存在: {file_path}")
                return []
            
            # 尝试不同的编码，优先使用utf-8-sig处理BOM
            encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        reader = csv.DictReader(f)
                        students = list(reader)
                    logger.info(f"成功使用 {encoding} 编码读取文件")
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.warning(f"使用 {encoding} 编码读取失败: {e}")
                    continue
            
            if not students:
                logger.error("无法读取CSV文件，请检查文件编码")
                return []
            
            # 验证数据格式
            validated_students = self._validate_students_data(students)
            
            logger.info(f"成功加载 {len(validated_students)} 名学员数据")
            return validated_students
            
        except Exception as e:
            logger.error(f"加载学员数据失败: {e}")
            return []
    
    def load_students_from_excel(self, file_path: str) -> List[Dict]:
        """
        从Excel文件加载学员数据

        Args:
            file_path: Excel文件路径

        Returns:
            学员数据列表
        """
        if not PANDAS_AVAILABLE:
            logger.error("pandas未安装，无法读取Excel文件")
            return []

        try:
            file_path = Path(file_path)

            if not file_path.exists():
                logger.error(f"数据文件不存在: {file_path}")
                return []

            # 读取Excel文件
            df = pd.read_excel(file_path)

            # 转换为字典列表
            students = df.to_dict('records')

            # 验证数据格式
            validated_students = self._validate_students_data(students)

            logger.info(f"成功加载 {len(validated_students)} 名学员数据")
            return validated_students

        except Exception as e:
            logger.error(f"加载Excel数据失败: {e}")
            return []
    
    def _validate_students_data(self, students: List[Dict]) -> List[Dict]:
        """
        验证学员数据格式
        
        Args:
            students: 原始学员数据
            
        Returns:
            验证后的学员数据
        """
        validated_students = []
        
        for i, student in enumerate(students, 1):
            try:
                # 检查必要字段
                missing_fields = []
                for field in self.required_fields:
                    if field not in student or not str(student[field]).strip():
                        missing_fields.append(field)
                
                if missing_fields:
                    logger.warning(f"第{i}行学员数据缺少字段: {missing_fields}")
                    continue
                
                # 验证身份证号格式
                id_card = str(student["身份证号"]).strip()
                if not self._validate_id_card(id_card):
                    logger.warning(f"第{i}行学员身份证号格式错误: {id_card}")
                    continue
                
                # 清理数据
                cleaned_student = {
                    "姓名": str(student["姓名"]).strip(),
                    "身份证号": id_card,
                    "密码": str(student["密码"]).strip()
                }
                
                # 添加可选字段
                optional_fields = ["手机号", "邮箱", "备注"]
                for field in optional_fields:
                    if field in student and student[field]:
                        cleaned_student[field] = str(student[field]).strip()
                
                validated_students.append(cleaned_student)
                
            except Exception as e:
                logger.warning(f"第{i}行学员数据处理失败: {e}")
                continue
        
        return validated_students
    
    def _validate_id_card(self, id_card: str) -> bool:
        """
        验证身份证号格式
        
        Args:
            id_card: 身份证号
            
        Returns:
            是否有效
        """
        if not id_card:
            return False
        
        # 基本长度检查
        if len(id_card) not in [15, 18]:
            return False
        
        # 18位身份证号检查
        if len(id_card) == 18:
            # 前17位必须是数字
            if not id_card[:17].isdigit():
                return False
            
            # 最后一位可以是数字或X
            if not (id_card[17].isdigit() or id_card[17].upper() == 'X'):
                return False
        
        # 15位身份证号检查
        elif len(id_card) == 15:
            if not id_card.isdigit():
                return False
        
        return True
    
    def save_students_to_csv(self, students: List[Dict], file_path: str) -> bool:
        """
        保存学员数据到CSV文件
        
        Args:
            students: 学员数据
            file_path: 保存路径
            
        Returns:
            是否成功
        """
        try:
            if not students:
                logger.warning("没有学员数据需要保存")
                return False
            
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=students[0].keys())
                writer.writeheader()
                writer.writerows(students)
            
            logger.info(f"成功保存 {len(students)} 名学员数据到 {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存学员数据失败: {e}")
            return False
    
    def get_student_summary(self, students: List[Dict]) -> Dict:
        """
        获取学员数据摘要
        
        Args:
            students: 学员数据
            
        Returns:
            数据摘要
        """
        if not students:
            return {"total": 0, "valid": 0, "invalid": 0}
        
        summary = {
            "total": len(students),
            "valid": len(students),
            "invalid": 0,
            "fields": list(students[0].keys()) if students else [],
            "sample": students[0] if students else {}
        }
        
        return summary


# 便捷函数
def load_students_data(file_path: str = "students.csv") -> List[Dict]:
    """
    加载学员数据的便捷函数

    Args:
        file_path: 数据文件路径

    Returns:
        学员数据列表
    """
    data_manager = DataManager()

    file_path = Path(file_path)

    if file_path.suffix.lower() in ['.xlsx', '.xls']:
        if PANDAS_AVAILABLE:
            return data_manager.load_students_from_excel(str(file_path))
        else:
            logger.error("Excel文件需要pandas支持，请安装: pip install pandas")
            return []
    else:
        return data_manager.load_students_from_csv(str(file_path))


def validate_student_data(student: Dict) -> bool:
    """
    验证单个学员数据
    
    Args:
        student: 学员数据
        
    Returns:
        是否有效
    """
    required_fields = ["姓名", "身份证号", "密码"]
    
    for field in required_fields:
        if field not in student or not str(student[field]).strip():
            return False
    
    return True


def create_sample_data(file_path: str = "students_sample.csv") -> bool:
    """
    创建示例数据文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        是否成功
    """
    try:
        sample_data = [
            {
                "姓名": "张三",
                "身份证号": "350122199510201430",
                "密码": "password123",
                "备注": "示例数据1"
            },
            {
                "姓名": "李四", 
                "身份证号": "350425198804062414",
                "密码": "password456",
                "备注": "示例数据2"
            },
            {
                "姓名": "王五",
                "身份证号": "350104198903055449", 
                "密码": "password789",
                "备注": "示例数据3"
            }
        ]
        
        data_manager = DataManager()
        return data_manager.save_students_to_csv(sample_data, file_path)
        
    except Exception as e:
        logger.error(f"创建示例数据失败: {e}")
        return False
