#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器实现
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional, Union
from copy import deepcopy

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

from .defaults import DEFAULT_CONFIG, DEFAULT_SELECTORS, ENV_MAPPINGS, TYPE_CONVERTERS


class ConfigManager:
    """
    配置管理器
    
    支持多层配置加载：
    1. 默认配置（最低优先级）
    2. 配置文件（中等优先级）
    3. 环境变量（最高优先级）
    """
    
    def __init__(self, config_file: Optional[Union[str, Path]] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径（可选）
        """
        self.config_file = Path(config_file) if config_file else None
        self.config = {}
        self.selectors = {}
        
        # 加载配置
        self._load_all_configs()
    
    def _load_all_configs(self) -> None:
        """加载所有配置"""
        # 1. 加载默认配置
        self._load_default_config()
        
        # 2. 加载配置文件
        self._load_config_file()
        
        # 3. 加载环境变量覆盖
        self._load_env_overrides()
    
    def _load_default_config(self) -> None:
        """加载默认配置"""
        self.config = deepcopy(DEFAULT_CONFIG)
        self.selectors = deepcopy(DEFAULT_SELECTORS)
    
    def _load_config_file(self) -> None:
        """加载配置文件"""
        if not self.config_file or not self.config_file.exists():
            return
        
        try:
            file_extension = self.config_file.suffix.lower()
            
            if file_extension == '.yaml' or file_extension == '.yml':
                if not YAML_AVAILABLE:
                    print("警告: PyYAML未安装，无法加载YAML配置文件")
                    return
                
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)
            
            elif file_extension == '.json':
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
            
            else:
                print(f"警告: 不支持的配置文件格式: {file_extension}")
                return
            
            if file_config:
                # 更新主配置
                if 'config' in file_config:
                    self.config.update(file_config['config'])
                
                # 更新选择器配置
                if 'selectors' in file_config:
                    self.selectors.update(file_config['selectors'])
                
                # 如果文件直接包含配置项（向后兼容）
                if 'config' not in file_config and 'selectors' not in file_config:
                    self.config.update(file_config)
                
                print(f"配置文件加载成功: {self.config_file}")
        
        except Exception as e:
            print(f"配置文件加载失败: {e}")
    
    def _load_env_overrides(self) -> None:
        """加载环境变量覆盖"""
        for env_key, config_key in ENV_MAPPINGS.items():
            env_value = os.getenv(env_key)
            if env_value is not None:
                # 类型转换
                converted_value = self._convert_type(config_key, env_value)
                self.config[config_key] = converted_value
                print(f"环境变量覆盖: {config_key} = {converted_value}")
    
    def _convert_type(self, config_key: str, value: str) -> Any:
        """
        类型转换
        
        Args:
            config_key: 配置键名
            value: 字符串值
            
        Returns:
            转换后的值
        """
        if config_key in TYPE_CONVERTERS:
            try:
                converter = TYPE_CONVERTERS[config_key]
                return converter(value)
            except (ValueError, TypeError) as e:
                print(f"类型转换失败 {config_key}={value}: {e}")
                return value
        
        return value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键名
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(key, default)
    
    def get_selector(self, key: str, default: list = None) -> list:
        """
        获取选择器配置
        
        Args:
            key: 选择器键名
            default: 默认值
            
        Returns:
            选择器列表
        """
        if default is None:
            default = []
        return self.selectors.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键名
            value: 配置值
        """
        self.config[key] = value
    
    def update(self, config_dict: Dict[str, Any]) -> None:
        """
        批量更新配置
        
        Args:
            config_dict: 配置字典
        """
        self.config.update(config_dict)
    
    def reload(self) -> None:
        """重新加载所有配置"""
        self._load_all_configs()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        获取完整配置字典
        
        Returns:
            配置字典
        """
        return deepcopy(self.config)
    
    def save_to_file(self, file_path: Union[str, Path], format: str = 'yaml') -> bool:
        """
        保存配置到文件
        
        Args:
            file_path: 文件路径
            format: 文件格式 ('yaml' 或 'json')
            
        Returns:
            是否保存成功
        """
        try:
            file_path = Path(file_path)
            
            config_data = {
                'config': self.to_dict(),
                'selectors': deepcopy(self.selectors)
            }
            
            if format.lower() == 'yaml':
                if not YAML_AVAILABLE:
                    print("错误: PyYAML未安装，无法保存YAML格式")
                    return False
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            elif format.lower() == 'json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False, default=str)
            
            else:
                print(f"错误: 不支持的格式: {format}")
                return False
            
            print(f"配置保存成功: {file_path}")
            return True
        
        except Exception as e:
            print(f"配置保存失败: {e}")
            return False
    
    def __getitem__(self, key: str) -> Any:
        """支持字典式访问"""
        return self.config[key]
    
    def __setitem__(self, key: str, value: Any) -> None:
        """支持字典式设置"""
        self.config[key] = value
    
    def __contains__(self, key: str) -> bool:
        """支持 in 操作符"""
        return key in self.config
