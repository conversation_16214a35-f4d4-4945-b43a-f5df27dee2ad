#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置包初始化文件
保持向后兼容性，同时提供新的配置管理功能
"""

from pathlib import Path
from .manager import ConfigManager
from .defaults import DEFAULT_CONFIG, DEFAULT_SELECTORS

# 创建全局配置管理器实例
_config_manager = None

def get_config_manager(config_file=None):
    """
    获取配置管理器实例（单例模式）
    
    Args:
        config_file: 配置文件路径（可选）
        
    Returns:
        ConfigManager实例
    """
    global _config_manager
    if _config_manager is None:
        # 尝试查找配置文件
        if config_file is None:
            # 查找可能的配置文件
            possible_files = [
                Path("config.yaml"),
                Path("config.yml"), 
                Path("config.json"),
                Path("../config.yaml"),
                Path("../config.yml"),
                Path("../config.json")
            ]
            
            for file_path in possible_files:
                if file_path.exists():
                    config_file = file_path
                    break
        
        _config_manager = ConfigManager(config_file)
    
    return _config_manager

# 为了向后兼容，保持原有的CONFIG字典接口
class CompatibleConfigDict(dict):
    """
    兼容的配置字典类
    保持原有的字典访问方式，同时支持新的配置管理功能
    """
    
    def __init__(self):
        super().__init__()
        self._manager = get_config_manager()
        # 初始化时同步配置
        self.update(self._manager.to_dict())
    
    def __getitem__(self, key):
        # 从配置管理器获取最新值
        return self._manager.get(key, super().get(key))
    
    def get(self, key, default=None):
        # 从配置管理器获取最新值
        return self._manager.get(key, default)
    
    def update_from_manager(self):
        """从配置管理器更新配置"""
        super().clear()
        super().update(self._manager.to_dict())
    
    def reload(self):
        """重新加载配置"""
        self._manager.reload()
        self.update_from_manager()

# 创建兼容的CONFIG字典
CONFIG = CompatibleConfigDict()

# 选择器配置（保持向后兼容）
class CompatibleSelectorsDict(dict):
    """兼容的选择器字典类"""
    
    def __init__(self):
        super().__init__()
        self._manager = get_config_manager()
        # 初始化时同步选择器
        self.update(self._manager.selectors)
    
    def __getitem__(self, key):
        return self._manager.get_selector(key, super().get(key, []))
    
    def get(self, key, default=None):
        if default is None:
            default = []
        return self._manager.get_selector(key, default)

# 创建兼容的SELECTORS字典
SELECTORS = CompatibleSelectorsDict()

# 提供配置更新函数（保持向后兼容）
def update_config(config_dict):
    """
    更新配置（向后兼容函数）
    
    Args:
        config_dict: 配置字典
    """
    manager = get_config_manager()
    manager.update(config_dict)
    CONFIG.update_from_manager()

def reload_config():
    """重新加载配置"""
    CONFIG.reload()
    SELECTORS.clear()
    SELECTORS.update(get_config_manager().selectors)

# 实用函数
def get_config(key, default=None):
    """获取配置值的便捷函数"""
    return CONFIG.get(key, default)

def get_selector(key, default=None):
    """获取选择器配置的便捷函数"""
    if default is None:
        default = []
    return SELECTORS.get(key, default)

def is_headless():
    """检查是否为无头模式"""
    return CONFIG.get("headless", False)

def get_timeout(timeout_type="page"):
    """获取超时配置"""
    timeout_key = f"{timeout_type}_timeout"
    return CONFIG.get(timeout_key, 30000)

def get_retry_config():
    """获取重试配置"""
    return {
        "max_retries": CONFIG.get("max_retries", 3),
        "retry_delay": CONFIG.get("retry_delay", 2.0),
        "captcha_retries": CONFIG.get("captcha_retries", 3)
    }

# 导出主要接口
__all__ = [
    'ConfigManager',
    'CONFIG',
    'SELECTORS',
    'get_config_manager',
    'update_config',
    'reload_config',
    'get_config',
    'get_selector',
    'is_headless',
    'get_timeout',
    'get_retry_config'
]
