#!/bin/bash
# 自考成绩查询自动化系统 v1.0 - 一键启用脚本 (Linux/macOS)

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}📋 $1${NC}"
}

echo
echo "========================================"
echo "   自考成绩查询自动化系统 v1.0"
echo "   一键启用脚本 (Linux/macOS)"
echo "========================================"
echo

# 检查Python是否安装
echo "[1/8] 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        print_error "Python未安装或未添加到PATH"
        echo "💡 请先安装Python 3.8+"
        echo "📥 Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "📥 CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "📥 macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi
print_success "Python环境检查通过"

# 检查pip是否可用
echo "[2/8] 检查pip工具..."
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        print_error "pip工具不可用"
        echo "💡 请安装pip: sudo apt install python3-pip (Ubuntu/Debian)"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi
print_success "pip工具检查通过"

# 创建必要目录
echo "[3/8] 创建必要目录..."
mkdir -p output/screenshots
mkdir -p output/logs
mkdir -p data
print_success "目录创建完成"

# 安装Python依赖
echo "[4/8] 安装Python依赖包..."
echo "📦 正在安装依赖，请稍候..."
if ! $PIP_CMD install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --quiet; then
    echo "❌ 清华源安装失败，尝试使用默认源..."
    if ! $PIP_CMD install -r requirements.txt --quiet; then
        print_error "依赖安装失败"
        echo "💡 请检查网络连接或手动安装: $PIP_CMD install -r requirements.txt"
        exit 1
    fi
fi
print_success "Python依赖安装完成"

# 安装Playwright浏览器
echo "[5/8] 安装Playwright浏览器..."
echo "🌐 正在下载Chromium浏览器，请稍候..."
if ! $PYTHON_CMD -m playwright install chromium --quiet; then
    print_error "Playwright浏览器安装失败"
    echo "💡 请检查网络连接或手动安装: $PYTHON_CMD -m playwright install chromium"
    exit 1
fi
print_success "Playwright浏览器安装完成"

# 检查配置文件
echo "[6/8] 检查配置文件..."
if [ ! -f "config.yaml" ]; then
    print_error "config.yaml配置文件缺失"
    echo "💡 请确保config.yaml文件存在"
    exit 1
fi
print_success "配置文件检查通过"

# 检查数据文件
echo "[7/8] 检查数据文件..."
if [ ! -f "data/students.csv" ]; then
    print_warning "学员数据文件不存在，创建示例文件..."
    echo "姓名,身份证号,密码" > data/students.csv
    echo "张三,123456789012345678,password123" >> data/students.csv
    print_success "已创建示例数据文件，请编辑 data/students.csv 添加真实数据"
else
    print_success "数据文件检查通过"
fi

# 运行系统检查
echo "[8/8] 运行系统自检..."
if ! $PYTHON_CMD -c "import sys; sys.path.append('.'); from src.config import CONFIG; print('✅ 配置系统正常')" 2>/dev/null; then
    print_error "系统配置检查失败"
    echo "💡 请检查源代码文件是否完整"
    exit 1
fi

if ! $PYTHON_CMD -c "import playwright; print('✅ Playwright正常')" 2>/dev/null; then
    print_error "Playwright检查失败"
    echo "💡 请重新运行脚本或手动安装: $PYTHON_CMD -m playwright install chromium"
    exit 1
fi

echo
echo "========================================"
echo "🎉 系统启用完成！"
echo "========================================"
echo
print_info "系统信息:"
echo "   📁 项目目录: $(pwd)"
echo "   📄 配置文件: config.yaml"
echo "   📊 数据文件: data/students.csv"
echo "   📸 截图目录: output/screenshots"
echo "   📝 日志目录: output/logs"
echo
print_info "启动命令:"
echo "   $PYTHON_CMD src/main.py              # 标准模式"
echo "   $PYTHON_CMD src/main.py --headless   # 无头模式"
echo "   $PYTHON_CMD src/main.py --debug      # 调试模式"
echo
print_info "使用说明:"
echo "   1. 编辑 data/students.csv 添加学员信息"
echo "   2. 根据需要修改 config.yaml 配置"
echo "   3. 运行上述启动命令开始处理"
echo

# 询问是否立即启动
read -p "是否立即启动系统？(y/n): " choice
case "$choice" in 
    y|Y|yes|YES ) 
        echo
        echo "🚀 启动系统..."
        $PYTHON_CMD src/main.py
        ;;
    * ) 
        echo
        echo "💡 您可以稍后使用以下命令启动系统:"
        echo "   $PYTHON_CMD src/main.py"
        ;;
esac

echo
echo "脚本执行完成！"
