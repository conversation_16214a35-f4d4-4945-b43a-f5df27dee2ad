#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度查看工具
用于查看当前的处理进度和统计信息
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

def format_time(iso_time_str):
    """格式化时间显示"""
    if not iso_time_str:
        return "未设置"
    try:
        dt = datetime.fromisoformat(iso_time_str)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except:
        return iso_time_str

def view_progress_file():
    """查看进度文件内容"""
    progress_file = Path("progress.json")
    
    if not progress_file.exists():
        print("📝 当前没有进度文件")
        print("💡 进度文件会在开始批量处理时自动创建")
        return False
    
    try:
        with open(progress_file, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)
        
        print("📊 进度文件内容")
        print("=" * 60)
        
        # 基本信息
        print(f"🆔 会话ID: {progress_data.get('session_id', '未知')}")
        print(f"📅 开始时间: {format_time(progress_data.get('start_time'))}")
        print(f"📅 结束时间: {format_time(progress_data.get('end_time'))}")
        print(f"📋 状态: {progress_data.get('status', '未知')}")
        print(f"👥 总学员数: {progress_data.get('total_students', 0)}")
        
        # 统计信息
        stats = progress_data.get('statistics', {})
        print(f"\n📈 处理统计:")
        print(f"   ✅ 已完成: {stats.get('completed', 0)} 名")
        print(f"   ❌ 失败: {stats.get('failed', 0)} 名")
        print(f"   ⏭️ 跳过: {stats.get('skipped', 0)} 名")
        print(f"   📊 成功率: {stats.get('success_rate', 0):.1f}%")
        
        # 已完成学员
        completed = progress_data.get('completed', [])
        if completed:
            print(f"\n✅ 已完成学员 ({len(completed)} 名):")
            for i, student in enumerate(completed, 1):
                time_str = format_time(student.get('timestamp', ''))
                print(f"   {i}. {student.get('name', '未知')} ({student.get('id', '未知')}) - {time_str}")
        
        # 失败学员
        failed = progress_data.get('failed', [])
        if failed:
            print(f"\n❌ 失败学员 ({len(failed)} 名):")
            for i, student in enumerate(failed, 1):
                time_str = format_time(student.get('timestamp', ''))
                error = student.get('error_message', '未知错误')
                print(f"   {i}. {student.get('name', '未知')} ({student.get('id', '未知')}) - {error}")
                print(f"      时间: {time_str}")
        
        # 跳过学员
        skipped = progress_data.get('skipped', [])
        if skipped:
            print(f"\n⏭️ 跳过学员 ({len(skipped)} 名):")
            for i, student in enumerate(skipped, 1):
                time_str = format_time(student.get('timestamp', ''))
                print(f"   {i}. {student.get('name', '未知')} ({student.get('id', '未知')}) - {time_str}")
        
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ 读取进度文件失败: {e}")
        return False

def view_progress_manager():
    """使用ProgressManager查看进度"""
    try:
        from core.progress import ProgressManager
        
        print("🔧 使用ProgressManager查看进度")
        print("=" * 60)
        
        progress_manager = ProgressManager()
        
        # 检查是否有未完成会话
        if progress_manager.has_unfinished_session():
            print("🔄 发现未完成的处理会话")
            
            # 获取会话信息
            session_info = progress_manager.get_session_info()
            if session_info:
                print(f"🆔 会话ID: {session_info.get('session_id')}")
                print(f"📅 开始时间: {format_time(session_info.get('start_time'))}")
                print(f"👥 总学员数: {session_info.get('total_students', 0)}")
            
            # 获取统计信息
            stats = progress_manager.get_statistics()
            print(f"\n📈 当前统计:")
            print(f"   ✅ 已完成: {stats.get('completed', 0)} 名")
            print(f"   ❌ 失败: {stats.get('failed', 0)} 名")
            print(f"   ⏭️ 跳过: {stats.get('skipped', 0)} 名")
            print(f"   📊 成功率: {stats.get('success_rate', 0):.1f}%")
            
            # 获取已处理学员ID
            completed_ids, failed_ids, skipped_ids = progress_manager.get_processed_student_ids()
            total_processed = len(completed_ids) + len(failed_ids) + len(skipped_ids)
            print(f"   📝 已处理总数: {total_processed} 名")
            
        else:
            print("📝 当前没有未完成的处理会话")
        
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ ProgressManager查看失败: {e}")
        return False

def simulate_progress():
    """模拟创建一些进度数据用于演示"""
    print("🎭 创建模拟进度数据用于演示")
    print("=" * 60)
    
    try:
        from core.progress import ProgressManager
        
        # 创建进度管理器
        progress_manager = ProgressManager("demo_progress.json")
        
        # 开始会话
        session_id = progress_manager.start_session(5)
        print(f"✅ 创建演示会话: {session_id}")
        
        # 模拟处理一些学员
        demo_students = [
            ("350122199510201430", "张三", "completed"),
            ("350425198804062414", "李四", "failed", "验证码识别失败"),
            ("350104198903055449", "王五", "completed"),
            ("350427198206222020", "杨文颖", "failed", "网络连接超时"),
        ]
        
        for student_id, name, status, *error in demo_students:
            error_msg = error[0] if error else None
            progress_manager.save_student_progress(student_id, name, status, error_msg)
            print(f"   📝 保存 {name}: {status}")
        
        print(f"\n📊 演示数据创建完成")
        print(f"📁 文件位置: demo_progress.json")
        print(f"💡 您可以查看这个文件了解进度数据格式")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建演示数据失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 进度管理系统查看工具")
    print("=" * 60)
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 查看当前进度文件")
        print("2. 使用ProgressManager查看进度")
        print("3. 创建演示进度数据")
        print("4. 查看演示进度数据")
        print("5. 清理演示数据")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "1":
            print("\n" + "=" * 40)
            view_progress_file()
            
        elif choice == "2":
            print("\n" + "=" * 40)
            view_progress_manager()
            
        elif choice == "3":
            print("\n" + "=" * 40)
            simulate_progress()
            
        elif choice == "4":
            print("\n" + "=" * 40)
            # 临时切换到演示文件
            import shutil
            if Path("demo_progress.json").exists():
                # 备份原文件
                if Path("progress.json").exists():
                    shutil.copy("progress.json", "progress_backup.json")
                # 复制演示文件
                shutil.copy("demo_progress.json", "progress.json")
                view_progress_file()
                # 恢复原文件
                if Path("progress_backup.json").exists():
                    shutil.move("progress_backup.json", "progress.json")
                else:
                    Path("progress.json").unlink()
            else:
                print("❌ 演示数据不存在，请先创建")
                
        elif choice == "5":
            print("\n" + "=" * 40)
            demo_file = Path("demo_progress.json")
            if demo_file.exists():
                demo_file.unlink()
                print("✅ 演示数据已清理")
            else:
                print("📝 没有演示数据需要清理")
                
        elif choice == "0":
            print("\n👋 再见！")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
