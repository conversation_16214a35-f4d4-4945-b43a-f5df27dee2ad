{"config": {"login_url": "https://121.204.170.198:8082/zk/reg/v3/?rlx=1752850109&p=FIXED06FA3SKREG", "headless": false, "slow_mo": 500, "page_timeout": 30000, "navigation_timeout": 20000, "element_timeout": 10000, "max_retries": 3, "retry_delay": 2.0, "captcha_retries": 3, "screenshot_dir": "output/screenshots", "log_dir": "output/logs", "data_file": "data/students.csv", "log_level": "INFO", "log_rotation": "10 MB", "log_retention": "7 days", "captcha_debug": true, "captcha_save_path": "../output/logs/captcha_debug", "screenshot_quality": 90, "screenshot_format": "png", "viewport_width": 1920, "viewport_height": 1080}, "selectors": {"username_input": ["input[name='username']", "input[id='username']", "#username", "input[placeholder*='身份证']"], "password_input": ["input[name='password']", "input[id='password']", "#password", "input[type='password']"], "captcha_input": ["input[name='captcha']", "input[id='captcha']", "#captcha", "input[placeholder*='验证码']"], "login_button": ["#loginBtn", ".loginBtn", "button[type='submit']", "input[type='submit']", ".login-btn"], "score_query_menu": ["text=当次成绩查询", "//a[contains(text(), '当次成绩查询')]", "//td[contains(text(), '当次成绩查询')]", "//span[contains(text(), '当次成绩查询')]", "[title*='当次成绩查询']", "[alt*='当次成绩查询']"], "query_options": ["text=点击进入", "//a[contains(text(), '点击进入')]", "//button[contains(text(), '点击进入')]", "//input[contains(@value, '点击进入')]", "[title*='点击进入']", "[alt*='点击进入']"], "menu_items": ["text=当次成绩查询", "//a[contains(text(), '当次成绩查询')]", "[title*='成绩查询']", "//li[contains(text(), '当次成绩查询')]"], "score_table": ["table", ".score-table", "#score_table", "[class*='table']"], "password_change_indicators": ["text=修改密码", "text=密码修改", "text=修改登录密码", "text=密码即将过期", "text=请修改密码", "//text()[contains(., '修改密码')]", "//text()[contains(., '密码修改')]", "//text()[contains(., '修改登录密码')]", "//text()[contains(., '密码即将过期')]", "//text()[contains(., '请修改密码')]", "//div[contains(text(), '修改密码')]", "//span[contains(text(), '修改密码')]", "//h1[contains(text(), '修改密码')]", "//h2[contains(text(), '修改密码')]", "//h3[contains(text(), '修改密码')]", "[title*='修改密码']", "[alt*='修改密码']"], "old_password_input": ["input[name='oldpwd']", "input[name='old_password']", "input[name='oldPassword']", "input[id='oldpwd']", "input[id='old_password']", "input[id='oldPassword']", "#oldpwd", "#old_password", "#oldPassword", "input[placeholder*='原密码']", "input[placeholder*='旧密码']", "input[placeholder*='当前密码']", "input[placeholder*='输入原密码']", "input[type='password'][name*='old']", "input[type='password'][id*='old']"], "new_password_input": ["input[name='newpwd']", "input[name='new_password']", "input[name='newPassword']", "input[name='mypwd']", "input[id='newpwd']", "input[id='new_password']", "input[id='newPassword']", "#newpwd", "#new_password", "#newPassword", "input[placeholder*='新密码']", "input[placeholder*='输入新密码']", "input[placeholder*='请输入新密码']", "input[type='password'][name*='new']", "input[type='password'][id*='new']"], "confirm_password_input": ["input[name='confirmpwd']", "input[name='confirm_password']", "input[name='confirmPassword']", "input[name='repwd']", "input[name='re_password']", "input[id='confirmpwd']", "input[id='confirm_password']", "input[id='confirmPassword']", "input[id='repwd']", "input[id='re_password']", "#confirmpwd", "#confirm_password", "#confirmPassword", "#repwd", "#re_password", "input[placeholder*='确认密码']", "input[placeholder*='再次输入']", "input[placeholder*='重复密码']", "input[placeholder*='再次输入密码']", "input[type='password'][name*='confirm']", "input[type='password'][id*='confirm']", "input[type='password'][name*='re']", "input[type='password'][id*='re']"], "change_password_button": ["input[value*='修改密码']", "input[value*='修改登录密码']", "input[value*='确认修改']", "input[value*='提交']", "button:has-text('修改密码')", "button:has-text('修改登录密码')", "button:has-text('确认修改')", "button:has-text('提交')", "//button[contains(text(), '修改密码')]", "//button[contains(text(), '修改登录密码')]", "//button[contains(text(), '确认修改')]", "//input[contains(@value, '修改密码')]", "//input[contains(@value, '修改登录密码')]", "//input[contains(@value, '确认修改')]", "#changePasswordBtn", "#modifyPasswordBtn", ".change-password-btn", ".modify-password-btn", "[onclick*='changePassword']", "[onclick*='modifyPassword']"]}}