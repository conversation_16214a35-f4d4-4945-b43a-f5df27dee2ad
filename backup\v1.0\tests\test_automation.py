#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化测试程序
测试自考成绩查询系统的各个功能模块，包含完整的浏览器管理和登录功能
"""

import sys
import time
import unittest
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent / "src"))

from modules.data import load_students_data
from modules.browser import BrowserManager
from modules.login import LoginManager
from modules.navigation import navigate_after_login
from modules.screenshot import capture_student_screenshot
from modules.captcha import CaptchaRecognizer
from modules.password_change import PasswordChangeHandler
from modules.logger import get_logger_manager, log_info, log_success, log_error, log_warning
from config import CONFIG, SELECTORS


class AutomationTestSuite:
    """自动化测试套件 - 基于主程序架构的完整测试实现"""
    
    def __init__(self, data_file: str = "data/students.csv"):
        """
        初始化测试套件
        
        Args:
            data_file: 测试数据文件路径
        """
        self.data_file = data_file
        self.logger_manager = get_logger_manager()
        self.students = []
        self.test_results = {
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "errors": [],
            "start_time": None,
            "end_time": None
        }
        
        log_info("自动化测试套件初始化完成", "测试系统")
    
    def load_test_data(self) -> bool:
        """加载测试数据"""
        log_info(f"加载测试数据文件: {self.data_file}", "数据加载")
        
        try:
            self.students = load_students_data(self.data_file)
            
            if not self.students:
                log_error("数据加载", Exception("未能加载任何测试数据"))
                return False
            
            log_success(f"成功加载 {len(self.students)} 条测试数据", "数据加载")
            
            # 显示测试数据概览
            print(f"\n📋 测试数据概览 ({len(self.students)} 条):")
            for i, student in enumerate(self.students[:3], 1):  # 只显示前3条
                print(f"   {i}. {student['姓名']} ({student['身份证号']})")
            if len(self.students) > 3:
                print(f"   ... 还有 {len(self.students) - 3} 条数据")
            
            return True
            
        except Exception as e:
            log_error("数据加载", e)
            return False

    def check_password_change_requirement(self, page) -> bool:
        """
        检测是否需要修改密码
        
        Args:
            page: 页面对象
            
        Returns:
            是否需要修改密码
        """
        try:
            log_info("检测是否出现密码修改页面", "密码检测")
            
            # 检测密码修改相关的元素
            password_change_indicators = [
                "修改登录密码",
                "修改密码", 
                "密码修改",
                "更改密码",
                "#mypwd",  # 新密码输入框
                "#mypwd2", # 确认密码输入框
                "#btnsubmit"  # 提交按钮
            ]
            
            # 等待页面稳定
            time.sleep(2)
            
            # 检查页面内容
            page_content = page.content()
            
            # 检查是否包含密码修改相关文本
            text_found = any(indicator in page_content for indicator in password_change_indicators[:4])
            
            # 检查是否存在密码修改表单元素
            elements_found = False
            try:
                # 检查新密码输入框
                new_pwd_element = page.query_selector(SELECTORS["password_change"]["new_password"])
                confirm_pwd_element = page.query_selector(SELECTORS["password_change"]["confirm_password"])
                submit_btn_element = page.query_selector(SELECTORS["password_change"]["submit_button"])
                
                elements_found = all([new_pwd_element, confirm_pwd_element, submit_btn_element])
                
            except Exception as e:
                log_warning(f"检测密码修改元素时出错: {e}", "密码检测")
            
            # 如果文本或元素任一存在，则认为需要修改密码
            needs_change = text_found or elements_found
            
            if needs_change:
                log_info("检测到密码修改页面，需要执行密码修改", "密码检测")
            else:
                log_info("未检测到密码修改页面，跳过密码修改步骤", "密码检测")
            
            return needs_change
            
        except Exception as e:
            log_error("密码检测", e)
            return False

    def handle_conditional_password_change(self, page, student: Dict) -> Tuple[bool, str]:
        """
        条件性处理密码修改
        
        Args:
            page: 页面对象
            student: 学员信息
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            student_name = student["姓名"]
            current_password = student["密码"]
            
            log_info(f"开始条件性密码修改处理: {student_name}", "密码修改")
            
            # 检查是否需要修改密码
            if not self.check_password_change_requirement(page):
                log_info("无需修改密码，继续正常流程", "密码修改")
                return True, "无需修改密码"
            
            # 需要修改密码，执行修改流程
            log_info("执行自动密码修改流程", "密码修改")
            
            # 使用现有的密码修改处理器
            password_handler = PasswordChangeHandler()
            
            # 执行密码修改（使用当前密码作为新密码）
            success, error_msg = password_handler.change_password(
                page, 
                new_password=current_password,
                confirm_password=current_password
            )
            
            if success:
                log_success(f"学员 {student_name} 密码修改成功", "密码修改")
                return True, "密码修改成功"
            else:
                log_error("密码修改", Exception(f"密码修改失败: {error_msg}"))
                return False, f"密码修改失败: {error_msg}"
                
        except Exception as e:
            error_message = f"密码修改处理异常: {str(e)}"
            log_error("密码修改", e)
            return False, error_message

    def _execute_login_with_password_change(self, student: Dict, browser_manager: BrowserManager) -> Tuple[bool, str, Optional[object]]:
        """
        执行登录步骤（包含条件性密码修改）
        
        Args:
            student: 学员信息字典
            browser_manager: 浏览器管理器实例
            
        Returns:
            (是否成功, 错误信息, 页面对象)
        """
        try:
            student_name = student["姓名"]
            username = student["身份证号"]
            password = student["密码"]
            
            log_info("执行自动登录（含密码修改检测）", "登录流程")
            login_manager = LoginManager(browser_manager)
            success, error_msg, page = login_manager.perform_login(username, password, max_retries=3)
            
            if not success:
                error_message = f"登录失败: {error_msg}"
                log_error("登录流程", Exception(error_message))
                return False, error_message, None
            
            log_success("登录成功", "登录流程")
            
            # 登录成功后，检查并处理密码修改
            log_info("检查是否需要修改密码", "登录流程")
            pwd_success, pwd_error = self.handle_conditional_password_change(page, student)
            
            if not pwd_success:
                log_warning(f"密码修改处理失败: {pwd_error}", "登录流程")
                # 密码修改失败不影响后续流程，只记录警告
            
            return True, "登录成功", page
            
        except Exception as e:
            error_message = f"登录步骤异常: {str(e)}"
            log_error("登录流程", e)
            return False, error_message, None

    def _execute_navigation_step(self, page) -> Tuple[bool, str]:
        """
        执行页面导航步骤
        
        Args:
            page: 页面对象
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            log_info("执行页面导航", "导航流程")
            if not navigate_after_login(page):
                error_message = "页面导航失败"
                log_error("导航流程", Exception(error_message))
                return False, error_message
            
            log_success("页面导航成功", "导航流程")
            return True, "导航成功"
            
        except Exception as e:
            error_message = f"导航步骤异常: {str(e)}"
            log_error("导航流程", e)
            return False, error_message

    def _execute_screenshot_step(self, page, student_name: str) -> Tuple[bool, str, Optional[str]]:
        """
        执行截图步骤
        
        Args:
            page: 页面对象
            student_name: 学员姓名
            
        Returns:
            (是否成功, 错误信息, 截图路径)
        """
        try:
            log_info("执行成绩单截图", "截图流程")
            screenshot_path = capture_student_screenshot(page, student_name)
            
            if not screenshot_path:
                error_message = "成绩单截图失败"
                log_error("截图流程", Exception(error_message))
                return False, error_message, None
            
            log_success(f"成绩单截图成功: {screenshot_path}", "截图流程")
            return True, "截图成功", screenshot_path
            
        except Exception as e:
            error_message = f"截图步骤异常: {str(e)}"
            log_error("截图流程", e)
            return False, error_message, None

    def _cleanup_resources(self, page, browser_manager: BrowserManager) -> None:
        """
        清理资源
        
        Args:
            page: 页面对象（可能为None）
            browser_manager: 浏览器管理器实例
        """
        try:
            if page:
                browser_manager.close_page(page)
                log_info("页面资源清理完成", "资源清理")
        except Exception as e:
            log_warning(f"页面资源清理异常: {e}", "资源清理")
