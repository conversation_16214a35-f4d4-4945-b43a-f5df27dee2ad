<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml"><head>
<meta http-equiv="X-FRAME-OPTIONS" content="DENY">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>福建省高等教育自学考试 福建自考网上报名</title>

<script type="text/javascript" src="/framework/pjs/jquery-me.js"></script>
<script type="text/javascript" src="/framework/pjs/jquery.min.s01.js"></script>



<script>
<!--
function ready(s)
{
	var yhdllb=$_('yhdllb');
	var mn=$_('myname');
	if(mn == null || mn.value == '' || mn.value.length==0){
		if(yhdllb=="zjh"){
			alert("身份证号不能为空，请输入您的身份证号！");
		}else{
			alert("用户名不能为空，请输入您的用户名！");
		}
		mn.focus();return false;
	}
	var mp=$_('mypwd');
	if(mp == null || mp.value == '' || mp.value.length==0){
		alert("登录密码不能为空！");
		mp.focus();return false;
	}
	var vc=$_('verifycode');
	if(vc.value.length!=6){
		alert("请输入正确的验证序列!");
		vc.focus();vc.select();return false;
	}
	
    $.ajax({type:"post",url:"/zk/ajaxs/20201225/",data:{t:'skyhdl',yhdllb:yhdllb.value,myname:ie(mn.value),mypwd:ie(mp.value),verifycode:vc.value},cache:false,async:false,timeout:5,dataType:"json",success:function(result){ 
			//alert(result);
			if(result!=null){
				if(result.errcode=="20010"){
					window.location.href=result.url;
				}else{
					alert("警告："+result.msg);  
					get_verifycode();
				}		
			}else{
				alert("警告：errcode:19 操作失败！");  
				get_verifycode();
			}
        },
        error:function(jqXHR){alert("警告：errCode:"+jqXHR.status+" 请求失败，请稍后再试！");}
    });
}
function get_verifycode(){
	document.getElementById("seccodeimg").src="/zk/action/verify/?p=FIXEDB78E1UVERIFY&id="+Math.random();
}
-->
</script>
<style type="text/css">
<!--
body {
	font-family: "宋体";
	font-size: 12px;
	color: #494949;
	text-align: center;
	margin: 0px;
	padding: 0px;
	background-repeat: repeat-x;
	background-position: top;
	line-height: 18px;
}

body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form,
	fieldset, input, textarea, p, blockquote, th, td {
	padding: 0;
	margin: 0;
}

ol, ul {
	list-style: none;
}

a:link {
	color: #494949;
	text-decoration: none;
}

a:visited {
	color: #494949;
	text-decoration: none;
}

a:hover {
	color: #494949;
	text-decoration: none;
}

a:active {
	color: #494949;
	text-decoration: none;
}
/*放开*/ /*选中*/ /*出错*/
input.ipt-normal {
	border-color: #A0B4C5;
	background-color: #FFF
}

input.ipt-focus {
	border-color: #727272;
	background-color: #FFFBD5
}

input.ipt-error {
	border-color: #D5060D;
	background-color: #FEF5F5
}

input.inp {
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
	font-size: 14px;
	width: 190px;
	height: 30px;
	border: #838383 1px solid;
	padding: 2px 3px;
	font-size: 14px;
	line-height: 30px
}

.regs {
	line-height: 30px;
}

.regs a:link {
	color: #0000ff;
	font-weight: bold;
	text-decoration: none;
}

.regs a:visited {
	color: #0000ff;
	font-weight: bold;
	text-decoration: none;
}

.regs a:hover {
	color: #ee0000;
	font-weight: bold;
	text-decoration: underline;
}

.regs a:active {
	color: #ee0000;
	font-weight: bold;
	text-decoration: underline;
}

.loginBtn {
	display: block;
	color: #ffffff;
	width: 220px;
	height: 40px;
	line-height: 40px;
	border: none;
	overflow: hidden;
	text-align: center;
	background: #69b3f2;
	font-family: \5fae\8f6f\96c5\9ed1, \5fae\8f6f\96c5\9ed1, arial,
		\5b8b\4f53;
	font-size: 24px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
}
/*放开*/ /*选中*/
.spanif {
	font-size: 20px;
	font-family: \5fae\8f6f\96c5\9ed1, \9ed1\4f53, \5b8b\4f53;
	/*font-weight:bold;*/
	border-bottom: #39a6eb 2px solid;
	line-height: 50px;
	Cursor: pointer;
	background-color: #88caf3;
	color: #000000;
}

.spanin {
	font-size: 20px;
	font-family: \5fae\8f6f\96c5\9ed1, \9ed1\4f53, \5b8b\4f53;
	/*font-weight:bold;*/
	border-bottom: #39a6eb 0px solid;
	line-height: 50px;
	Cursor: pointer;
	background-color: #e1f2fc;
	color: #000000;
}

.regtop {
	background-color: #ffffff;
	text-align: center;
}

.regtopbox {
	margin: auto;
	background-color: #ffffff;
	width: 990px;
	height: 50px;
	overflow: hidden;
}

.regtopdiv {
	text-align: left;
	background-image: url(/framework/reg/regtop60.jpg);
	background-repeat: no-repeat;
}

.regtopspan {
	position: relative;
	left: 510px;
	font-size: 28px;
	font-family: \5fae\8f6f\96c5\9ed1, \9ed1\4f53, \5b8b\4f53;
	/*font-weight:bold;*/
	line-height: 50px;
	background-color: #ffffff;
	color: #000000;
}

.regtopreturn {
	position: relative;
	left: 530px;
	font-size: 14px;
	font-family: \5fae\8f6f\96c5\9ed1, \9ed1\4f53, \5b8b\4f53;
	height: 30px;
	line-height: 30px;
	Cursor: pointer;
	background-color: #efefef;
	color: #666666;
	padding: 3px;
}

.nav a {
	float: left;
	border: #ffffff 2px solid;
	font-size: 14px;
	letter-spacing: 4px;
	font-family: \5fae\8f6f\96c5\9ed1, \9ed1\4f53, \5b8b\4f53;
	width: 140px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	Cursor: pointer;
	background-color: #39a6ec;
	color: #ffffff;
}

.nav a:hover {
	float: left;
	border: #ffffff 2px solid;
	font-size: 14px;
	letter-spacing: 4px;
	font-family: \5fae\8f6f\96c5\9ed1, \9ed1\4f53, \5b8b\4f53;
	width: 140px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	Cursor: pointer;
	background-color: #ffffff;
	color: #39a6ec;
}
-->
</style>
<script language="javascript" type="text/javascript">
<!--
function changeMc(o){
	try{
		// x,y是关闭的层 z是即将显示的层
		 document.getElementById('span_s1').style.display = "none"
		 document.getElementById('span_s2').style.display = "none"
		 document.getElementById('s1').className= 'spanif';
		 document.getElementById('s2').className= 'spanif';
		 document.getElementById('yhdllb').value=document.getElementById('from_'+o).value;
		 document.getElementById('loginname').innerHTML=document.getElementById('span_'+o).value;
		 //document.getElementById('span_'+o).style.display = "block"
		 //document.getElementById('span_'+o).background='images/bg_news_pn.gif';
		 document.getElementById(o).className= 'spanin';
		 document.getElementById('myname').value= '';
		 document.getElementById('mypwd').value= '';
	}catch(e){
	
	}
}
 -->
</script>
</head>
<body>
	<!--top 福建省高等教育自学考试考务考籍管理系统 | 登录 -->
	<div class="regtop">
		<div class="regtopbox">
			<div class="regtopdiv">
				<span class="regtopspan">登录</span> <span class="regtopreturn"><a href="/">返回首页</a></span>
			</div>
		</div>
	</div>

	<!--top end-->
	<div style="background-color: #39a6ec; text-align: center;">
		<div style="margin: auto; background-color: #39a6ec; width: 990px; height: 512px;">
			<div style="height: 50px;"></div>
			<div style="height: 400px;">
				<div style="float: left; position: relative; width: 560px; height: 400px; background-image: url(/framework/reg/skreg60.jpg); background-repeat: no-repeat;">
					<div class="nav" style="position: absolute; top: 270px; left: 80px;">
						<a href="/2013/?p=FjzkGg&amp;a=sksm">考生须知</a>
					</div>
					<div class="nav" style="position: absolute; top: 270px; left: 280px;">
						<a href="/2013/fjzk_gg.php?n=skbklc">报名流程</a>
					</div>
				</div>
				<div style="float: right; background-color: #e1f2fc; width: 400px; height: 400px;">
					<!--login-->
					<div style="width: 400px; border: #838383 0px solid; border-top: #838383 0px solid;">
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody><tr align="center">
								<td width="50%" id="s1" class="spanin" style="border-right: #39a6eb 2px solid;" onclick="javascript:changeMc('s1')">身份证登录</td>
								<td width="50%" id="s2" class="spanif" onclick="javascript:changeMc('s2')">用户名登录</td>
							</tr>
						</tbody></table>
						<table width="100%" border="0" cellspacing="0" cellpadding="0">
							<tbody><tr align="center">
								<td>&nbsp;<br>
								<br></td>
							</tr>
						</tbody></table>
						<input type="hidden" id="span_s1" name="span_s1" value="身份证:" style=""> <input type="hidden" id="span_s2" name="span_s2" value="用户名:" style=""> <input type="hidden" id="from_s1" name="from_s1" value="zjh" style=""> <input type="hidden" id="from_s2" name="from_s2" value="yhm" style=""> <input type="hidden" id="t" name="t" value="skyhdl" style=""> <input type="hidden" id="yhdllb" name="yhdllb" value="zjh" style="">
						<div id="LoginFormTip"></div>
						<div>
							<table align="center">
								<tbody><tr>
									<td align="right" height="50"><div id="loginname">身份证:</div></td>
									<td align="left"><input class="inp ipt-normal" onfocus="this.className='inp ipt-focus'" onblur="this.className='inp ipt-normal'" style="width: 180px; height: 30px;" id="myname" name="myname" type="text" maxlength="25" autocomplete="off"></td>
									<td align="left"><span></span></td>
								</tr>
								<tr>
									<td align="right" height="50">密　码:</td>
									<td align="left"><input class="inp ipt-normal" onfocus="this.className='inp ipt-focus'" onblur="this.className='inp ipt-normal'" style="width: 180px; height: 30px;" id="mypwd" name="mypwd" type="password" autocomplete="off"></td>
									<td align="left"><span></span></td>
								</tr>
								<tr>
									<td align="right" height="50">验证码:</td>
									<td align="left" valign="top"><input class="inp ipt-normal" onfocus="this.className='inp ipt-focus'" onblur="this.className='inp ipt-normal'" style="width: 60px; height: 30px;" id="verifycode" name="verifycode" type="text" maxlength="6" autocomplete="off">&nbsp;&nbsp;<img id="seccodeimg" style="cursor: pointer" onclick="this.src='/zk/action/verify/?p=FIXEDB78E1UVERIFY&amp;id='+Math.random()" src="/zk/action/verify/?p=FIXEDB78E1UVERIFY" align="absmiddle"></td>
									<td align="left">&nbsp;</td>
								</tr>
								<tr>
									<td align="right" colspan="2"><button type="button" id="loginBtn" class="loginBtn" onclick="ready('');">登录</button></td>
									<td align="left">&nbsp;</td>
								</tr>
							</tbody></table>
						</div>
						<div class="regs">
							<table width="100%">
								<tbody><tr>
									<td>新生首次报考请先注册用户;已注册的在籍考生直接登陆<br>（转考区登陆后选择新生模块报考）。
									</td>
								</tr>
							</tbody></table>
							<a href="./?p=RE3C5O8V9E1R7P1W0D9S4K">?忘记密码</a> | 没有在线账号？<a href="./?p=7R5E1G0I1S4T8ECRCS1K">注册用户</a>
						</div>
					</div>
					<!--login end-->
				</div>
			</div>
		</div>
	</div>
	<!--bottom-->
	<div style="background-color: #ffffff; text-align: center;">
		<div style="margin: auto; background-color: #ffffff; width: 990px; height: 70px; overflow: hidden; margin-top:10px; line-height: 25px; font-family: Arial;">
			政策问题请直接咨询各地区自考办( <a href="/2013/?p=FjzkGg&amp;a=txl">点击查看电话</a> ）　报名期间技术支持电话：0591-87883785 87881896<br>
			© 2024 福建省高等教育自学考试考务考籍管理信息系统　主办：福建省教育考试院　技术支持与维护：福建信息职业技术学院
		</div>
	</div>


</body></html>