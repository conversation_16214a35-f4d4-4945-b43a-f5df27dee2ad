#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码识别模块
基于ddddocr实现验证码自动识别
"""

import time
from pathlib import Path
from typing import Optional, Tuple
from playwright.sync_api import Page, Locator

try:
    import ddddocr
    DDDDOCR_AVAILABLE = True
except ImportError:
    DDDDOCR_AVAILABLE = False

from config import CONFIG, SELECTORS
from modules.logger import log_info, log_success, log_error, log_warning


class CaptchaRecognizer:
    """验证码识别器"""
    
    def __init__(self):
        """初始化验证码识别器"""
        self.ocr = None
        self.debug_dir = Path(CONFIG["captcha_save_path"])
        self.debug_dir.mkdir(parents=True, exist_ok=True)
        
        if DDDDOCR_AVAILABLE:
            try:
                # 尝试新版本初始化方式
                try:
                    self.ocr = ddddocr.DdddOcr(show_ad=False)
                except TypeError:
                    # 新版本不支持show_ad参数
                    self.ocr = ddddocr.DdddOcr()
                log_success("验证码识别器初始化成功", "验证码")
            except Exception as e:
                log_error("验证码", e)
                self.ocr = None
        else:
            log_warning("ddddocr未安装，验证码识别功能不可用", "验证码")
    
    def find_captcha_elements(self, page: Page) -> Tuple[Optional[Locator], Optional[Locator]]:
        """
        查找验证码相关元素
        
        Args:
            page: 页面对象
            
        Returns:
            (验证码图片元素, 验证码输入框元素)
        """
        log_info("开始查找验证码元素", "验证码")
        
        # 等待页面稳定
        time.sleep(2)
        
        # 查找验证码图片
        captcha_image = None
        for selector in SELECTORS["captcha_image"]:
            try:
                elements = page.locator(selector)
                if elements.count() > 0:
                    captcha_image = elements.first
                    log_success(f"找到验证码图片: {selector}", "验证码")
                    break
            except Exception as e:
                log_warning(f"选择器 {selector} 查找失败: {e}", "验证码")
                continue
        
        # 查找验证码输入框
        captcha_input = None
        for selector in SELECTORS["captcha_input"]:
            try:
                elements = page.locator(selector)
                if elements.count() > 0:
                    captcha_input = elements.first
                    log_success(f"找到验证码输入框: {selector}", "验证码")
                    break
            except Exception as e:
                log_warning(f"选择器 {selector} 查找失败: {e}", "验证码")
                continue
        
        if not captcha_image:
            log_warning("未找到验证码图片元素", "验证码")
        
        if not captcha_input:
            log_warning("未找到验证码输入框元素", "验证码")
        
        return captcha_image, captcha_input
    
    def capture_captcha_image(self, page: Page, captcha_image: Locator) -> Optional[bytes]:
        """
        截取验证码图片
        
        Args:
            page: 页面对象
            captcha_image: 验证码图片元素
            
        Returns:
            图片字节数据
        """
        try:
            log_info("开始截取验证码图片", "验证码")
            
            # 滚动到验证码位置
            captcha_image.scroll_into_view_if_needed()
            time.sleep(1)
            
            # 截取验证码图片
            image_bytes = captcha_image.screenshot()
            
            # 保存调试图片
            if CONFIG["captcha_debug"]:
                timestamp = int(time.time())
                debug_path = self.debug_dir / f"captcha_{timestamp}.png"
                with open(debug_path, 'wb') as f:
                    f.write(image_bytes)
                log_info(f"验证码调试图片已保存: {debug_path}", "验证码")
            
            log_success("验证码图片截取成功", "验证码")
            return image_bytes
            
        except Exception as e:
            log_error("验证码", e)
            return None
    
    def recognize_captcha(self, image_bytes: bytes) -> Optional[str]:
        """
        识别验证码

        Args:
            image_bytes: 图片字节数据

        Returns:
            识别结果
        """
        if not self.ocr:
            log_warning("验证码识别器未初始化", "验证码")
            return None

        try:
            log_info("开始识别验证码", "验证码")

            # 修复PIL版本兼容性问题
            try:
                # 使用ddddocr识别
                result = self.ocr.classification(image_bytes)
            except AttributeError as e:
                if "ANTIALIAS" in str(e):
                    # PIL版本兼容性问题，尝试修复
                    import PIL.Image
                    if not hasattr(PIL.Image, 'ANTIALIAS'):
                        PIL.Image.ANTIALIAS = PIL.Image.LANCZOS
                    result = self.ocr.classification(image_bytes)
                else:
                    raise e

            # 清理识别结果
            result = str(result).strip()

            if result:
                log_success(f"验证码识别成功: {result}", "验证码")
                return result
            else:
                log_warning("验证码识别结果为空", "验证码")
                return None

        except Exception as e:
            log_error("验证码", e)
            return None
    
    def input_captcha(self, page: Page, captcha_input: Locator, captcha_text: str) -> bool:
        """
        输入验证码
        
        Args:
            page: 页面对象
            captcha_input: 验证码输入框
            captcha_text: 验证码文本
            
        Returns:
            是否成功
        """
        try:
            log_info(f"开始输入验证码: {captcha_text}", "验证码")
            
            # 清空输入框
            captcha_input.clear()
            time.sleep(0.5)
            
            # 输入验证码
            captcha_input.fill(captcha_text)
            time.sleep(0.5)
            
            # 验证输入
            input_value = captcha_input.input_value()
            if input_value == captcha_text:
                log_success("验证码输入成功", "验证码")
                return True
            else:
                log_warning(f"验证码输入验证失败: 期望{captcha_text}, 实际{input_value}", "验证码")
                return False
                
        except Exception as e:
            log_error("验证码", e)
            return False
    
    def process_captcha(self, page: Page) -> bool:
        """
        处理验证码的完整流程
        
        Args:
            page: 页面对象
            
        Returns:
            是否成功
        """
        try:
            log_info("开始验证码处理流程", "验证码")
            
            # 查找验证码元素
            captcha_image, captcha_input = self.find_captcha_elements(page)
            
            if not captcha_image or not captcha_input:
                log_warning("验证码元素查找失败", "验证码")
                return False
            
            # 截取验证码图片
            image_bytes = self.capture_captcha_image(page, captcha_image)
            if not image_bytes:
                log_warning("验证码图片截取失败", "验证码")
                return False
            
            # 识别验证码
            captcha_text = self.recognize_captcha(image_bytes)
            if not captcha_text:
                log_warning("验证码识别失败", "验证码")
                return False
            
            # 输入验证码
            if not self.input_captcha(page, captcha_input, captcha_text):
                log_warning("验证码输入失败", "验证码")
                return False
            
            log_success("验证码处理流程完成", "验证码")
            return True
            
        except Exception as e:
            log_error("验证码", e)
            return False
    
    def refresh_captcha(self, page: Page) -> bool:
        """
        刷新验证码
        
        Args:
            page: 页面对象
            
        Returns:
            是否成功
        """
        try:
            log_info("尝试刷新验证码", "验证码")
            
            # 查找验证码图片
            captcha_image, _ = self.find_captcha_elements(page)
            
            if captcha_image:
                # 点击验证码图片刷新
                captcha_image.click()
                time.sleep(2)
                log_success("验证码刷新成功", "验证码")
                return True
            else:
                log_warning("未找到验证码图片，无法刷新", "验证码")
                return False
                
        except Exception as e:
            log_error("验证码", e)
            return False
    
    def handle_captcha_with_retry(self, page: Page, max_retries: int = 3) -> bool:
        """
        带重试的验证码处理

        Args:
            page: 页面对象
            max_retries: 最大重试次数

        Returns:
            是否成功
        """
        for attempt in range(max_retries):
            log_info(f"验证码处理尝试 {attempt + 1}/{max_retries}", "验证码")

            if self.process_captcha(page):
                return True

            if attempt < max_retries - 1:
                log_info("验证码处理失败，尝试刷新验证码", "验证码")
                self.refresh_captcha(page)
                time.sleep(2)

        # 如果自动识别失败，提供手动输入选项
        log_warning(f"验证码自动识别失败，已重试 {max_retries} 次", "验证码")

        # 尝试手动输入验证码
        return self._manual_captcha_input(page)

    def _manual_captcha_input(self, page: Page) -> bool:
        """
        手动输入验证码

        Args:
            page: 页面对象

        Returns:
            是否成功
        """
        try:
            log_info("启动手动验证码输入模式", "验证码")

            # 查找验证码元素
            captcha_image, captcha_input = self.find_captcha_elements(page)

            if not captcha_image or not captcha_input:
                log_warning("未找到验证码元素，无法手动输入", "验证码")
                return False

            # 截取验证码图片
            image_bytes = self.capture_captcha_image(page, captcha_image)
            if image_bytes:
                # 保存验证码图片供用户查看
                timestamp = int(time.time())
                manual_captcha_path = self.debug_dir / f"manual_captcha_{timestamp}.png"
                with open(manual_captcha_path, 'wb') as f:
                    f.write(image_bytes)

                print(f"\n⚠️  验证码自动识别失败")
                print(f"📸 验证码图片已保存: {manual_captcha_path}")
                print(f"🖼️  请在文件管理器中打开该图片查看验证码")

                # 等待用户输入
                captcha_code = input(f"请输入验证码 (直接回车跳过): ").strip()

                if captcha_code:
                    return self.input_captcha(page, captcha_input, captcha_code)
                else:
                    log_info("用户跳过手动验证码输入", "验证码")
                    return False
            else:
                log_warning("无法截取验证码图片", "验证码")
                return False

        except Exception as e:
            log_error("验证码", e)
            return False


# 便捷函数
def handle_captcha(page: Page, max_retries: int = 3) -> bool:
    """
    处理验证码的便捷函数
    
    Args:
        page: 页面对象
        max_retries: 最大重试次数
        
    Returns:
        是否成功
    """
    recognizer = CaptchaRecognizer()
    return recognizer.handle_captcha_with_retry(page, max_retries)


def check_captcha_availability() -> bool:
    """
    检查验证码识别功能是否可用
    
    Returns:
        是否可用
    """
    return DDDDOCR_AVAILABLE and CaptchaRecognizer().ocr is not None
