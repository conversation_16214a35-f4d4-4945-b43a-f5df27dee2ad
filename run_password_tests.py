#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密码修改功能测试运行器
提供多种测试模式的统一入口
"""

import sys
import subprocess
from pathlib import Path


def show_menu():
    """显示测试菜单"""
    print("🎯 条件性密码修改功能测试程序")
    print("=" * 50)
    print("请选择测试模式：")
    print()
    print("1. 🚀 快速测试 (测试前3名学员)")
    print("   - 验证密码修改检测功能")
    print("   - 验证自动密码修改处理")
    print("   - 快速验证功能是否正常")
    print()
    print("2. 🔍 交互式快速测试")
    print("   - 可自定义测试学员数量")
    print("   - 实时查看测试过程")
    print("   - 适合功能调试")
    print()
    print("3. 📊 完整批量测试")
    print("   - 测试所有学员")
    print("   - 生成详细统计报告")
    print("   - 包含截图功能")
    print()
    print("4. 🛠️ 自定义测试")
    print("   - 指定数据文件")
    print("   - 自定义测试参数")
    print()
    print("0. ❌ 退出")
    print("=" * 50)


def run_quick_test():
    """运行快速测试"""
    print("\n🚀 启动快速测试...")
    cmd = [sys.executable, "tests/quick_password_test.py", "--count", "3"]
    return subprocess.run(cmd, cwd=Path.cwd()).returncode == 0


def run_interactive_test():
    """运行交互式测试"""
    print("\n🔍 启动交互式测试...")
    cmd = [sys.executable, "tests/quick_password_test.py", "--interactive"]
    return subprocess.run(cmd, cwd=Path.cwd()).returncode == 0


def run_full_test():
    """运行完整批量测试"""
    print("\n📊 启动完整批量测试...")
    cmd = [sys.executable, "tests/test_conditional_password_change.py"]
    return subprocess.run(cmd, cwd=Path.cwd()).returncode == 0


def run_custom_test():
    """运行自定义测试"""
    print("\n🛠️ 自定义测试配置")
    print("-" * 30)
    
    # 选择测试类型
    print("选择测试类型：")
    print("1. 快速测试")
    print("2. 完整测试")
    
    while True:
        test_type = input("请选择 (1-2): ").strip()
        if test_type in ['1', '2']:
            break
        print("❌ 请输入1或2")
    
    # 数据文件
    data_file = input("数据文件路径 (默认: data/students.csv): ").strip()
    if not data_file:
        data_file = "data/students.csv"
    
    # 检查文件是否存在
    if not Path(data_file).exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return False
    
    # 其他选项
    headless = input("使用无头模式？(y/N): ").strip().lower() in ['y', 'yes']
    
    # 构建命令
    if test_type == '1':
        # 快速测试
        count = input("测试学员数量 (1-10, 默认3): ").strip()
        if not count:
            count = "3"
        
        try:
            count = int(count)
            if not 1 <= count <= 10:
                print("❌ 数量必须在1-10之间")
                return False
        except ValueError:
            print("❌ 请输入有效数字")
            return False
        
        cmd = [sys.executable, "tests/quick_password_test.py", "--count", str(count), "--data", data_file]
        if headless:
            cmd.append("--headless")
    else:
        # 完整测试
        cmd = [sys.executable, "tests/test_conditional_password_change.py", "--data", data_file]
        if headless:
            cmd.append("--headless")
    
    print(f"\n🚀 启动自定义测试...")
    print(f"命令: {' '.join(cmd)}")
    
    return subprocess.run(cmd, cwd=Path.cwd()).returncode == 0


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查必要文件
    required_files = [
        "tests/quick_password_test.py",
        "tests/test_conditional_password_change.py",
        "data/students.csv",
        "src/modules",
        "config.yaml"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ 环境检查通过")
    return True


def main():
    """主函数"""
    try:
        # 检查环境
        if not check_environment():
            print("\n❌ 环境检查失败，请确保所有必要文件存在")
            return
        
        while True:
            show_menu()
            
            choice = input("请选择 (0-4): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                success = run_quick_test()
                if success:
                    print("\n✅ 快速测试完成")
                else:
                    print("\n❌ 快速测试失败")
            elif choice == '2':
                success = run_interactive_test()
                if success:
                    print("\n✅ 交互式测试完成")
                else:
                    print("\n❌ 交互式测试失败")
            elif choice == '3':
                success = run_full_test()
                if success:
                    print("\n✅ 完整测试完成")
                else:
                    print("\n❌ 完整测试失败")
            elif choice == '4':
                success = run_custom_test()
                if success:
                    print("\n✅ 自定义测试完成")
                else:
                    print("\n❌ 自定义测试失败")
            else:
                print("❌ 无效选择，请重新输入")
                continue
            
            # 询问是否继续
            if choice != '0':
                continue_test = input("\n是否继续其他测试？(y/N): ").strip().lower()
                if continue_test not in ['y', 'yes']:
                    print("👋 再见！")
                    break
    
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 系统异常: {e}")


if __name__ == "__main__":
    main()
