# 自考成绩查询自动化系统

## 📋 项目简介

本系统是一个基于Playwright的自动化工具，用于批量查询福建省自考成绩并自动截图保存。系统采用模块化设计，具备完善的错误处理和日志记录功能。

## ✨ 主要功能

- 🔐 **自动登录**: 支持身份证号登录，自动识别验证码
- 🧭 **智能导航**: 自动找到"当次成绩查询"菜单并智能选择查询选项
- 📸 **精确截图**: 智能定位成绩单表格，保存为高质量PNG图片
- 📊 **批量处理**: 支持批量处理多名学员，显示实时进度
- 📝 **完善日志**: 详细的操作日志和处理统计报告
- 🛡️ **错误处理**: 完善的异常处理和重试机制

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd exam-score-query

# 安装依赖
pip install -r requirements.txt

# 安装浏览器
playwright install chromium
```

### 2. 准备数据文件

编辑 `data/students.csv` 文件，格式如下：

```csv
姓名,身份证号,密码
张三,350122199510201430,password123
李四,350425198804062414,password456
王五,350104198903055449,password789
```

### 3. 运行系统

```bash
# 进入源码目录
cd src

# 基本运行
python main.py

# 指定数据文件
python main.py --data ../data/students.csv

# 无头模式运行
python main.py --headless

# 调试模式
python main.py --debug
```

## 📁 项目结构

```
exam-score-query/
├── src/                   # 源代码目录
│   ├── main.py           # 主程序入口
│   ├── config.py         # 配置文件
│   ├── check_setup.py    # 系统检查脚本
│   └── modules/          # 核心模块
│       ├── data.py       # 数据处理模块
│       ├── login.py      # 登录模块
│       ├── navigation.py # 导航模块
│       ├── screenshot.py # 截图模块
│       ├── captcha.py    # 验证码识别模块
│       ├── browser.py    # 浏览器管理模块
│       └── logger.py     # 日志系统模块
├── data/                 # 数据文件目录
│   └── students.csv      # 学员数据文件
├── output/               # 输出目录
│   ├── screenshots/      # 截图保存目录
│   └── logs/            # 日志文件目录
├── tests/               # 测试目录
├── docs/                # 文档目录
├── requirements.txt     # 项目依赖
├── setup.py            # 安装配置
└── README.md           # 项目说明
```

## ⚙️ 配置说明

主要配置项在 `src/config.py` 中：

```python
CONFIG = {
    "login_url": "https://121.204.170.198:8082/zk/online/2/",
    "headless": False,          # 是否无头模式
    "slow_mo": 500,            # 操作间隔(毫秒)
    "page_timeout": 30000,     # 页面超时(毫秒)
    "screenshot_dir": Path("../output/screenshots"),
    "log_dir": Path("../output/logs"),
    # ... 更多配置
}
```

## 📊 处理流程

1. **数据加载**: 读取学员CSV文件，验证数据格式
2. **批量处理**: 遍历所有学员，执行以下步骤：
   - 自动登录福建省自考系统
   - 导航到"当次成绩查询"页面
   - 智能选择查询选项
   - 截图成绩单并保存为"学员姓名.png"
3. **结果统计**: 生成处理报告，包含成功率、失败详情等

## 🔧 故障排除

### 常见问题

1. **验证码识别失败**
   - 系统会自动重试3次
   - 检查网络连接是否稳定

2. **页面加载超时**
   - 增加 `page_timeout` 配置值
   - 检查目标网站是否可访问

3. **截图失败**
   - 系统会使用多种备选策略
   - 检查截图目录权限

### 日志查看

- **主日志**: `output/logs/auto_query.log`
- **错误日志**: `output/logs/errors.log`
- **统计日志**: `output/logs/statistics.log`
- **会话报告**: `output/logs/report_session_*.json`

## 🛡️ 安全说明

- 密码信息仅在内存中处理，不会明文保存到日志
- 建议在安全的网络环境中运行
- 定期清理敏感的日志文件

## 📄 更新日志

### v1.0.0
- 完整的自动化流程实现
- 模块化架构设计
- 完善的日志和错误处理
- 批量处理和进度显示
- 高质量截图功能
- 标准化项目结构

---

**注意**: 本系统仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。
