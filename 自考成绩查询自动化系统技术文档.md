# 自考成绩查询自动化系统技术文档

## 目录

- [1. 系统概述](#1-系统概述)
- [2. 环境配置和依赖安装](#2-环境配置和依赖安装)
- [3. 项目结构说明](#3-项目结构说明)
- [4. 核心功能实现逻辑](#4-核心功能实现逻辑)
- [5. 配置系统详解](#5-配置系统详解)
- [6. 使用指南](#6-使用指南)
- [7. 故障排除](#7-故障排除)
- [8. 维护和更新](#8-维护和更新)

---

## 1. 系统概述

### 1.1 系统功能简介

自考成绩查询自动化系统是一个基于Python和Playwright的Web自动化工具，专门用于批量查询福建省高等教育自学考试成绩。系统能够自动完成登录、导航、截图等操作，大大提高成绩查询的效率。

### 1.2 主要特性

- **🤖 全自动化流程**：从登录到截图保存的完整自动化
- **🧠 智能验证码识别**：基于深度学习的验证码自动识别
- **📊 批量处理能力**：支持多学员连续处理
- **🔄 中断恢复机制**：支持中断后从上次停止位置继续
- **📸 精确截图定位**：智能表格识别和精确截图
- **⚙️ 灵活配置系统**：支持多种配置方式和自定义选择器
- **📝 详细日志记录**：完整的操作日志和错误追踪
- **🛡️ 错误恢复机制**：自动重试和异常处理

### 1.3 系统要求

#### 操作系统支持
- **Windows**: Windows 10/11 (推荐)
- **macOS**: macOS 10.14+ 
- **Linux**: Ubuntu 18.04+, CentOS 7+

#### Python版本要求
- **Python 3.8+** (推荐 Python 3.9-3.11)
- **pip 21.0+**

#### 硬件要求
- **内存**: 最低 4GB RAM (推荐 8GB+)
- **存储**: 最低 2GB 可用空间
- **网络**: 稳定的互联网连接

### 1.4 系统架构

```mermaid
graph TB
    A[主程序 main.py] --> B[数据加载模块]
    A --> C[自动查询管理器]
    
    C --> D[登录模块]
    C --> E[导航模块] 
    C --> F[截图模块]
    C --> G[进度管理模块]
    
    D --> H[验证码识别]
    E --> I[智能选择器]
    F --> J[表格定位算法]
    G --> K[状态持久化]
    
    L[配置系统] --> M[YAML配置]
    L --> N[JSON配置]
    L --> O[默认配置]
    
    P[日志系统] --> Q[操作日志]
    P --> R[错误日志]
    P --> S[统计日志]
```

### 1.5 核心组件说明

| 组件 | 功能描述 | 关键文件 |
|------|----------|----------|
| **配置管理** | 多层级配置系统，支持YAML/JSON/环境变量 | `config/`, `config.yaml` |
| **登录模块** | 自动登录、验证码识别、会话管理 | `modules/login.py` |
| **导航模块** | 智能菜单查找、页面导航、选项选择 | `modules/navigation.py` |
| **截图模块** | 表格定位、精确截图、文件保存 | `modules/screenshot.py` |
| **进度管理** | 状态跟踪、中断恢复、批量处理 | `modules/progress.py` |
| **日志系统** | 多级日志、错误追踪、统计报告 | `modules/logger.py` |

---

## 2. 环境配置和依赖安装

### 2.1 Python环境配置

#### 2.1.1 Windows环境

```bash
# 1. 下载并安装Python 3.9+
# 从 https://python.org 下载官方安装包

# 2. 验证安装
python --version
pip --version

# 3. 升级pip到最新版本
python -m pip install --upgrade pip
```

#### 2.1.2 macOS环境

```bash
# 使用Homebrew安装Python
brew install python@3.9

# 或使用pyenv管理Python版本
brew install pyenv
pyenv install 3.9.16
pyenv global 3.9.16
```

#### 2.1.3 Linux环境

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.9 python3.9-pip python3.9-venv

# CentOS/RHEL
sudo yum install python39 python39-pip
```

### 2.2 项目依赖安装

#### 2.2.1 核心依赖列表

```txt
# requirements.txt
playwright==1.40.0
pyyaml==6.0.1
pillow==10.1.0
opencv-python==********
numpy==1.24.3
loguru==0.7.2
pathlib2==2.3.7
typing-extensions==4.8.0
```

#### 2.2.2 分步安装指南

```bash
# 1. 克隆项目（如果从Git获取）
git clone <repository-url>
cd 自考成绩查询2

# 2. 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 3. 安装核心依赖
pip install playwright==1.40.0
pip install pyyaml==6.0.1
pip install pillow==10.1.0
pip install opencv-python==********
pip install numpy==1.24.3
pip install loguru==0.7.2

# 4. 或者批量安装（如果有requirements.txt）
pip install -r requirements.txt
```

#### 2.2.3 Playwright浏览器驱动安装

```bash
# 安装Playwright浏览器驱动
playwright install

# 或者只安装Chromium（推荐）
playwright install chromium

# 验证安装
playwright --version
```

### 2.3 验证码识别模型

系统使用预训练的ONNX模型进行验证码识别：

```bash
# 确保模型文件存在
ls models/
# 应该包含: captcha_model.onnx

# 如果缺少模型文件，请联系开发者获取
```

### 2.4 常见安装问题和解决方案

#### 2.4.1 Playwright安装失败

```bash
# 问题：网络连接超时
# 解决方案：使用国内镜像
pip install playwright -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 问题：权限不足
# 解决方案：使用管理员权限
sudo pip install playwright  # Linux/macOS
# 或在Windows中以管理员身份运行命令提示符
```

#### 2.4.2 OpenCV安装问题

```bash
# 问题：OpenCV依赖缺失
# Ubuntu/Debian解决方案：
sudo apt install libgl1-mesa-glx libglib2.0-0

# CentOS/RHEL解决方案：
sudo yum install mesa-libGL glib2
```

#### 2.4.3 Python版本兼容性

```bash
# 检查Python版本
python --version

# 如果版本过低，升级Python
# Windows: 重新下载安装最新版本
# macOS: brew upgrade python
# Linux: 使用包管理器升级
```

---

## 3. 项目结构说明

### 3.1 完整目录结构

```
自考成绩查询2/
├── src/                          # 源代码目录
│   ├── main.py                   # 主程序入口
│   ├── config/                   # 配置模块
│   │   ├── __init__.py          # 配置系统初始化
│   │   └── defaults.py          # 默认配置定义
│   └── modules/                  # 功能模块
│       ├── __init__.py
│       ├── auto_query.py        # 自动查询管理器
│       ├── data.py              # 数据处理模块
│       ├── login.py             # 登录功能模块
│       ├── navigation.py        # 导航功能模块
│       ├── screenshot.py        # 截图功能模块
│       ├── progress.py          # 进度管理模块
│       ├── logger.py            # 日志系统模块
│       └── captcha/             # 验证码识别模块
│           ├── __init__.py
│           └── recognizer.py    # 验证码识别器
├── data/                         # 数据文件目录
│   └── students.csv             # 学员数据文件
├── output/                       # 输出文件目录
│   ├── screenshots/             # 截图保存目录
│   └── logs/                    # 日志文件目录
│       ├── captcha_debug/       # 验证码调试图片
│       ├── auto_query.log       # 主要操作日志
│       ├── errors.log           # 错误日志
│       └── statistics.log       # 统计日志
├── models/                       # 模型文件目录
│   └── captcha_model.onnx       # 验证码识别模型
├── config.yaml                  # YAML配置文件（主配置）
├── config.json                  # JSON配置文件（备用配置）
├── progress.json                # 进度状态文件
├── requirements.txt             # Python依赖列表
└── README.md                    # 项目说明文档
```

### 3.2 核心文件功能说明

#### 3.2.1 主程序文件

| 文件 | 功能描述 |
|------|----------|
| `src/main.py` | 程序主入口，负责初始化系统、加载配置、启动批量处理流程 |

#### 3.2.2 配置系统文件

| 文件 | 优先级 | 功能描述 |
|------|--------|----------|
| `config.yaml` | **最高** | 主配置文件，YAML格式，易于编辑和维护 |
| `config.json` | 中等 | 备用配置文件，JSON格式，程序化配置 |
| `src/config/defaults.py` | 最低 | 默认配置，代码中定义，作为兜底配置 |

#### 3.2.3 功能模块文件

| 模块 | 文件 | 核心功能 |
|------|------|----------|
| **自动查询** | `modules/auto_query.py` | 批量处理协调、流程控制、资源管理 |
| **数据处理** | `modules/data.py` | 学员数据加载、CSV文件解析、数据验证 |
| **登录功能** | `modules/login.py` | 用户认证、验证码处理、会话管理 |
| **导航功能** | `modules/navigation.py` | 菜单查找、页面跳转、智能选择 |
| **截图功能** | `modules/screenshot.py` | 表格定位、截图生成、文件保存 |
| **进度管理** | `modules/progress.py` | 状态跟踪、中断恢复、批量控制 |
| **日志系统** | `modules/logger.py` | 日志记录、错误追踪、统计报告 |
| **验证码识别** | `modules/captcha/recognizer.py` | 图像处理、模型推理、结果输出 |

#### 3.2.4 数据和输出文件

| 目录/文件 | 用途 | 格式要求 |
|-----------|------|----------|
| `data/students.csv` | 学员信息存储 | CSV格式，包含姓名和身份证号 |
| `output/screenshots/` | 成绩截图保存 | PNG格式，以学员姓名命名 |
| `output/logs/` | 日志文件存储 | 文本格式，按日期和类型分类 |
| `progress.json` | 处理进度记录 | JSON格式，支持中断恢复 |

### 3.3 配置文件优先级说明

系统采用三层配置架构，优先级从高到低：

```mermaid
graph TD
    A[环境变量] --> B[config.yaml]
    B --> C[config.json]
    C --> D[defaults.py]
    
    E[最终配置] --> A
    E --> B
    E --> C
    E --> D
```

1. **环境变量** (最高优先级)
   - 格式：`EXAM_*` 前缀
   - 示例：`EXAM_HEADLESS=true`

2. **config.yaml** (高优先级)
   - 主要配置文件
   - 人工编辑友好
   - 支持注释

3. **config.json** (中等优先级)
   - 备用配置文件
   - 程序化配置
   - 严格JSON格式

4. **defaults.py** (最低优先级)
   - 代码中的默认值
   - 兜底配置
   - 确保系统可运行

---

## 4. 核心功能实现逻辑

### 4.1 登录模块

#### 4.1.1 登录流程图

```mermaid
sequenceDiagram
    participant M as 主程序
    participant L as 登录模块
    participant C as 验证码识别
    participant B as 浏览器

    M->>L: 开始登录(学员信息)
    L->>B: 导航到登录页面
    L->>B: 填写用户名
    L->>B: 填写密码
    L->>C: 获取验证码图片
    C->>C: 图像预处理
    C->>C: 模型推理
    C-->>L: 返回识别结果
    L->>B: 输入验证码
    L->>B: 点击登录按钮
    B-->>L: 登录结果
    alt 登录成功
        L-->>M: 返回成功
    else 登录失败
        L->>L: 重试逻辑(最多3次)
        L-->>M: 返回失败
    end
```

#### 4.1.2 验证码识别流程

```python
# 验证码识别核心逻辑
class CaptchaRecognizer:
    def recognize(self, image_path: str) -> str:
        # 1. 图像预处理
        image = self.preprocess_image(image_path)

        # 2. 模型推理
        result = self.model.predict(image)

        # 3. 后处理
        text = self.postprocess_result(result)

        return text

    def preprocess_image(self, image_path: str):
        """图像预处理：灰度化、降噪、标准化"""
        image = cv2.imread(image_path)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        denoised = cv2.fastNlMeansDenoising(gray)
        normalized = denoised / 255.0
        return normalized.reshape(1, -1)
```

#### 4.1.3 登录重试机制

```python
# 登录重试逻辑
async def login_with_retry(self, student_info: dict) -> bool:
    max_retries = self.config.get('max_retries', 3)

    for attempt in range(1, max_retries + 1):
        try:
            log_info(f"登录尝试 {attempt}/{max_retries}")

            # 执行登录步骤
            success = await self.perform_login(student_info)

            if success:
                log_success("登录成功")
                return True

        except Exception as e:
            log_error(f"登录尝试 {attempt} 失败: {e}")

        # 失败后重新加载页面
        if attempt < max_retries:
            await self.page.reload()
            await asyncio.sleep(2)

    log_error("登录失败，已达到最大重试次数")
    return False
```

### 4.2 导航模块

#### 4.2.1 智能选择器系统

系统使用多层级选择器策略，确保在不同页面结构下都能正确定位元素：

```python
# 选择器配置示例
SELECTORS = {
    "score_query_menu": [
        "text=当次成绩查询",                    # Playwright原生文本选择器
        "//a[contains(text(), '当次成绩查询')]",   # XPath选择器
        "//td[contains(text(), '当次成绩查询')]",  # 表格单元格选择器
        "//span[contains(text(), '当次成绩查询')]", # Span元素选择器
        "[title*='当次成绩查询']",               # 属性选择器
        "[alt*='当次成绩查询']"                  # Alt属性选择器
    ]
}
```

#### 4.2.2 智能选择策略

```python
# 智能选择逻辑
def smart_select_option(self, options: list) -> int:
    """根据选项数量智能选择策略"""
    option_count = len(options)

    if option_count == 0:
        raise NavigationError("未找到任何查询选项")
    elif option_count == 1:
        log_success("选择策略: 单个选项，选择第一个")
        return 0
    elif option_count == 2:
        log_success("选择策略: 多个选项(2个)，选择第二个")
        return 1
    else:
        # 多个选项时选择中间的
        selected_index = option_count // 2
        log_success(f"选择策略: 多个选项({option_count}个)，选择第{selected_index + 1}个")
        return selected_index
```

#### 4.2.3 页面验证机制

```python
# 页面验证逻辑
async def verify_page(self, expected_indicators: list) -> bool:
    """验证页面是否包含预期的标识"""
    page_content = await self.page.content()

    for indicator in expected_indicators:
        if indicator in page_content:
            log_success(f"页面验证成功，包含标识: {indicator}")
            return True

    log_warning("页面验证失败，未找到预期标识")
    return False
```

### 4.3 截图模块

#### 4.3.1 表格定位算法

```python
# 表格定位评分算法
def score_table(self, table_element) -> int:
    """为表格元素计算相关性评分"""
    score = 0
    table_text = table_element.text_content().lower()

    # 关键词评分
    keywords = {
        '成绩': 20,
        '合格': 15,
        '分数': 10,
        '课程': 10,
        '科目': 8,
        '学分': 5
    }

    for keyword, points in keywords.items():
        if keyword in table_text:
            score += points

    # 表格结构评分
    rows = table_element.query_selector_all('tr')
    if len(rows) >= 2:  # 至少有表头和数据行
        score += 10

    # 列数评分
    if rows:
        cols = len(rows[0].query_selector_all('td, th'))
        if cols >= 3:  # 至少3列
            score += 5

    return score
```

#### 4.3.2 截图质量优化

```python
# 截图质量优化
async def optimize_screenshot_quality(self):
    """优化截图质量设置"""
    # 设置高DPI
    await self.page.set_viewport_size({
        "width": 1920,
        "height": 1080
    })

    # 等待页面完全加载
    await self.page.wait_for_load_state('networkidle')

    # 滚动到顶部确保完整显示
    await self.page.evaluate("window.scrollTo(0, 0)")

    # 短暂等待确保渲染完成
    await asyncio.sleep(1)
```

### 4.4 进度管理模块

#### 4.4.1 状态持久化

```python
# 进度状态数据结构
{
    "session_id": "uuid-string",
    "start_time": "2025-07-24T10:16:31",
    "total_students": 7,
    "completed_students": [
        {
            "id": "350122199510201430",
            "name": "何勇",
            "status": "completed",
            "timestamp": "2025-07-24T10:02:17",
            "error_message": null
        }
    ],
    "failed_students": [],
    "current_index": 3,
    "success_rate": 100.0
}
```

#### 4.4.2 中断恢复逻辑

```python
# 中断恢复实现
def resume_from_progress(self, students: list) -> list:
    """从进度文件恢复处理状态"""
    if not self.progress_file.exists():
        return students

    progress = self.load_progress()
    completed_ids = {s['id'] for s in progress['completed_students']}

    # 过滤已完成的学员
    remaining_students = [
        s for s in students
        if s['id'] not in completed_ids
    ]

    log_info(f"已过滤 {len(completed_ids)} 名已处理学员")
    log_info(f"剩余待处理学员: {len(remaining_students)} 名")

    return remaining_students
```

---

## 5. 配置系统详解

### 5.1 配置文件类型和用途

#### 5.1.1 YAML配置文件 (config.yaml)

**用途**: 主要配置文件，人工编辑友好

```yaml
# 浏览器配置
browser:
  headless: false              # 是否无头模式
  viewport_width: 1280         # 视口宽度
  viewport_height: 720         # 视口高度
  user_agent: "Mozilla/5.0..." # 用户代理

# 超时配置
timeouts:
  page_timeout: 30000          # 页面加载超时(毫秒)
  navigation_timeout: 20000    # 导航超时(毫秒)
  element_timeout: 10000       # 元素查找超时(毫秒)

# 重试配置
retry:
  max_retries: 3               # 最大重试次数
  retry_delay: 2.0             # 重试间隔(秒)
  captcha_retries: 3           # 验证码重试次数

# 目录配置
directories:
  screenshot_dir: "output/screenshots"
  log_dir: "output/logs"
  data_file: "data/students.csv"

# 选择器配置
selectors:
  score_query_menu:
    - "text=当次成绩查询"
    - "//a[contains(text(), '当次成绩查询')]"
    - "//td[contains(text(), '当次成绩查询')]"
    - "//span[contains(text(), '当次成绩查询')]"
    - "[title*='当次成绩查询']"
    - "[alt*='当次成绩查询']"

  query_options:
    - "text=点击进入"
    - "//a[contains(text(), '点击进入')]"
    - "//button[contains(text(), '点击进入')]"
    - "//input[contains(@value, '点击进入')]"
    - "[title*='点击进入']"
    - "[alt*='点击进入']"
```

#### 5.1.2 JSON配置文件 (config.json)

**用途**: 备用配置，程序化配置

```json
{
  "browser": {
    "headless": false,
    "viewport_width": 1280,
    "viewport_height": 720
  },
  "timeouts": {
    "page_timeout": 30000,
    "navigation_timeout": 20000,
    "element_timeout": 10000
  },
  "selectors": {
    "score_query_menu": [
      "text=当次成绩查询",
      "//a[contains(text(), '当次成绩查询')]"
    ]
  }
}
```

#### 5.1.3 环境变量配置

**用途**: 运行时动态配置，CI/CD友好

```bash
# 设置环境变量
export EXAM_HEADLESS=true
export EXAM_MAX_RETRIES=5
export EXAM_LOG_LEVEL=DEBUG
export EXAM_SCREENSHOT_DIR=/custom/path

# Windows PowerShell
$env:EXAM_HEADLESS="true"
$env:EXAM_MAX_RETRIES="5"
```

### 5.2 选择器语法详解

#### 5.2.1 Playwright原生选择器

```yaml
selectors:
  # 文本选择器 - 精确匹配
  - "text=当次成绩查询"

  # 文本选择器 - 部分匹配
  - "text*=成绩查询"

  # 文本选择器 - 正则表达式
  - "text=/成绩.*查询/"
```

#### 5.2.2 XPath选择器

```yaml
selectors:
  # 包含文本的链接
  - "//a[contains(text(), '当次成绩查询')]"

  # 包含文本的任意元素
  - "//*[contains(text(), '当次成绩查询')]"

  # 属性匹配
  - "//input[contains(@value, '点击进入')]"

  # 层级关系
  - "//table//td[contains(text(), '成绩')]"
```

#### 5.2.3 CSS选择器

```yaml
selectors:
  # 属性选择器
  - "[title*='成绩查询']"
  - "[alt*='点击进入']"

  # 类选择器
  - ".score-table"
  - ".query-button"

  # ID选择器
  - "#loginBtn"
  - "#scoreTable"
```

### 5.3 配置优先级和合并规则

#### 5.3.1 配置加载顺序

```python
# 配置加载逻辑
def load_config():
    # 1. 加载默认配置
    config = load_defaults()

    # 2. 合并JSON配置
    if config_json_exists():
        json_config = load_json_config()
        config = merge_config(config, json_config)

    # 3. 合并YAML配置
    if config_yaml_exists():
        yaml_config = load_yaml_config()
        config = merge_config(config, yaml_config)

    # 4. 应用环境变量
    env_config = load_env_config()
    config = merge_config(config, env_config)

    return config
```

#### 5.3.2 配置合并策略

- **字典合并**: 深度合并，子键值会被覆盖
- **列表合并**: 完全替换，不进行追加
- **基本类型**: 直接覆盖

### 5.4 自定义配置示例

#### 5.4.1 添加新的选择器

```yaml
# config.yaml
selectors:
  # 添加自定义选择器
  custom_menu:
    - "text=自定义菜单"
    - "//a[@class='custom-link']"
    - "[data-menu='custom']"

  # 修改现有选择器
  score_query_menu:
    - "text=当次成绩查询"
    - "text=成绩查询入口"  # 新增备选文本
    - "//a[contains(text(), '当次成绩查询')]"
```

#### 5.4.2 调整超时和重试配置

```yaml
# 针对网络较慢的环境
timeouts:
  page_timeout: 60000      # 增加页面超时
  navigation_timeout: 45000 # 增加导航超时
  element_timeout: 20000   # 增加元素查找超时

retry:
  max_retries: 5           # 增加重试次数
  retry_delay: 3.0         # 增加重试间隔
  captcha_retries: 5       # 增加验证码重试
```

---

## 6. 使用指南

### 6.1 学员数据文件准备

#### 6.1.1 CSV文件格式要求

学员数据文件 `data/students.csv` 必须包含以下列：

```csv
姓名,身份证号
何勇,350122199510201430
俞进铖,350425198804062414
陈旭琴,350104198903055449
杨文颖,35042719820622202X
周艺群,350321198908272669
曾世豪,350305200404012978
周君,350182199503312921
```

#### 6.1.2 数据格式要求

| 字段 | 要求 | 示例 |
|------|------|------|
| **姓名** | 2-4个中文字符，与准考证一致 | `张三`, `李小明` |
| **身份证号** | 18位，最后一位可以是X | `350122199510201430` |

#### 6.1.3 文件编码要求

```bash
# 确保CSV文件使用UTF-8编码
# 推荐使用UTF-8 with BOM格式，避免中文乱码

# 检查文件编码（Linux/macOS）
file -I data/students.csv

# 转换编码（如果需要）
iconv -f GBK -t UTF-8 students_gbk.csv > data/students.csv
```

### 6.2 程序启动和运行

#### 6.2.1 基本启动流程

```bash
# 1. 进入项目目录
cd 自考成绩查询2

# 2. 激活虚拟环境（如果使用）
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 3. 启动程序
python src/main.py
```

#### 6.2.2 程序运行界面

```
🎯 自考成绩查询自动化系统
========================================

📋 学员列表 (7 名):
    1. 何勇 (350122199510201430)
    2. 俞进铖 (350425198804062414)
    3. 陈旭琴 (350104198903055449)
    4. 杨文颖 (35042719820622202X)
    5. 周艺群 (350321198908272669)
    6. 曾世豪 (350305200404012978)
    7. 周君 (350182199503312921)

⚠️  即将开始批量处理 7 名学员
   请确保网络连接正常，系统将自动进行以下操作：
   1. 自动登录福建省自考系统
   2. 导航到成绩查询页面
   3. 截图保存成绩单

是否继续？(y/N): y
```

#### 6.2.3 处理过程监控

```
🚀 开始批量处理 7 名学员...
============================================================

🌐 初始化浏览器...
✅ 浏览器初始化成功

📝 处理第 1/7 名学员: 何勇
   身份证号: 350122199510201430
   进度: 14.3% (1/7)

✅ 用户名输入成功: input[name='myname']
✅ 密码输入成功: input[name='mypwd']
✅ 验证码识别成功: tkp8b6
✅ 登录成功
✅ 找到'当次成绩查询'菜单: text=当次成绩查询 (数量: 1)
✅ 完整导航流程成功
✅ 精确表格截图成功: output\screenshots\何勇.png
   ✅ 成功 (耗时: 33.7秒)
```

### 6.3 中断恢复操作

#### 6.3.1 中断场景

程序可能在以下情况下中断：
- 网络连接中断
- 手动停止程序 (Ctrl+C)
- 系统异常或重启
- 验证码识别连续失败

#### 6.3.2 恢复操作步骤

```bash
# 1. 重新启动程序
python src/main.py

# 2. 系统会自动检测进度文件
🔄 发现未完成的处理进度
是否从上次中断的地方继续处理？(y/n): y

# 3. 系统会过滤已处理的学员
📋 正在过滤已处理的学员...
发现已处理的学员: 3 名
剩余待处理学员: 4 名
✅ 已过滤 3 名已处理学员

# 4. 从中断位置继续处理
📝 处理第 1/4 名学员: 杨文颖 (剩余学员中的第1个)
```

#### 6.3.3 重新开始处理

```bash
# 如果选择重新开始
🔄 发现未完成的处理进度
是否从上次中断的地方继续处理？(y/n): n

🔄 将重新开始处理所有学员
⚠️  这将覆盖现有的处理进度
```

### 6.4 输出文件管理

#### 6.4.1 截图文件

```bash
# 截图文件位置
output/screenshots/
├── 何勇.png          # 学员成绩截图
├── 俞进铖.png
├── 陈旭琴.png
└── ...

# 截图文件特点
- 文件名：学员姓名.png
- 格式：PNG格式，高质量
- 内容：成绩表格的精确截图
- 大小：通常50-200KB
```

#### 6.4.2 日志文件

```bash
# 日志文件结构
output/logs/
├── auto_query.log              # 主要操作日志
├── errors.log                  # 错误日志
├── statistics.log              # 统计日志
├── captcha_debug/              # 验证码调试图片
│   ├── captcha_1753323402.png
│   └── captcha_1753323437.png
└── report_session_*.json       # 会话报告
```

#### 6.4.3 进度文件

```json
// progress.json - 进度状态文件
{
  "session_id": "e7f7383a-0196-4cf9-b6c8-abf7010b4c29",
  "start_time": "2025-07-24T10:16:31.024592",
  "total_students": 7,
  "completed_students": [
    {
      "id": "350122199510201430",
      "name": "何勇",
      "status": "completed",
      "timestamp": "2025-07-24T10:02:17.123456",
      "error_message": null
    }
  ],
  "failed_students": [],
  "success_rate": 100.0
}
```

### 6.5 批量处理最佳实践

#### 6.5.1 处理前准备

```bash
# 1. 检查网络连接
ping 121.204.170.198

# 2. 验证学员数据
head -5 data/students.csv

# 3. 清理旧的输出文件（可选）
rm -rf output/screenshots/*.png
rm -f progress.json

# 4. 检查磁盘空间
df -h output/
```

#### 6.5.2 分批处理建议

对于大量学员，建议分批处理：

```bash
# 方法1：分割CSV文件
split -l 20 data/students.csv data/batch_

# 方法2：使用head/tail命令
head -20 data/students.csv > data/batch1.csv
tail -n +21 data/students.csv | head -20 > data/batch2.csv
```

#### 6.5.3 监控和维护

```bash
# 实时监控日志
tail -f output/logs/auto_query.log

# 检查处理进度
ls -la output/screenshots/ | wc -l

# 查看错误日志
tail -20 output/logs/errors.log
```

---

## 7. 故障排除

### 7.1 常见错误类型和解决方案

#### 7.1.1 登录相关错误

**错误**: `登录失败，页面包含错误信息: 失败`

**原因分析**:
- 验证码识别错误
- 网络连接不稳定
- 学员信息不正确

**解决方案**:
```bash
# 1. 检查学员信息
grep "学员姓名" data/students.csv

# 2. 检查验证码识别日志
grep "验证码识别" output/logs/auto_query.log

# 3. 手动验证登录
# 在浏览器中手动尝试登录该学员账号

# 4. 调整配置增加重试次数
# config.yaml
retry:
  max_retries: 5
  captcha_retries: 5
```

**错误**: `浏览器初始化失败`

**解决方案**:
```bash
# 1. 重新安装Playwright浏览器
playwright install chromium

# 2. 检查系统权限
# Windows: 以管理员身份运行
# Linux: 检查用户权限

# 3. 检查防火墙设置
# 确保允许Python和Chromium网络访问
```

#### 7.1.2 导航相关错误

**错误**: `未找到'当次成绩查询'菜单`

**原因分析**:
- 页面结构发生变化
- 选择器配置过时
- 页面加载不完整

**解决方案**:
```yaml
# 1. 更新选择器配置 (config.yaml)
selectors:
  score_query_menu:
    - "text=当次成绩查询"
    - "text=成绩查询"           # 添加备选文本
    - "text*=成绩"              # 部分匹配
    - "//a[contains(text(), '成绩')]"  # 更宽泛的XPath

# 2. 增加页面等待时间
timeouts:
  page_timeout: 60000
  element_timeout: 20000
```

**错误**: `页面验证失败，未找到预期标识`

**解决方案**:
```python
# 1. 检查页面内容
# 在日志中查看实际页面内容

# 2. 更新验证标识
# 根据实际页面内容调整验证关键词

# 3. 手动检查页面
# 使用浏览器开发者工具检查页面结构
```

#### 7.1.3 截图相关错误

**错误**: `未找到成绩表格`

**解决方案**:
```yaml
# 1. 更新表格选择器 (config.yaml)
selectors:
  score_table:
    - "table"
    - ".score-table"
    - "#scoreTable"
    - "//table[contains(@class, 'score')]"
    - "//table[.//td[contains(text(), '成绩')]]"

# 2. 降低表格识别阈值
screenshot:
  table_score_threshold: 50  # 降低评分要求
```

**错误**: `截图文件保存失败`

**解决方案**:
```bash
# 1. 检查目录权限
chmod 755 output/screenshots/

# 2. 检查磁盘空间
df -h output/

# 3. 检查文件名合法性
# 确保学员姓名不包含特殊字符
```

### 7.2 日志分析方法

#### 7.2.1 日志文件结构

```bash
# 主要日志文件
output/logs/auto_query.log     # 详细操作日志
output/logs/errors.log         # 错误汇总日志
output/logs/statistics.log     # 统计信息日志
```

#### 7.2.2 日志级别说明

| 级别 | 标识 | 含义 | 示例 |
|------|------|------|------|
| **INFO** | `INFO` | 一般信息 | `[登录] 开始登录流程` |
| **SUCCESS** | `SUCCESS` | 成功操作 | `[登录] 登录成功` |
| **WARNING** | `WARNING` | 警告信息 | `[验证码] 识别可信度较低` |
| **ERROR** | `ERROR` | 错误信息 | `[登录] 登录失败` |

#### 7.2.3 日志分析技巧

```bash
# 1. 查看特定学员的处理日志
grep "何勇" output/logs/auto_query.log

# 2. 查看所有错误信息
grep "ERROR" output/logs/auto_query.log

# 3. 统计成功率
grep -c "学员处理成功" output/logs/auto_query.log
grep -c "学员处理失败" output/logs/auto_query.log

# 4. 查看验证码识别情况
grep "验证码识别" output/logs/auto_query.log

# 5. 查看最近的处理记录
tail -100 output/logs/auto_query.log
```

### 7.3 验证码识别问题

#### 7.3.1 识别失败诊断

```bash
# 1. 检查验证码调试图片
ls -la output/logs/captcha_debug/

# 2. 查看识别日志
grep "验证码识别" output/logs/auto_query.log

# 3. 检查模型文件
ls -la models/captcha_model.onnx
```

#### 7.3.2 提高识别成功率

```yaml
# config.yaml - 调整验证码相关配置
captcha:
  retries: 5                 # 增加重试次数
  confidence_threshold: 0.7  # 降低可信度要求
  debug: true               # 启用调试模式保存图片
```

#### 7.3.3 手动验证码处理

如果自动识别持续失败，可以考虑：

1. **更新识别模型**: 联系开发者获取新模型
2. **手动介入**: 在关键时刻暂停自动化，手动输入
3. **调整策略**: 增加重试次数和等待时间

### 7.4 网络连接问题

#### 7.4.1 连接超时诊断

```bash
# 1. 测试网络连接
ping 121.204.170.198

# 2. 测试HTTP连接
curl -I https://121.204.170.198:8082/

# 3. 检查DNS解析
nslookup 121.204.170.198
```

#### 7.4.2 网络配置优化

```yaml
# config.yaml - 网络相关配置
timeouts:
  page_timeout: 60000        # 增加页面超时
  navigation_timeout: 45000  # 增加导航超时
  element_timeout: 20000     # 增加元素超时

retry:
  max_retries: 5             # 增加重试次数
  retry_delay: 5.0           # 增加重试间隔
```

### 7.5 性能优化建议

#### 7.5.1 系统资源优化

```yaml
# config.yaml - 性能优化配置
browser:
  headless: true             # 启用无头模式节省资源
  disable_images: true       # 禁用图片加载
  disable_javascript: false  # 保持JS启用（必需）

performance:
  concurrent_limit: 1        # 限制并发数量
  memory_limit: "2GB"        # 内存限制
```

#### 7.5.2 批量处理优化

```python
# 分批处理策略
def process_in_batches(students, batch_size=10):
    """分批处理学员，避免内存溢出"""
    for i in range(0, len(students), batch_size):
        batch = students[i:i + batch_size]
        yield batch
```

---

## 8. 维护和更新

### 8.1 配置文件维护

#### 8.1.1 选择器更新流程

当网站结构发生变化时，需要更新选择器配置：

```bash
# 1. 备份当前配置
cp config.yaml config.yaml.backup

# 2. 使用浏览器开发者工具检查新的页面结构
# F12 -> Elements -> 查找目标元素

# 3. 更新选择器配置
```

```yaml
# config.yaml - 选择器更新示例
selectors:
  score_query_menu:
    # 保留原有选择器作为备选
    - "text=当次成绩查询"
    - "//a[contains(text(), '当次成绩查询')]"

    # 添加新的选择器
    - "text=成绩查询入口"           # 新的文本
    - "//button[@class='new-btn']"  # 新的按钮样式
    - "[data-action='score-query']" # 新的数据属性
```

#### 8.1.2 配置测试方法

```bash
# 1. 语法验证
python -c "import yaml; yaml.safe_load(open('config.yaml'))"

# 2. 配置加载测试
python -c "
import sys
sys.path.append('src')
from config import CONFIG
print('配置加载成功')
print('选择器数量:', len(CONFIG['selectors']['score_query_menu']))
"

# 3. 单学员测试
# 修改students.csv只包含一个学员进行测试
```

### 8.2 选择器失效处理

#### 8.2.1 快速诊断步骤

```bash
# 1. 运行程序查看错误信息
python src/main.py 2>&1 | grep "未找到"

# 2. 检查页面源码
# 程序会在output/screenshots/目录生成page_source.html

# 3. 手动验证页面
# 使用浏览器访问登录页面，检查元素结构
```

#### 8.2.2 选择器更新策略

```yaml
# 渐进式更新策略
selectors:
  score_query_menu:
    # 第一层：精确匹配（优先级最高）
    - "text=当次成绩查询"
    - "#scoreQueryBtn"

    # 第二层：部分匹配（中等优先级）
    - "text*=成绩查询"
    - "[title*='成绩查询']"

    # 第三层：模糊匹配（兜底策略）
    - "//a[contains(text(), '成绩')]"
    - "//button[contains(@class, 'query')]"
    - "//*[contains(text(), '查询')]"
```

#### 8.2.3 选择器测试工具

```python
# 选择器测试脚本 (test_selectors.py)
import asyncio
from playwright.async_api import async_playwright

async def test_selectors():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()

        # 导航到目标页面
        await page.goto("https://121.204.170.198:8082/...")

        # 测试选择器
        selectors = [
            "text=当次成绩查询",
            "//a[contains(text(), '当次成绩查询')]",
            "[title*='成绩查询']"
        ]

        for selector in selectors:
            try:
                element = await page.wait_for_selector(selector, timeout=5000)
                if element:
                    print(f"✅ 选择器有效: {selector}")
                else:
                    print(f"❌ 选择器无效: {selector}")
            except:
                print(f"❌ 选择器超时: {selector}")

        await browser.close()

# 运行测试
asyncio.run(test_selectors())
```

### 8.3 系统升级指南

#### 8.3.1 依赖包升级

```bash
# 1. 检查当前版本
pip list | grep playwright
pip list | grep pyyaml

# 2. 升级核心依赖
pip install --upgrade playwright
pip install --upgrade pyyaml
pip install --upgrade pillow

# 3. 重新安装浏览器驱动
playwright install chromium

# 4. 测试升级后的功能
python src/main.py
```

#### 8.3.2 兼容性检查

```python
# 版本兼容性检查脚本
import sys
import pkg_resources

def check_compatibility():
    """检查依赖包版本兼容性"""
    requirements = {
        'playwright': '>=1.35.0',
        'pyyaml': '>=6.0',
        'pillow': '>=9.0.0',
        'opencv-python': '>=4.5.0'
    }

    for package, version_req in requirements.items():
        try:
            installed = pkg_resources.get_distribution(package)
            print(f"✅ {package}: {installed.version}")
        except pkg_resources.DistributionNotFound:
            print(f"❌ {package}: 未安装")
        except Exception as e:
            print(f"⚠️  {package}: 检查失败 - {e}")

if __name__ == "__main__":
    print("Python版本:", sys.version)
    print("\n依赖包检查:")
    check_compatibility()
```

#### 8.3.3 配置迁移

```bash
# 升级时的配置迁移步骤

# 1. 备份现有配置
cp config.yaml config.yaml.v1.backup
cp config.json config.json.v1.backup

# 2. 检查新版本的配置格式
diff config.yaml.example config.yaml

# 3. 合并配置（手动）
# 将自定义配置合并到新的配置模板中

# 4. 验证配置
python -c "
import sys
sys.path.append('src')
from config import CONFIG
print('配置验证通过')
"
```

### 8.4 备份和恢复

#### 8.4.1 备份策略

```bash
# 1. 创建备份脚本 (backup.sh)
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backup_$DATE"

mkdir -p $BACKUP_DIR

# 备份配置文件
cp config.yaml $BACKUP_DIR/
cp config.json $BACKUP_DIR/
cp data/students.csv $BACKUP_DIR/

# 备份输出文件
cp -r output/ $BACKUP_DIR/

# 备份进度文件
cp progress.json $BACKUP_DIR/ 2>/dev/null || true

echo "备份完成: $BACKUP_DIR"
```

#### 8.4.2 恢复流程

```bash
# 1. 恢复配置文件
cp backup_20250724_101500/config.yaml ./
cp backup_20250724_101500/config.json ./

# 2. 恢复学员数据
cp backup_20250724_101500/students.csv data/

# 3. 恢复进度文件（如果需要继续处理）
cp backup_20250724_101500/progress.json ./

# 4. 验证恢复
python src/main.py --dry-run  # 如果支持干运行模式
```

#### 8.4.3 定期维护任务

```bash
# 创建维护脚本 (maintenance.sh)
#!/bin/bash

echo "开始系统维护..."

# 1. 清理旧日志（保留最近30天）
find output/logs/ -name "*.log" -mtime +30 -delete
find output/logs/captcha_debug/ -name "*.png" -mtime +7 -delete

# 2. 清理临时文件
rm -f output/screenshots/page_source.html
rm -f *.tmp

# 3. 压缩旧的截图文件
find output/screenshots/ -name "*.png" -mtime +7 -exec gzip {} \;

# 4. 检查磁盘空间
df -h output/

# 5. 验证配置文件
python -c "
import yaml
with open('config.yaml') as f:
    yaml.safe_load(f)
print('配置文件验证通过')
"

echo "维护完成"
```

### 8.5 监控和告警

#### 8.5.1 性能监控

```python
# 性能监控脚本 (monitor.py)
import psutil
import time
import json
from datetime import datetime

def monitor_system():
    """监控系统资源使用情况"""
    stats = {
        'timestamp': datetime.now().isoformat(),
        'cpu_percent': psutil.cpu_percent(interval=1),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_usage': psutil.disk_usage('.').percent,
        'network_io': psutil.net_io_counters()._asdict()
    }

    # 保存监控数据
    with open('output/logs/system_monitor.json', 'a') as f:
        f.write(json.dumps(stats) + '\n')

    # 检查告警条件
    if stats['memory_percent'] > 80:
        print(f"⚠️  内存使用率过高: {stats['memory_percent']:.1f}%")

    if stats['disk_usage'] > 90:
        print(f"⚠️  磁盘使用率过高: {stats['disk_usage']:.1f}%")

if __name__ == "__main__":
    monitor_system()
```

#### 8.5.2 日志监控

```bash
# 日志监控脚本 (log_monitor.sh)
#!/bin/bash

LOG_FILE="output/logs/auto_query.log"
ERROR_COUNT=$(grep -c "ERROR" $LOG_FILE)
SUCCESS_COUNT=$(grep -c "学员处理成功" $LOG_FILE)

echo "错误数量: $ERROR_COUNT"
echo "成功数量: $SUCCESS_COUNT"

# 如果错误率过高，发送告警
if [ $ERROR_COUNT -gt 5 ]; then
    echo "⚠️  错误数量过多，请检查系统状态"
fi
```

---

## 附录

### A. 常用命令速查

```bash
# 启动程序
python src/main.py

# 检查配置
python -c "import sys; sys.path.append('src'); from config import CONFIG; print('OK')"

# 查看日志
tail -f output/logs/auto_query.log

# 清理输出
rm -rf output/screenshots/*.png progress.json

# 备份数据
cp -r output/ backup_$(date +%Y%m%d)/

# 测试网络
ping 121.204.170.198
```

### B. 配置模板

```yaml
# config.yaml 完整模板
browser:
  headless: false
  viewport_width: 1280
  viewport_height: 720

timeouts:
  page_timeout: 30000
  navigation_timeout: 20000
  element_timeout: 10000

retry:
  max_retries: 3
  retry_delay: 2.0
  captcha_retries: 3

directories:
  screenshot_dir: "output/screenshots"
  log_dir: "output/logs"
  data_file: "data/students.csv"

selectors:
  score_query_menu:
    - "text=当次成绩查询"
    - "//a[contains(text(), '当次成绩查询')]"

  query_options:
    - "text=点击进入"
    - "//a[contains(text(), '点击进入')]"
```

### C. 错误代码对照表

| 错误代码 | 错误描述 | 解决方案 |
|----------|----------|----------|
| `LOGIN_001` | 用户名输入失败 | 检查选择器配置 |
| `LOGIN_002` | 密码输入失败 | 检查选择器配置 |
| `LOGIN_003` | 验证码识别失败 | 检查模型文件 |
| `NAV_001` | 菜单查找失败 | 更新选择器配置 |
| `NAV_002` | 页面验证失败 | 检查验证标识 |
| `SHOT_001` | 表格定位失败 | 更新表格选择器 |
| `SHOT_002` | 截图保存失败 | 检查目录权限 |
| `NET_001` | 网络连接超时 | 检查网络配置 |
| `SYS_001` | 系统资源不足 | 优化性能配置 |

### D. 技术支持

#### 联系方式
- **技术文档**: 本文档
- **问题反馈**: 通过项目仓库提交Issue
- **更新通知**: 关注项目仓库Release

#### 常见问题FAQ
1. **Q: 验证码识别率低怎么办？**
   A: 检查模型文件完整性，增加重试次数，联系开发者获取新模型

2. **Q: 程序运行缓慢怎么办？**
   A: 启用无头模式，增加超时时间，检查网络连接

3. **Q: 截图文件为空怎么办？**
   A: 检查表格选择器配置，验证页面结构变化

4. **Q: 如何批量处理大量学员？**
   A: 分批处理，使用进度恢复功能，监控系统资源

---

**文档版本**: v1.0
**最后更新**: 2025-07-24
**适用系统版本**: v2.0+

> 本文档提供了自考成绩查询自动化系统的完整技术指南。如有疑问或建议，请通过项目仓库联系开发团队。
